{"id": "05e677002f164e8a65611e650bb1ca7f", "_format": "hh-sol-build-info-1", "solcVersion": "0.8.22", "solcLongVersion": "0.8.22+commit.4fc1097e", "input": {"language": "Solidity", "sources": {"@openzeppelin/contracts/access/AccessControl.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.3.0) (access/AccessControl.sol)\n\npragma solidity ^0.8.20;\n\nimport {IAccessControl} from \"./IAccessControl.sol\";\nimport {Context} from \"../utils/Context.sol\";\nimport {ERC165} from \"../utils/introspection/ERC165.sol\";\n\n/**\n * @dev Contract module that allows children to implement role-based access\n * control mechanisms. This is a lightweight version that doesn't allow enumerating role\n * members except through off-chain means by accessing the contract event logs. Some\n * applications may benefit from on-chain enumerability, for those cases see\n * {AccessControlEnumerable}.\n *\n * Roles are referred to by their `bytes32` identifier. These should be exposed\n * in the external API and be unique. The best way to achieve this is by\n * using `public constant` hash digests:\n *\n * ```solidity\n * bytes32 public constant MY_ROLE = keccak256(\"MY_ROLE\");\n * ```\n *\n * Roles can be used to represent a set of permissions. To restrict access to a\n * function call, use {hasRole}:\n *\n * ```solidity\n * function foo() public {\n *     require(hasRole(<PERSON><PERSON>_<PERSON><PERSON><PERSON>, msg.sender));\n *     ...\n * }\n * ```\n *\n * Roles can be granted and revoked dynamically via the {grantRole} and\n * {revokeRole} functions. Each role has an associated admin role, and only\n * accounts that have a role's admin role can call {grantRole} and {revokeRole}.\n *\n * By default, the admin role for all roles is `DEFAULT_ADMIN_ROLE`, which means\n * that only accounts with this role will be able to grant or revoke other\n * roles. More complex role relationships can be created by using\n * {_setRoleAdmin}.\n *\n * WARNING: The `DEFAULT_ADMIN_ROLE` is also its own admin: it has permission to\n * grant and revoke this role. Extra precautions should be taken to secure\n * accounts that have been granted it. We recommend using {AccessControlDefaultAdminRules}\n * to enforce additional security measures for this role.\n */\nabstract contract AccessControl is Context, IAccessControl, ERC165 {\n    struct RoleData {\n        mapping(address account => bool) hasRole;\n        bytes32 adminRole;\n    }\n\n    mapping(bytes32 role => RoleData) private _roles;\n\n    bytes32 public constant DEFAULT_ADMIN_ROLE = 0x00;\n\n    /**\n     * @dev Modifier that checks that an account has a specific role. Reverts\n     * with an {AccessControlUnauthorizedAccount} error including the required role.\n     */\n    modifier onlyRole(bytes32 role) {\n        _checkRole(role);\n        _;\n    }\n\n    /**\n     * @dev See {IERC165-supportsInterface}.\n     */\n    function supportsInterface(bytes4 interfaceId) public view virtual override returns (bool) {\n        return interfaceId == type(IAccessControl).interfaceId || super.supportsInterface(interfaceId);\n    }\n\n    /**\n     * @dev Returns `true` if `account` has been granted `role`.\n     */\n    function hasRole(bytes32 role, address account) public view virtual returns (bool) {\n        return _roles[role].hasRole[account];\n    }\n\n    /**\n     * @dev Reverts with an {AccessControlUnauthorizedAccount} error if `_msgSender()`\n     * is missing `role`. Overriding this function changes the behavior of the {onlyRole} modifier.\n     */\n    function _checkRole(bytes32 role) internal view virtual {\n        _checkRole(role, _msgSender());\n    }\n\n    /**\n     * @dev Reverts with an {AccessControlUnauthorizedAccount} error if `account`\n     * is missing `role`.\n     */\n    function _checkRole(bytes32 role, address account) internal view virtual {\n        if (!hasRole(role, account)) {\n            revert AccessControlUnauthorizedAccount(account, role);\n        }\n    }\n\n    /**\n     * @dev Returns the admin role that controls `role`. See {grantRole} and\n     * {revokeRole}.\n     *\n     * To change a role's admin, use {_setRoleAdmin}.\n     */\n    function getRoleAdmin(bytes32 role) public view virtual returns (bytes32) {\n        return _roles[role].adminRole;\n    }\n\n    /**\n     * @dev Grants `role` to `account`.\n     *\n     * If `account` had not been already granted `role`, emits a {RoleGranted}\n     * event.\n     *\n     * Requirements:\n     *\n     * - the caller must have ``role``'s admin role.\n     *\n     * May emit a {RoleGranted} event.\n     */\n    function grantRole(bytes32 role, address account) public virtual onlyRole(getRoleAdmin(role)) {\n        _grantRole(role, account);\n    }\n\n    /**\n     * @dev Revokes `role` from `account`.\n     *\n     * If `account` had been granted `role`, emits a {RoleRevoked} event.\n     *\n     * Requirements:\n     *\n     * - the caller must have ``role``'s admin role.\n     *\n     * May emit a {RoleRevoked} event.\n     */\n    function revokeRole(bytes32 role, address account) public virtual onlyRole(getRoleAdmin(role)) {\n        _revokeRole(role, account);\n    }\n\n    /**\n     * @dev Revokes `role` from the calling account.\n     *\n     * Roles are often managed via {grantRole} and {revokeRole}: this function's\n     * purpose is to provide a mechanism for accounts to lose their privileges\n     * if they are compromised (such as when a trusted device is misplaced).\n     *\n     * If the calling account had been revoked `role`, emits a {RoleRevoked}\n     * event.\n     *\n     * Requirements:\n     *\n     * - the caller must be `callerConfirmation`.\n     *\n     * May emit a {RoleRevoked} event.\n     */\n    function renounceRole(bytes32 role, address callerConfirmation) public virtual {\n        if (callerConfirmation != _msgSender()) {\n            revert AccessControlBadConfirmation();\n        }\n\n        _revokeRole(role, callerConfirmation);\n    }\n\n    /**\n     * @dev Sets `adminRole` as ``role``'s admin role.\n     *\n     * Emits a {RoleAdminChanged} event.\n     */\n    function _setRoleAdmin(bytes32 role, bytes32 adminRole) internal virtual {\n        bytes32 previousAdminRole = getRoleAdmin(role);\n        _roles[role].adminRole = adminRole;\n        emit RoleAdminChanged(role, previousAdminRole, adminRole);\n    }\n\n    /**\n     * @dev Attempts to grant `role` to `account` and returns a boolean indicating if `role` was granted.\n     *\n     * Internal function without access restriction.\n     *\n     * May emit a {RoleGranted} event.\n     */\n    function _grantRole(bytes32 role, address account) internal virtual returns (bool) {\n        if (!hasRole(role, account)) {\n            _roles[role].hasRole[account] = true;\n            emit RoleGranted(role, account, _msgSender());\n            return true;\n        } else {\n            return false;\n        }\n    }\n\n    /**\n     * @dev Attempts to revoke `role` from `account` and returns a boolean indicating if `role` was revoked.\n     *\n     * Internal function without access restriction.\n     *\n     * May emit a {RoleRevoked} event.\n     */\n    function _revokeRole(bytes32 role, address account) internal virtual returns (bool) {\n        if (hasRole(role, account)) {\n            _roles[role].hasRole[account] = false;\n            emit RoleRevoked(role, account, _msgSender());\n            return true;\n        } else {\n            return false;\n        }\n    }\n}\n"}, "@openzeppelin/contracts/access/IAccessControl.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.3.0) (access/IAccessControl.sol)\n\npragma solidity ^0.8.20;\n\n/**\n * @dev External interface of AccessControl declared to support ERC-165 detection.\n */\ninterface IAccessControl {\n    /**\n     * @dev The `account` is missing a role.\n     */\n    error AccessControlUnauthorizedAccount(address account, bytes32 neededRole);\n\n    /**\n     * @dev The caller of a function is not the expected one.\n     *\n     * NOTE: Don't confuse with {AccessControlUnauthorizedAccount}.\n     */\n    error AccessControlBadConfirmation();\n\n    /**\n     * @dev Emitted when `newAdminRole` is set as ``role``'s admin role, replacing `previousAdminRole`\n     *\n     * `DEFAULT_ADMIN_ROLE` is the starting admin for all roles, despite\n     * {RoleAdminChanged} not being emitted to signal this.\n     */\n    event RoleAdminChanged(bytes32 indexed role, bytes32 indexed previousAdminRole, bytes32 indexed newAdminRole);\n\n    /**\n     * @dev Emitted when `account` is granted `role`.\n     *\n     * `sender` is the account that originated the contract call. This account bears the admin role (for the granted role).\n     * Expected in cases where the role was granted using the internal {AccessControl-_grantRole}.\n     */\n    event RoleGranted(bytes32 indexed role, address indexed account, address indexed sender);\n\n    /**\n     * @dev Emitted when `account` is revoked `role`.\n     *\n     * `sender` is the account that originated the contract call:\n     *   - if using `revokeRole`, it is the admin role bearer\n     *   - if using `renounceRole`, it is the role bearer (i.e. `account`)\n     */\n    event RoleRevoked(bytes32 indexed role, address indexed account, address indexed sender);\n\n    /**\n     * @dev Returns `true` if `account` has been granted `role`.\n     */\n    function hasRole(bytes32 role, address account) external view returns (bool);\n\n    /**\n     * @dev Returns the admin role that controls `role`. See {grantRole} and\n     * {revokeRole}.\n     *\n     * To change a role's admin, use {AccessControl-_setRoleAdmin}.\n     */\n    function getRoleAdmin(bytes32 role) external view returns (bytes32);\n\n    /**\n     * @dev Grants `role` to `account`.\n     *\n     * If `account` had not been already granted `role`, emits a {RoleGranted}\n     * event.\n     *\n     * Requirements:\n     *\n     * - the caller must have ``role``'s admin role.\n     */\n    function grantRole(bytes32 role, address account) external;\n\n    /**\n     * @dev Revokes `role` from `account`.\n     *\n     * If `account` had been granted `role`, emits a {RoleRevoked} event.\n     *\n     * Requirements:\n     *\n     * - the caller must have ``role``'s admin role.\n     */\n    function revokeRole(bytes32 role, address account) external;\n\n    /**\n     * @dev Revokes `role` from the calling account.\n     *\n     * Roles are often managed via {grantRole} and {revokeRole}: this function's\n     * purpose is to provide a mechanism for accounts to lose their privileges\n     * if they are compromised (such as when a trusted device is misplaced).\n     *\n     * If the calling account had been granted `role`, emits a {RoleRevoked}\n     * event.\n     *\n     * Requirements:\n     *\n     * - the caller must be `callerConfirmation`.\n     */\n    function renounceRole(bytes32 role, address callerConfirmation) external;\n}\n"}, "@openzeppelin/contracts/utils/Context.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.0.1) (utils/Context.sol)\n\npragma solidity ^0.8.20;\n\n/**\n * @dev Provides information about the current execution context, including the\n * sender of the transaction and its data. While these are generally available\n * via msg.sender and msg.data, they should not be accessed in such a direct\n * manner, since when dealing with meta-transactions the account sending and\n * paying for execution may not be the actual sender (as far as an application\n * is concerned).\n *\n * This contract is only required for intermediate, library-like contracts.\n */\nabstract contract Context {\n    function _msgSender() internal view virtual returns (address) {\n        return msg.sender;\n    }\n\n    function _msgData() internal view virtual returns (bytes calldata) {\n        return msg.data;\n    }\n\n    function _contextSuffixLength() internal view virtual returns (uint256) {\n        return 0;\n    }\n}\n"}, "@openzeppelin/contracts/utils/introspection/ERC165.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.1.0) (utils/introspection/ERC165.sol)\n\npragma solidity ^0.8.20;\n\nimport {IERC165} from \"./IERC165.sol\";\n\n/**\n * @dev Implementation of the {IERC165} interface.\n *\n * Contracts that want to implement ERC-165 should inherit from this contract and override {supportsInterface} to check\n * for the additional interface id that will be supported. For example:\n *\n * ```solidity\n * function supportsInterface(bytes4 interfaceId) public view virtual override returns (bool) {\n *     return interfaceId == type(MyInterface).interfaceId || super.supportsInterface(interfaceId);\n * }\n * ```\n */\nabstract contract ERC165 is IERC165 {\n    /**\n     * @dev See {IERC165-supportsInterface}.\n     */\n    function supportsInterface(bytes4 interfaceId) public view virtual returns (bool) {\n        return interfaceId == type(IERC165).interfaceId;\n    }\n}\n"}, "@openzeppelin/contracts/utils/introspection/IERC165.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.1.0) (utils/introspection/IERC165.sol)\n\npragma solidity ^0.8.20;\n\n/**\n * @dev Interface of the ERC-165 standard, as defined in the\n * https://eips.ethereum.org/EIPS/eip-165[ERC].\n *\n * Implementers can declare support of contract interfaces, which can then be\n * queried by others ({ERC165<PERSON><PERSON><PERSON>}).\n *\n * For an implementation, see {ERC165}.\n */\ninterface IERC165 {\n    /**\n     * @dev Returns true if this contract implements the interface defined by\n     * `interfaceId`. See the corresponding\n     * https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[ERC section]\n     * to learn more about how these ids are created.\n     *\n     * This function call must use less than 30 000 gas.\n     */\n    function supportsInterface(bytes4 interfaceId) external view returns (bool);\n}\n"}, "contracts/SimpleClaimRegistry.sol": {"content": "// SPDX-License-Identifier: MIT\npragma solidity ^0.8.19;\n\nimport \"@openzeppelin/contracts/access/AccessControl.sol\";\n\n/**\n * @title SimpleClaimRegistry\n * @dev A simplified claim registry with custom claim type management\n */\ncontract SimpleClaimRegistry is AccessControl {\n    bytes32 public constant CLAIM_ISSUER_ROLE = keccak256(\"CLAIM_ISSUER_ROLE\");\n    bytes32 public constant CLAIM_VERIFIER_ROLE = keccak256(\"CLAIM_VERIFIER_ROLE\");\n\n    // Custom claim management\n    uint256 private _nextClaimTypeId;\n    \n    struct ClaimType {\n        uint256 id;\n        string name;\n        string description;\n        address creator;\n        uint256 createdAt;\n        bool active;\n    }\n    \n    // Mapping: claimTypeId => ClaimType\n    mapping(uint256 => ClaimType) public claimTypes;\n    \n    // Mapping: creator => claimTypeIds[]\n    mapping(address => uint256[]) public creatorClaimTypes;\n\n    // Claim storage\n    struct Claim {\n        uint256 claimType;\n        address issuer;\n        address subject;\n        bytes signature;\n        bytes data;\n        string uri;\n        uint256 issuedAt;\n        uint256 expiresAt;\n        bool revoked;\n    }\n\n    // Mapping: claimId => Claim\n    mapping(bytes32 => Claim) public claims;\n    \n    // Mapping: subject => claimType => claimIds[]\n    mapping(address => mapping(uint256 => bytes32[])) public claimIds;\n\n    // Events\n    event ClaimIssued(\n        address indexed subject,\n        uint256 indexed claimType,\n        bytes32 indexed claimId,\n        address issuer,\n        string uri\n    );\n\n    event ClaimRevoked(\n        address indexed subject,\n        uint256 indexed claimType,\n        bytes32 indexed claimId,\n        address issuer\n    );\n\n    event ClaimTypeCreated(\n        uint256 indexed claimTypeId,\n        string name,\n        string description,\n        address indexed creator\n    );\n\n    event ClaimTypeUpdated(\n        uint256 indexed claimTypeId,\n        string name,\n        string description,\n        bool active\n    );\n\n    constructor(address admin) {\n        _grantRole(DEFAULT_ADMIN_ROLE, admin);\n        _grantRole(CLAIM_ISSUER_ROLE, admin);\n        _grantRole(CLAIM_VERIFIER_ROLE, admin);\n        \n        // Initialize claim type counter\n        _nextClaimTypeId = 1;\n        \n        // Create default claim types for backward compatibility\n        _createClaimType(\"KYC Verification\", \"Basic identity verification through KYC process\");\n        _createClaimType(\"Accredited Investor\", \"Qualified as an accredited investor\");\n        _createClaimType(\"Jurisdiction Compliance\", \"Meets specific jurisdiction requirements\");\n        _createClaimType(\"General Qualification\", \"General investment qualification\");\n    }\n\n    /**\n     * @dev Create a new claim type\n     */\n    function createClaimType(\n        string calldata name,\n        string calldata description\n    ) external onlyRole(CLAIM_ISSUER_ROLE) returns (uint256) {\n        return _createClaimType(name, description);\n    }\n\n    /**\n     * @dev Internal function to create claim type\n     */\n    function _createClaimType(\n        string memory name,\n        string memory description\n    ) internal returns (uint256) {\n        uint256 claimTypeId = _nextClaimTypeId++;\n        \n        claimTypes[claimTypeId] = ClaimType({\n            id: claimTypeId,\n            name: name,\n            description: description,\n            creator: msg.sender,\n            createdAt: block.timestamp,\n            active: true\n        });\n        \n        creatorClaimTypes[msg.sender].push(claimTypeId);\n        \n        emit ClaimTypeCreated(claimTypeId, name, description, msg.sender);\n        return claimTypeId;\n    }\n\n    /**\n     * @dev Update claim type details\n     */\n    function updateClaimType(\n        uint256 claimTypeId,\n        string calldata name,\n        string calldata description,\n        bool active\n    ) external {\n        ClaimType storage claimType = claimTypes[claimTypeId];\n        require(claimType.id != 0, \"ClaimRegistry: claim type does not exist\");\n        require(\n            claimType.creator == msg.sender || hasRole(DEFAULT_ADMIN_ROLE, msg.sender),\n            \"ClaimRegistry: not authorized to update\"\n        );\n\n        claimType.name = name;\n        claimType.description = description;\n        claimType.active = active;\n\n        emit ClaimTypeUpdated(claimTypeId, name, description, active);\n    }\n\n    /**\n     * @dev Issue a claim for a subject\n     */\n    function issueClaim(\n        address subject,\n        uint256 claimType,\n        bytes calldata signature,\n        bytes calldata data,\n        string calldata uri,\n        uint256 expiresAt\n    ) external onlyRole(CLAIM_ISSUER_ROLE) returns (bytes32) {\n        require(claimTypes[claimType].id != 0, \"ClaimRegistry: invalid claim type\");\n        require(claimTypes[claimType].active, \"ClaimRegistry: claim type is inactive\");\n\n        bytes32 claimId = keccak256(\n            abi.encodePacked(subject, claimType, msg.sender, block.timestamp, data)\n        );\n\n        claims[claimId] = Claim({\n            claimType: claimType,\n            issuer: msg.sender,\n            subject: subject,\n            signature: signature,\n            data: data,\n            uri: uri,\n            issuedAt: block.timestamp,\n            expiresAt: expiresAt,\n            revoked: false\n        });\n\n        claimIds[subject][claimType].push(claimId);\n\n        emit ClaimIssued(subject, claimType, claimId, msg.sender, uri);\n        return claimId;\n    }\n\n    /**\n     * @dev Check if a subject has a valid claim of a specific type\n     */\n    function hasValidClaim(address subject, uint256 claimType) external view returns (bool) {\n        bytes32[] memory subjectClaimIds = claimIds[subject][claimType];\n        \n        for (uint256 i = 0; i < subjectClaimIds.length; i++) {\n            Claim memory claim = claims[subjectClaimIds[i]];\n            if (!claim.revoked && (claim.expiresAt == 0 || claim.expiresAt > block.timestamp)) {\n                return true;\n            }\n        }\n        \n        return false;\n    }\n\n    /**\n     * @dev Get all active claim types (paginated)\n     */\n    function getActiveClaimTypes(uint256 offset, uint256 limit) external view returns (ClaimType[] memory) {\n        require(limit > 0 && limit <= 100, \"ClaimRegistry: invalid limit\");\n        \n        // Count active claim types\n        uint256 activeCount = 0;\n        for (uint256 i = 1; i < _nextClaimTypeId; i++) {\n            if (claimTypes[i].active) {\n                activeCount++;\n            }\n        }\n        \n        if (offset >= activeCount) {\n            return new ClaimType[](0);\n        }\n        \n        uint256 resultLength = activeCount - offset;\n        if (resultLength > limit) {\n            resultLength = limit;\n        }\n        \n        ClaimType[] memory result = new ClaimType[](resultLength);\n        uint256 resultIndex = 0;\n        uint256 currentOffset = 0;\n        \n        for (uint256 i = 1; i < _nextClaimTypeId && resultIndex < resultLength; i++) {\n            if (claimTypes[i].active) {\n                if (currentOffset >= offset) {\n                    result[resultIndex] = claimTypes[i];\n                    resultIndex++;\n                }\n                currentOffset++;\n            }\n        }\n        \n        return result;\n    }\n\n    /**\n     * @dev Get total number of claim types\n     */\n    function getTotalClaimTypes() external view returns (uint256) {\n        return _nextClaimTypeId - 1;\n    }\n\n    /**\n     * @dev Check if claim type exists and is active\n     */\n    function isValidClaimType(uint256 claimTypeId) external view returns (bool) {\n        return claimTypes[claimTypeId].id != 0 && claimTypes[claimTypeId].active;\n    }\n\n    /**\n     * @dev Get all claim types created by an address\n     */\n    function getClaimTypesByCreator(address creator) external view returns (uint256[] memory) {\n        return creatorClaimTypes[creator];\n    }\n\n    /**\n     * @dev Get all claim IDs for a subject and claim type\n     */\n    function getClaimIds(address subject, uint256 claimType) external view returns (bytes32[] memory) {\n        return claimIds[subject][claimType];\n    }\n}\n"}}, "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata", "storageLayout"], "": ["ast"]}}}}, "output": {"sources": {"@openzeppelin/contracts/access/AccessControl.sol": {"ast": {"absolutePath": "@openzeppelin/contracts/access/AccessControl.sol", "exportedSymbols": {"AccessControl": [295], "Context": [408], "ERC165": [432], "IAccessControl": [378]}, "id": 296, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 1, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "108:24:0"}, {"absolutePath": "@openzeppelin/contracts/access/IAccessControl.sol", "file": "./IAccessControl.sol", "id": 3, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 296, "sourceUnit": 379, "src": "134:52:0", "symbolAliases": [{"foreign": {"id": 2, "name": "IAccessControl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 378, "src": "142:14:0", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"absolutePath": "@openzeppelin/contracts/utils/Context.sol", "file": "../utils/Context.sol", "id": 5, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 296, "sourceUnit": 409, "src": "187:45:0", "symbolAliases": [{"foreign": {"id": 4, "name": "Context", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 408, "src": "195:7:0", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"absolutePath": "@openzeppelin/contracts/utils/introspection/ERC165.sol", "file": "../utils/introspection/ERC165.sol", "id": 7, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 296, "sourceUnit": 433, "src": "233:57:0", "symbolAliases": [{"foreign": {"id": 6, "name": "ERC165", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 432, "src": "241:6:0", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"abstract": true, "baseContracts": [{"baseName": {"id": 9, "name": "Context", "nameLocations": ["1988:7:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 408, "src": "1988:7:0"}, "id": 10, "nodeType": "InheritanceSpecifier", "src": "1988:7:0"}, {"baseName": {"id": 11, "name": "IAccessControl", "nameLocations": ["1997:14:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 378, "src": "1997:14:0"}, "id": 12, "nodeType": "InheritanceSpecifier", "src": "1997:14:0"}, {"baseName": {"id": 13, "name": "ERC165", "nameLocations": ["2013:6:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 432, "src": "2013:6:0"}, "id": 14, "nodeType": "InheritanceSpecifier", "src": "2013:6:0"}], "canonicalName": "AccessControl", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 8, "nodeType": "StructuredDocumentation", "src": "292:1660:0", "text": " @dev Contract module that allows children to implement role-based access\n control mechanisms. This is a lightweight version that doesn't allow enumerating role\n members except through off-chain means by accessing the contract event logs. Some\n applications may benefit from on-chain enumerability, for those cases see\n {AccessControlEnumerable}.\n Roles are referred to by their `bytes32` identifier. These should be exposed\n in the external API and be unique. The best way to achieve this is by\n using `public constant` hash digests:\n ```solidity\n bytes32 public constant MY_ROLE = keccak256(\"MY_ROLE\");\n ```\n Roles can be used to represent a set of permissions. To restrict access to a\n function call, use {hasRole}:\n ```solidity\n function foo() public {\n     require(hasRole(MY_ROLE, msg.sender));\n     ...\n }\n ```\n Roles can be granted and revoked dynamically via the {grantRole} and\n {revokeRole} functions. Each role has an associated admin role, and only\n accounts that have a role's admin role can call {grantRole} and {revokeRole}.\n By default, the admin role for all roles is `DEFAULT_ADMIN_ROLE`, which means\n that only accounts with this role will be able to grant or revoke other\n roles. More complex role relationships can be created by using\n {_setRoleAdmin}.\n WARNING: The `DEFAULT_ADMIN_ROLE` is also its own admin: it has permission to\n grant and revoke this role. Extra precautions should be taken to secure\n accounts that have been granted it. We recommend using {AccessControlDefaultAdminRules}\n to enforce additional security measures for this role."}, "fullyImplemented": true, "id": 295, "linearizedBaseContracts": [295, 432, 444, 378, 408], "name": "AccessControl", "nameLocation": "1971:13:0", "nodeType": "ContractDefinition", "nodes": [{"canonicalName": "AccessControl.RoleData", "id": 21, "members": [{"constant": false, "id": 18, "mutability": "mutable", "name": "hasRole", "nameLocation": "2085:7:0", "nodeType": "VariableDeclaration", "scope": 21, "src": "2052:40:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}, "typeName": {"id": 17, "keyName": "account", "keyNameLocation": "2068:7:0", "keyType": {"id": 15, "name": "address", "nodeType": "ElementaryTypeName", "src": "2060:7:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "2052:32:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 16, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2079:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}}, "visibility": "internal"}, {"constant": false, "id": 20, "mutability": "mutable", "name": "adminRole", "nameLocation": "2110:9:0", "nodeType": "VariableDeclaration", "scope": 21, "src": "2102:17:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 19, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "2102:7:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "name": "RoleData", "nameLocation": "2033:8:0", "nodeType": "StructDefinition", "scope": 295, "src": "2026:100:0", "visibility": "public"}, {"constant": false, "id": 26, "mutability": "mutable", "name": "_roles", "nameLocation": "2174:6:0", "nodeType": "VariableDeclaration", "scope": 295, "src": "2132:48:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_struct$_RoleData_$21_storage_$", "typeString": "mapping(bytes32 => struct AccessControl.RoleData)"}, "typeName": {"id": 25, "keyName": "role", "keyNameLocation": "2148:4:0", "keyType": {"id": 22, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "2140:7:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "Mapping", "src": "2132:33:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_struct$_RoleData_$21_storage_$", "typeString": "mapping(bytes32 => struct AccessControl.RoleData)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 24, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 23, "name": "RoleData", "nameLocations": ["2156:8:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 21, "src": "2156:8:0"}, "referencedDeclaration": 21, "src": "2156:8:0", "typeDescriptions": {"typeIdentifier": "t_struct$_RoleData_$21_storage_ptr", "typeString": "struct AccessControl.RoleData"}}}, "visibility": "private"}, {"constant": true, "functionSelector": "a217fddf", "id": 29, "mutability": "constant", "name": "DEFAULT_ADMIN_ROLE", "nameLocation": "2211:18:0", "nodeType": "VariableDeclaration", "scope": 295, "src": "2187:49:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 27, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "2187:7:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "value": {"hexValue": "30783030", "id": 28, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2232:4:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0x00"}, "visibility": "public"}, {"body": {"id": 39, "nodeType": "Block", "src": "2454:44:0", "statements": [{"expression": {"arguments": [{"id": 35, "name": "role", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 32, "src": "2475:4:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 34, "name": "_checkRole", "nodeType": "Identifier", "overloadedDeclarations": [93, 114], "referencedDeclaration": 93, "src": "2464:10:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_bytes32_$returns$__$", "typeString": "function (bytes32) view"}}, "id": 36, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2464:16:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 37, "nodeType": "ExpressionStatement", "src": "2464:16:0"}, {"id": 38, "nodeType": "PlaceholderStatement", "src": "2490:1:0"}]}, "documentation": {"id": 30, "nodeType": "StructuredDocumentation", "src": "2243:174:0", "text": " @dev Modifier that checks that an account has a specific role. Reverts\n with an {AccessControlUnauthorizedAccount} error including the required role."}, "id": 40, "name": "only<PERSON><PERSON>", "nameLocation": "2431:8:0", "nodeType": "ModifierDefinition", "parameters": {"id": 33, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 32, "mutability": "mutable", "name": "role", "nameLocation": "2448:4:0", "nodeType": "VariableDeclaration", "scope": 40, "src": "2440:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 31, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "2440:7:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "2439:14:0"}, "src": "2422:76:0", "virtual": false, "visibility": "internal"}, {"baseFunctions": [431], "body": {"id": 61, "nodeType": "Block", "src": "2656:111:0", "statements": [{"expression": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 59, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}, "id": 54, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 49, "name": "interfaceId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "2673:11:0", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"arguments": [{"id": 51, "name": "IAccessControl", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 378, "src": "2693:14:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IAccessControl_$378_$", "typeString": "type(contract IAccessControl)"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_type$_t_contract$_IAccessControl_$378_$", "typeString": "type(contract IAccessControl)"}], "id": 50, "name": "type", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -27, "src": "2688:4:0", "typeDescriptions": {"typeIdentifier": "t_function_metatype_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 52, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2688:20:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_magic_meta_type_t_contract$_IAccessControl_$378", "typeString": "type(contract IAccessControl)"}}, "id": 53, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "2709:11:0", "memberName": "interfaceId", "nodeType": "MemberAccess", "src": "2688:32:0", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}}, "src": "2673:47:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "||", "rightExpression": {"arguments": [{"id": 57, "name": "interfaceId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "2748:11:0", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes4", "typeString": "bytes4"}], "expression": {"id": 55, "name": "super", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -25, "src": "2724:5:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_super$_AccessControl_$295_$", "typeString": "type(contract super AccessControl)"}}, "id": 56, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2730:17:0", "memberName": "supportsInterface", "nodeType": "MemberAccess", "referencedDeclaration": 431, "src": "2724:23:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_bytes4_$returns$_t_bool_$", "typeString": "function (bytes4) view returns (bool)"}}, "id": 58, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2724:36:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "2673:87:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 48, "id": 60, "nodeType": "Return", "src": "2666:94:0"}]}, "documentation": {"id": 41, "nodeType": "StructuredDocumentation", "src": "2504:56:0", "text": " @dev See {IERC165-supportsInterface}."}, "functionSelector": "01ffc9a7", "id": 62, "implemented": true, "kind": "function", "modifiers": [], "name": "supportsInterface", "nameLocation": "2574:17:0", "nodeType": "FunctionDefinition", "overrides": {"id": 45, "nodeType": "OverrideSpecifier", "overrides": [], "src": "2632:8:0"}, "parameters": {"id": 44, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 43, "mutability": "mutable", "name": "interfaceId", "nameLocation": "2599:11:0", "nodeType": "VariableDeclaration", "scope": 62, "src": "2592:18:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}, "typeName": {"id": 42, "name": "bytes4", "nodeType": "ElementaryTypeName", "src": "2592:6:0", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}}, "visibility": "internal"}], "src": "2591:20:0"}, "returnParameters": {"id": 48, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 47, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 62, "src": "2650:4:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 46, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2650:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "2649:6:0"}, "scope": 295, "src": "2565:202:0", "stateMutability": "view", "virtual": true, "visibility": "public"}, {"baseFunctions": [345], "body": {"id": 79, "nodeType": "Block", "src": "2937:53:0", "statements": [{"expression": {"baseExpression": {"expression": {"baseExpression": {"id": 72, "name": "_roles", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 26, "src": "2954:6:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_struct$_RoleData_$21_storage_$", "typeString": "mapping(bytes32 => struct AccessControl.RoleData storage ref)"}}, "id": 74, "indexExpression": {"id": 73, "name": "role", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 65, "src": "2961:4:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2954:12:0", "typeDescriptions": {"typeIdentifier": "t_struct$_RoleData_$21_storage", "typeString": "struct AccessControl.RoleData storage ref"}}, "id": 75, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "2967:7:0", "memberName": "hasRole", "nodeType": "MemberAccess", "referencedDeclaration": 18, "src": "2954:20:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 77, "indexExpression": {"id": 76, "name": "account", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 67, "src": "2975:7:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2954:29:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 71, "id": 78, "nodeType": "Return", "src": "2947:36:0"}]}, "documentation": {"id": 63, "nodeType": "StructuredDocumentation", "src": "2773:76:0", "text": " @dev Returns `true` if `account` has been granted `role`."}, "functionSelector": "91d14854", "id": 80, "implemented": true, "kind": "function", "modifiers": [], "name": "hasRole", "nameLocation": "2863:7:0", "nodeType": "FunctionDefinition", "parameters": {"id": 68, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 65, "mutability": "mutable", "name": "role", "nameLocation": "2879:4:0", "nodeType": "VariableDeclaration", "scope": 80, "src": "2871:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 64, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "2871:7:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 67, "mutability": "mutable", "name": "account", "nameLocation": "2893:7:0", "nodeType": "VariableDeclaration", "scope": 80, "src": "2885:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 66, "name": "address", "nodeType": "ElementaryTypeName", "src": "2885:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2870:31:0"}, "returnParameters": {"id": 71, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 70, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 80, "src": "2931:4:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 69, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2931:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "2930:6:0"}, "scope": 295, "src": "2854:136:0", "stateMutability": "view", "virtual": true, "visibility": "public"}, {"body": {"id": 92, "nodeType": "Block", "src": "3255:47:0", "statements": [{"expression": {"arguments": [{"id": 87, "name": "role", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 83, "src": "3276:4:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"arguments": [], "expression": {"argumentTypes": [], "id": 88, "name": "_msgSender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 390, "src": "3282:10:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 89, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3282:12:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 86, "name": "_checkRole", "nodeType": "Identifier", "overloadedDeclarations": [93, 114], "referencedDeclaration": 114, "src": "3265:10:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_bytes32_$_t_address_$returns$__$", "typeString": "function (bytes32,address) view"}}, "id": 90, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3265:30:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 91, "nodeType": "ExpressionStatement", "src": "3265:30:0"}]}, "documentation": {"id": 81, "nodeType": "StructuredDocumentation", "src": "2996:198:0", "text": " @dev Reverts with an {AccessControlUnauthorizedAccount} error if `_msgSender()`\n is missing `role`. Overriding this function changes the behavior of the {onlyRole} modifier."}, "id": 93, "implemented": true, "kind": "function", "modifiers": [], "name": "_checkRole", "nameLocation": "3208:10:0", "nodeType": "FunctionDefinition", "parameters": {"id": 84, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 83, "mutability": "mutable", "name": "role", "nameLocation": "3227:4:0", "nodeType": "VariableDeclaration", "scope": 93, "src": "3219:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 82, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "3219:7:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "3218:14:0"}, "returnParameters": {"id": 85, "nodeType": "ParameterList", "parameters": [], "src": "3255:0:0"}, "scope": 295, "src": "3199:103:0", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"body": {"id": 113, "nodeType": "Block", "src": "3505:124:0", "statements": [{"condition": {"id": 105, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "3519:23:0", "subExpression": {"arguments": [{"id": 102, "name": "role", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 96, "src": "3528:4:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 103, "name": "account", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 98, "src": "3534:7:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 101, "name": "hasRole", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 80, "src": "3520:7:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_bytes32_$_t_address_$returns$_t_bool_$", "typeString": "function (bytes32,address) view returns (bool)"}}, "id": 104, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3520:22:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 112, "nodeType": "IfStatement", "src": "3515:108:0", "trueBody": {"id": 111, "nodeType": "Block", "src": "3544:79:0", "statements": [{"errorCall": {"arguments": [{"id": 107, "name": "account", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 98, "src": "3598:7:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 108, "name": "role", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 96, "src": "3607:4:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 106, "name": "AccessControlUnauthorizedAccount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 305, "src": "3565:32:0", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$_t_bytes32_$returns$__$", "typeString": "function (address,bytes32) pure"}}, "id": 109, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3565:47:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 110, "nodeType": "RevertStatement", "src": "3558:54:0"}]}}]}, "documentation": {"id": 94, "nodeType": "StructuredDocumentation", "src": "3308:119:0", "text": " @dev Reverts with an {AccessControlUnauthorizedAccount} error if `account`\n is missing `role`."}, "id": 114, "implemented": true, "kind": "function", "modifiers": [], "name": "_checkRole", "nameLocation": "3441:10:0", "nodeType": "FunctionDefinition", "parameters": {"id": 99, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 96, "mutability": "mutable", "name": "role", "nameLocation": "3460:4:0", "nodeType": "VariableDeclaration", "scope": 114, "src": "3452:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 95, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "3452:7:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 98, "mutability": "mutable", "name": "account", "nameLocation": "3474:7:0", "nodeType": "VariableDeclaration", "scope": 114, "src": "3466:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 97, "name": "address", "nodeType": "ElementaryTypeName", "src": "3466:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "3451:31:0"}, "returnParameters": {"id": 100, "nodeType": "ParameterList", "parameters": [], "src": "3505:0:0"}, "scope": 295, "src": "3432:197:0", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"baseFunctions": [353], "body": {"id": 127, "nodeType": "Block", "src": "3884:46:0", "statements": [{"expression": {"expression": {"baseExpression": {"id": 122, "name": "_roles", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 26, "src": "3901:6:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_struct$_RoleData_$21_storage_$", "typeString": "mapping(bytes32 => struct AccessControl.RoleData storage ref)"}}, "id": 124, "indexExpression": {"id": 123, "name": "role", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 117, "src": "3908:4:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3901:12:0", "typeDescriptions": {"typeIdentifier": "t_struct$_RoleData_$21_storage", "typeString": "struct AccessControl.RoleData storage ref"}}, "id": 125, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "3914:9:0", "memberName": "adminRole", "nodeType": "MemberAccess", "referencedDeclaration": 20, "src": "3901:22:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "functionReturnParameters": 121, "id": 126, "nodeType": "Return", "src": "3894:29:0"}]}, "documentation": {"id": 115, "nodeType": "StructuredDocumentation", "src": "3635:170:0", "text": " @dev Returns the admin role that controls `role`. See {grantRole} and\n {revokeRole}.\n To change a role's admin, use {_setRoleAdmin}."}, "functionSelector": "248a9ca3", "id": 128, "implemented": true, "kind": "function", "modifiers": [], "name": "getRoleAdmin", "nameLocation": "3819:12:0", "nodeType": "FunctionDefinition", "parameters": {"id": 118, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 117, "mutability": "mutable", "name": "role", "nameLocation": "3840:4:0", "nodeType": "VariableDeclaration", "scope": 128, "src": "3832:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 116, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "3832:7:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "3831:14:0"}, "returnParameters": {"id": 121, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 120, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 128, "src": "3875:7:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 119, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "3875:7:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "3874:9:0"}, "scope": 295, "src": "3810:120:0", "stateMutability": "view", "virtual": true, "visibility": "public"}, {"baseFunctions": [361], "body": {"id": 146, "nodeType": "Block", "src": "4320:42:0", "statements": [{"expression": {"arguments": [{"id": 142, "name": "role", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 131, "src": "4341:4:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 143, "name": "account", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 133, "src": "4347:7:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 141, "name": "_grantRole", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 256, "src": "4330:10:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_bytes32_$_t_address_$returns$_t_bool_$", "typeString": "function (bytes32,address) returns (bool)"}}, "id": 144, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4330:25:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 145, "nodeType": "ExpressionStatement", "src": "4330:25:0"}]}, "documentation": {"id": 129, "nodeType": "StructuredDocumentation", "src": "3936:285:0", "text": " @dev Grants `role` to `account`.\n If `account` had not been already granted `role`, emits a {RoleGranted}\n event.\n Requirements:\n - the caller must have ``role``'s admin role.\n May emit a {RoleGranted} event."}, "functionSelector": "2f2ff15d", "id": 147, "implemented": true, "kind": "function", "modifiers": [{"arguments": [{"arguments": [{"id": 137, "name": "role", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 131, "src": "4313:4:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 136, "name": "getRoleAdmin", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 128, "src": "4300:12:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_bytes32_$returns$_t_bytes32_$", "typeString": "function (bytes32) view returns (bytes32)"}}, "id": 138, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4300:18:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "id": 139, "kind": "modifierInvocation", "modifierName": {"id": 135, "name": "only<PERSON><PERSON>", "nameLocations": ["4291:8:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 40, "src": "4291:8:0"}, "nodeType": "ModifierInvocation", "src": "4291:28:0"}], "name": "grantRole", "nameLocation": "4235:9:0", "nodeType": "FunctionDefinition", "parameters": {"id": 134, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 131, "mutability": "mutable", "name": "role", "nameLocation": "4253:4:0", "nodeType": "VariableDeclaration", "scope": 147, "src": "4245:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 130, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "4245:7:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 133, "mutability": "mutable", "name": "account", "nameLocation": "4267:7:0", "nodeType": "VariableDeclaration", "scope": 147, "src": "4259:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 132, "name": "address", "nodeType": "ElementaryTypeName", "src": "4259:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "4244:31:0"}, "returnParameters": {"id": 140, "nodeType": "ParameterList", "parameters": [], "src": "4320:0:0"}, "scope": 295, "src": "4226:136:0", "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"baseFunctions": [369], "body": {"id": 165, "nodeType": "Block", "src": "4737:43:0", "statements": [{"expression": {"arguments": [{"id": 161, "name": "role", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 150, "src": "4759:4:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 162, "name": "account", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 152, "src": "4765:7:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 160, "name": "_revokeRole", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 294, "src": "4747:11:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_bytes32_$_t_address_$returns$_t_bool_$", "typeString": "function (bytes32,address) returns (bool)"}}, "id": 163, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4747:26:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 164, "nodeType": "ExpressionStatement", "src": "4747:26:0"}]}, "documentation": {"id": 148, "nodeType": "StructuredDocumentation", "src": "4368:269:0", "text": " @dev Revokes `role` from `account`.\n If `account` had been granted `role`, emits a {RoleRevoked} event.\n Requirements:\n - the caller must have ``role``'s admin role.\n May emit a {RoleRevoked} event."}, "functionSelector": "d547741f", "id": 166, "implemented": true, "kind": "function", "modifiers": [{"arguments": [{"arguments": [{"id": 156, "name": "role", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 150, "src": "4730:4:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 155, "name": "getRoleAdmin", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 128, "src": "4717:12:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_bytes32_$returns$_t_bytes32_$", "typeString": "function (bytes32) view returns (bytes32)"}}, "id": 157, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4717:18:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "id": 158, "kind": "modifierInvocation", "modifierName": {"id": 154, "name": "only<PERSON><PERSON>", "nameLocations": ["4708:8:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 40, "src": "4708:8:0"}, "nodeType": "ModifierInvocation", "src": "4708:28:0"}], "name": "revokeRole", "nameLocation": "4651:10:0", "nodeType": "FunctionDefinition", "parameters": {"id": 153, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 150, "mutability": "mutable", "name": "role", "nameLocation": "4670:4:0", "nodeType": "VariableDeclaration", "scope": 166, "src": "4662:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 149, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "4662:7:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 152, "mutability": "mutable", "name": "account", "nameLocation": "4684:7:0", "nodeType": "VariableDeclaration", "scope": 166, "src": "4676:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 151, "name": "address", "nodeType": "ElementaryTypeName", "src": "4676:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "4661:31:0"}, "returnParameters": {"id": 159, "nodeType": "ParameterList", "parameters": [], "src": "4737:0:0"}, "scope": 295, "src": "4642:138:0", "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"baseFunctions": [377], "body": {"id": 188, "nodeType": "Block", "src": "5407:166:0", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 177, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 174, "name": "callerConfirmation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 171, "src": "5421:18:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "id": 175, "name": "_msgSender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 390, "src": "5443:10:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 176, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5443:12:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "5421:34:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 182, "nodeType": "IfStatement", "src": "5417:102:0", "trueBody": {"id": 181, "nodeType": "Block", "src": "5457:62:0", "statements": [{"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 178, "name": "AccessControlBadConfirmation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 308, "src": "5478:28:0", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 179, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5478:30:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 180, "nodeType": "RevertStatement", "src": "5471:37:0"}]}}, {"expression": {"arguments": [{"id": 184, "name": "role", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 169, "src": "5541:4:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 185, "name": "callerConfirmation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 171, "src": "5547:18:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 183, "name": "_revokeRole", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 294, "src": "5529:11:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_bytes32_$_t_address_$returns$_t_bool_$", "typeString": "function (bytes32,address) returns (bool)"}}, "id": 186, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5529:37:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 187, "nodeType": "ExpressionStatement", "src": "5529:37:0"}]}, "documentation": {"id": 167, "nodeType": "StructuredDocumentation", "src": "4786:537:0", "text": " @dev Revokes `role` from the calling account.\n Roles are often managed via {grantRole} and {revokeRole}: this function's\n purpose is to provide a mechanism for accounts to lose their privileges\n if they are compromised (such as when a trusted device is misplaced).\n If the calling account had been revoked `role`, emits a {RoleRevoked}\n event.\n Requirements:\n - the caller must be `callerConfirmation`.\n May emit a {RoleRevoked} event."}, "functionSelector": "36568abe", "id": 189, "implemented": true, "kind": "function", "modifiers": [], "name": "renounceRole", "nameLocation": "5337:12:0", "nodeType": "FunctionDefinition", "parameters": {"id": 172, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 169, "mutability": "mutable", "name": "role", "nameLocation": "5358:4:0", "nodeType": "VariableDeclaration", "scope": 189, "src": "5350:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 168, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "5350:7:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 171, "mutability": "mutable", "name": "callerConfirmation", "nameLocation": "5372:18:0", "nodeType": "VariableDeclaration", "scope": 189, "src": "5364:26:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 170, "name": "address", "nodeType": "ElementaryTypeName", "src": "5364:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "5349:42:0"}, "returnParameters": {"id": 173, "nodeType": "ParameterList", "parameters": [], "src": "5407:0:0"}, "scope": 295, "src": "5328:245:0", "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"body": {"id": 216, "nodeType": "Block", "src": "5771:174:0", "statements": [{"assignments": [198], "declarations": [{"constant": false, "id": 198, "mutability": "mutable", "name": "previousAdminRole", "nameLocation": "5789:17:0", "nodeType": "VariableDeclaration", "scope": 216, "src": "5781:25:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 197, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "5781:7:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "id": 202, "initialValue": {"arguments": [{"id": 200, "name": "role", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 192, "src": "5822:4:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 199, "name": "getRoleAdmin", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 128, "src": "5809:12:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_bytes32_$returns$_t_bytes32_$", "typeString": "function (bytes32) view returns (bytes32)"}}, "id": 201, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5809:18:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "VariableDeclarationStatement", "src": "5781:46:0"}, {"expression": {"id": 208, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"baseExpression": {"id": 203, "name": "_roles", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 26, "src": "5837:6:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_struct$_RoleData_$21_storage_$", "typeString": "mapping(bytes32 => struct AccessControl.RoleData storage ref)"}}, "id": 205, "indexExpression": {"id": 204, "name": "role", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 192, "src": "5844:4:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5837:12:0", "typeDescriptions": {"typeIdentifier": "t_struct$_RoleData_$21_storage", "typeString": "struct AccessControl.RoleData storage ref"}}, "id": 206, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "5850:9:0", "memberName": "adminRole", "nodeType": "MemberAccess", "referencedDeclaration": 20, "src": "5837:22:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 207, "name": "adminRole", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 194, "src": "5862:9:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "src": "5837:34:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 209, "nodeType": "ExpressionStatement", "src": "5837:34:0"}, {"eventCall": {"arguments": [{"id": 211, "name": "role", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 192, "src": "5903:4:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 212, "name": "previousAdminRole", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 198, "src": "5909:17:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 213, "name": "adminRole", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 194, "src": "5928:9:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "id": 210, "name": "RoleAdminChanged", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 317, "src": "5886:16:0", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_bytes32_$_t_bytes32_$_t_bytes32_$returns$__$", "typeString": "function (bytes32,bytes32,bytes32)"}}, "id": 214, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5886:52:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 215, "nodeType": "EmitStatement", "src": "5881:57:0"}]}, "documentation": {"id": 190, "nodeType": "StructuredDocumentation", "src": "5579:114:0", "text": " @dev Sets `adminRole` as ``role``'s admin role.\n Emits a {RoleAdminChanged} event."}, "id": 217, "implemented": true, "kind": "function", "modifiers": [], "name": "_setRoleAdmin", "nameLocation": "5707:13:0", "nodeType": "FunctionDefinition", "parameters": {"id": 195, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 192, "mutability": "mutable", "name": "role", "nameLocation": "5729:4:0", "nodeType": "VariableDeclaration", "scope": 217, "src": "5721:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 191, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "5721:7:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 194, "mutability": "mutable", "name": "adminRole", "nameLocation": "5743:9:0", "nodeType": "VariableDeclaration", "scope": 217, "src": "5735:17:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 193, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "5735:7:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "5720:33:0"}, "returnParameters": {"id": 196, "nodeType": "ParameterList", "parameters": [], "src": "5771:0:0"}, "scope": 295, "src": "5698:247:0", "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"body": {"id": 255, "nodeType": "Block", "src": "6262:233:0", "statements": [{"condition": {"id": 231, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "6276:23:0", "subExpression": {"arguments": [{"id": 228, "name": "role", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 220, "src": "6285:4:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 229, "name": "account", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 222, "src": "6291:7:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 227, "name": "hasRole", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 80, "src": "6277:7:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_bytes32_$_t_address_$returns$_t_bool_$", "typeString": "function (bytes32,address) view returns (bool)"}}, "id": 230, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6277:22:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"id": 253, "nodeType": "Block", "src": "6452:37:0", "statements": [{"expression": {"hexValue": "66616c7365", "id": 251, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "6473:5:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}, "functionReturnParameters": 226, "id": 252, "nodeType": "Return", "src": "6466:12:0"}]}, "id": 254, "nodeType": "IfStatement", "src": "6272:217:0", "trueBody": {"id": 250, "nodeType": "Block", "src": "6301:145:0", "statements": [{"expression": {"id": 239, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"expression": {"baseExpression": {"id": 232, "name": "_roles", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 26, "src": "6315:6:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_struct$_RoleData_$21_storage_$", "typeString": "mapping(bytes32 => struct AccessControl.RoleData storage ref)"}}, "id": 234, "indexExpression": {"id": 233, "name": "role", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 220, "src": "6322:4:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "6315:12:0", "typeDescriptions": {"typeIdentifier": "t_struct$_RoleData_$21_storage", "typeString": "struct AccessControl.RoleData storage ref"}}, "id": 235, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "6328:7:0", "memberName": "hasRole", "nodeType": "MemberAccess", "referencedDeclaration": 18, "src": "6315:20:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 237, "indexExpression": {"id": 236, "name": "account", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 222, "src": "6336:7:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "6315:29:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "********", "id": 238, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "6347:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "6315:36:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 240, "nodeType": "ExpressionStatement", "src": "6315:36:0"}, {"eventCall": {"arguments": [{"id": 242, "name": "role", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 220, "src": "6382:4:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 243, "name": "account", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 222, "src": "6388:7:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"arguments": [], "expression": {"argumentTypes": [], "id": 244, "name": "_msgSender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 390, "src": "6397:10:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 245, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6397:12:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 241, "name": "RoleGranted", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 326, "src": "6370:11:0", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_bytes32_$_t_address_$_t_address_$returns$__$", "typeString": "function (bytes32,address,address)"}}, "id": 246, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6370:40:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 247, "nodeType": "EmitStatement", "src": "6365:45:0"}, {"expression": {"hexValue": "********", "id": 248, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "6431:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 226, "id": 249, "nodeType": "Return", "src": "6424:11:0"}]}}]}, "documentation": {"id": 218, "nodeType": "StructuredDocumentation", "src": "5951:223:0", "text": " @dev Attempts to grant `role` to `account` and returns a boolean indicating if `role` was granted.\n Internal function without access restriction.\n May emit a {RoleGranted} event."}, "id": 256, "implemented": true, "kind": "function", "modifiers": [], "name": "_grantRole", "nameLocation": "6188:10:0", "nodeType": "FunctionDefinition", "parameters": {"id": 223, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 220, "mutability": "mutable", "name": "role", "nameLocation": "6207:4:0", "nodeType": "VariableDeclaration", "scope": 256, "src": "6199:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 219, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "6199:7:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 222, "mutability": "mutable", "name": "account", "nameLocation": "6221:7:0", "nodeType": "VariableDeclaration", "scope": 256, "src": "6213:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 221, "name": "address", "nodeType": "ElementaryTypeName", "src": "6213:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "6198:31:0"}, "returnParameters": {"id": 226, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 225, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 256, "src": "6256:4:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 224, "name": "bool", "nodeType": "ElementaryTypeName", "src": "6256:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "6255:6:0"}, "scope": 295, "src": "6179:316:0", "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"body": {"id": 293, "nodeType": "Block", "src": "6816:233:0", "statements": [{"condition": {"arguments": [{"id": 267, "name": "role", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 259, "src": "6838:4:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 268, "name": "account", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 261, "src": "6844:7:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 266, "name": "hasRole", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 80, "src": "6830:7:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_bytes32_$_t_address_$returns$_t_bool_$", "typeString": "function (bytes32,address) view returns (bool)"}}, "id": 269, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6830:22:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"id": 291, "nodeType": "Block", "src": "7006:37:0", "statements": [{"expression": {"hexValue": "66616c7365", "id": 289, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "7027:5:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}, "functionReturnParameters": 265, "id": 290, "nodeType": "Return", "src": "7020:12:0"}]}, "id": 292, "nodeType": "IfStatement", "src": "6826:217:0", "trueBody": {"id": 288, "nodeType": "Block", "src": "6854:146:0", "statements": [{"expression": {"id": 277, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"expression": {"baseExpression": {"id": 270, "name": "_roles", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 26, "src": "6868:6:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_struct$_RoleData_$21_storage_$", "typeString": "mapping(bytes32 => struct AccessControl.RoleData storage ref)"}}, "id": 272, "indexExpression": {"id": 271, "name": "role", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 259, "src": "6875:4:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "6868:12:0", "typeDescriptions": {"typeIdentifier": "t_struct$_RoleData_$21_storage", "typeString": "struct AccessControl.RoleData storage ref"}}, "id": 273, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "6881:7:0", "memberName": "hasRole", "nodeType": "MemberAccess", "referencedDeclaration": 18, "src": "6868:20:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 275, "indexExpression": {"id": 274, "name": "account", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 261, "src": "6889:7:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "6868:29:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "66616c7365", "id": 276, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "6900:5:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}, "src": "6868:37:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 278, "nodeType": "ExpressionStatement", "src": "6868:37:0"}, {"eventCall": {"arguments": [{"id": 280, "name": "role", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 259, "src": "6936:4:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 281, "name": "account", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 261, "src": "6942:7:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"arguments": [], "expression": {"argumentTypes": [], "id": 282, "name": "_msgSender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 390, "src": "6951:10:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 283, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6951:12:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 279, "name": "RoleRevoked", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 335, "src": "6924:11:0", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_bytes32_$_t_address_$_t_address_$returns$__$", "typeString": "function (bytes32,address,address)"}}, "id": 284, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6924:40:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 285, "nodeType": "EmitStatement", "src": "6919:45:0"}, {"expression": {"hexValue": "********", "id": 286, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "6985:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 265, "id": 287, "nodeType": "Return", "src": "6978:11:0"}]}}]}, "documentation": {"id": 257, "nodeType": "StructuredDocumentation", "src": "6501:226:0", "text": " @dev Attempts to revoke `role` from `account` and returns a boolean indicating if `role` was revoked.\n Internal function without access restriction.\n May emit a {RoleRevoked} event."}, "id": 294, "implemented": true, "kind": "function", "modifiers": [], "name": "_revokeRole", "nameLocation": "6741:11:0", "nodeType": "FunctionDefinition", "parameters": {"id": 262, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 259, "mutability": "mutable", "name": "role", "nameLocation": "6761:4:0", "nodeType": "VariableDeclaration", "scope": 294, "src": "6753:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 258, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "6753:7:0", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 261, "mutability": "mutable", "name": "account", "nameLocation": "6775:7:0", "nodeType": "VariableDeclaration", "scope": 294, "src": "6767:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 260, "name": "address", "nodeType": "ElementaryTypeName", "src": "6767:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "6752:31:0"}, "returnParameters": {"id": 265, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 264, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 294, "src": "6810:4:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 263, "name": "bool", "nodeType": "ElementaryTypeName", "src": "6810:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "6809:6:0"}, "scope": 295, "src": "6732:317:0", "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}], "scope": 296, "src": "1953:5098:0", "usedErrors": [305, 308], "usedEvents": [317, 326, 335]}], "src": "108:6944:0"}, "id": 0}, "@openzeppelin/contracts/access/IAccessControl.sol": {"ast": {"absolutePath": "@openzeppelin/contracts/access/IAccessControl.sol", "exportedSymbols": {"IAccessControl": [378]}, "id": 379, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 297, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "109:24:1"}, {"abstract": false, "baseContracts": [], "canonicalName": "IAccessControl", "contractDependencies": [], "contractKind": "interface", "documentation": {"id": 298, "nodeType": "StructuredDocumentation", "src": "135:90:1", "text": " @dev External interface of AccessControl declared to support ERC-165 detection."}, "fullyImplemented": false, "id": 378, "linearizedBaseContracts": [378], "name": "IAccessControl", "nameLocation": "236:14:1", "nodeType": "ContractDefinition", "nodes": [{"documentation": {"id": 299, "nodeType": "StructuredDocumentation", "src": "257:56:1", "text": " @dev The `account` is missing a role."}, "errorSelector": "e2517d3f", "id": 305, "name": "AccessControlUnauthorizedAccount", "nameLocation": "324:32:1", "nodeType": "ErrorDefinition", "parameters": {"id": 304, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 301, "mutability": "mutable", "name": "account", "nameLocation": "365:7:1", "nodeType": "VariableDeclaration", "scope": 305, "src": "357:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 300, "name": "address", "nodeType": "ElementaryTypeName", "src": "357:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 303, "mutability": "mutable", "name": "neededRole", "nameLocation": "382:10:1", "nodeType": "VariableDeclaration", "scope": 305, "src": "374:18:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 302, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "374:7:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "356:37:1"}, "src": "318:76:1"}, {"documentation": {"id": 306, "nodeType": "StructuredDocumentation", "src": "400:148:1", "text": " @dev The caller of a function is not the expected one.\n NOTE: Don't confuse with {AccessControlUnauthorizedAccount}."}, "errorSelector": "6697b232", "id": 308, "name": "AccessControlBadConfirmation", "nameLocation": "559:28:1", "nodeType": "ErrorDefinition", "parameters": {"id": 307, "nodeType": "ParameterList", "parameters": [], "src": "587:2:1"}, "src": "553:37:1"}, {"anonymous": false, "documentation": {"id": 309, "nodeType": "StructuredDocumentation", "src": "596:254:1", "text": " @dev Emitted when `newAdminRole` is set as ``role``'s admin role, replacing `previousAdminRole`\n `DEFAULT_ADMIN_ROLE` is the starting admin for all roles, despite\n {RoleAdminChanged} not being emitted to signal this."}, "eventSelector": "bd79b86ffe0ab8e8776151514217cd7cacd52c909f66475c3af44e129f0b00ff", "id": 317, "name": "RoleAdminChanged", "nameLocation": "861:16:1", "nodeType": "EventDefinition", "parameters": {"id": 316, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 311, "indexed": true, "mutability": "mutable", "name": "role", "nameLocation": "894:4:1", "nodeType": "VariableDeclaration", "scope": 317, "src": "878:20:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 310, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "878:7:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 313, "indexed": true, "mutability": "mutable", "name": "previousAdminRole", "nameLocation": "916:17:1", "nodeType": "VariableDeclaration", "scope": 317, "src": "900:33:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 312, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "900:7:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 315, "indexed": true, "mutability": "mutable", "name": "newAdminRole", "nameLocation": "951:12:1", "nodeType": "VariableDeclaration", "scope": 317, "src": "935:28:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 314, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "935:7:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "877:87:1"}, "src": "855:110:1"}, {"anonymous": false, "documentation": {"id": 318, "nodeType": "StructuredDocumentation", "src": "971:295:1", "text": " @dev Emitted when `account` is granted `role`.\n `sender` is the account that originated the contract call. This account bears the admin role (for the granted role).\n Expected in cases where the role was granted using the internal {AccessControl-_grantRole}."}, "eventSelector": "2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d", "id": 326, "name": "RoleGranted", "nameLocation": "1277:11:1", "nodeType": "EventDefinition", "parameters": {"id": 325, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 320, "indexed": true, "mutability": "mutable", "name": "role", "nameLocation": "1305:4:1", "nodeType": "VariableDeclaration", "scope": 326, "src": "1289:20:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 319, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "1289:7:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 322, "indexed": true, "mutability": "mutable", "name": "account", "nameLocation": "1327:7:1", "nodeType": "VariableDeclaration", "scope": 326, "src": "1311:23:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 321, "name": "address", "nodeType": "ElementaryTypeName", "src": "1311:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 324, "indexed": true, "mutability": "mutable", "name": "sender", "nameLocation": "1352:6:1", "nodeType": "VariableDeclaration", "scope": 326, "src": "1336:22:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 323, "name": "address", "nodeType": "ElementaryTypeName", "src": "1336:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1288:71:1"}, "src": "1271:89:1"}, {"anonymous": false, "documentation": {"id": 327, "nodeType": "StructuredDocumentation", "src": "1366:275:1", "text": " @dev Emitted when `account` is revoked `role`.\n `sender` is the account that originated the contract call:\n   - if using `revokeRole`, it is the admin role bearer\n   - if using `renounceRole`, it is the role bearer (i.e. `account`)"}, "eventSelector": "f6391f5c32d9c69d2a47ea670b442974b53935d1edc7fd64eb21e047a839171b", "id": 335, "name": "RoleRevoked", "nameLocation": "1652:11:1", "nodeType": "EventDefinition", "parameters": {"id": 334, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 329, "indexed": true, "mutability": "mutable", "name": "role", "nameLocation": "1680:4:1", "nodeType": "VariableDeclaration", "scope": 335, "src": "1664:20:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 328, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "1664:7:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 331, "indexed": true, "mutability": "mutable", "name": "account", "nameLocation": "1702:7:1", "nodeType": "VariableDeclaration", "scope": 335, "src": "1686:23:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 330, "name": "address", "nodeType": "ElementaryTypeName", "src": "1686:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 333, "indexed": true, "mutability": "mutable", "name": "sender", "nameLocation": "1727:6:1", "nodeType": "VariableDeclaration", "scope": 335, "src": "1711:22:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 332, "name": "address", "nodeType": "ElementaryTypeName", "src": "1711:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1663:71:1"}, "src": "1646:89:1"}, {"documentation": {"id": 336, "nodeType": "StructuredDocumentation", "src": "1741:76:1", "text": " @dev Returns `true` if `account` has been granted `role`."}, "functionSelector": "91d14854", "id": 345, "implemented": false, "kind": "function", "modifiers": [], "name": "hasRole", "nameLocation": "1831:7:1", "nodeType": "FunctionDefinition", "parameters": {"id": 341, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 338, "mutability": "mutable", "name": "role", "nameLocation": "1847:4:1", "nodeType": "VariableDeclaration", "scope": 345, "src": "1839:12:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 337, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "1839:7:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 340, "mutability": "mutable", "name": "account", "nameLocation": "1861:7:1", "nodeType": "VariableDeclaration", "scope": 345, "src": "1853:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 339, "name": "address", "nodeType": "ElementaryTypeName", "src": "1853:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1838:31:1"}, "returnParameters": {"id": 344, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 343, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 345, "src": "1893:4:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 342, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1893:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "1892:6:1"}, "scope": 378, "src": "1822:77:1", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"documentation": {"id": 346, "nodeType": "StructuredDocumentation", "src": "1905:184:1", "text": " @dev Returns the admin role that controls `role`. See {grantRole} and\n {revokeRole}.\n To change a role's admin, use {AccessControl-_setRoleAdmin}."}, "functionSelector": "248a9ca3", "id": 353, "implemented": false, "kind": "function", "modifiers": [], "name": "getRoleAdmin", "nameLocation": "2103:12:1", "nodeType": "FunctionDefinition", "parameters": {"id": 349, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 348, "mutability": "mutable", "name": "role", "nameLocation": "2124:4:1", "nodeType": "VariableDeclaration", "scope": 353, "src": "2116:12:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 347, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "2116:7:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "2115:14:1"}, "returnParameters": {"id": 352, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 351, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 353, "src": "2153:7:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 350, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "2153:7:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "2152:9:1"}, "scope": 378, "src": "2094:68:1", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"documentation": {"id": 354, "nodeType": "StructuredDocumentation", "src": "2168:239:1", "text": " @dev Grants `role` to `account`.\n If `account` had not been already granted `role`, emits a {RoleGranted}\n event.\n Requirements:\n - the caller must have ``role``'s admin role."}, "functionSelector": "2f2ff15d", "id": 361, "implemented": false, "kind": "function", "modifiers": [], "name": "grantRole", "nameLocation": "2421:9:1", "nodeType": "FunctionDefinition", "parameters": {"id": 359, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 356, "mutability": "mutable", "name": "role", "nameLocation": "2439:4:1", "nodeType": "VariableDeclaration", "scope": 361, "src": "2431:12:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 355, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "2431:7:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 358, "mutability": "mutable", "name": "account", "nameLocation": "2453:7:1", "nodeType": "VariableDeclaration", "scope": 361, "src": "2445:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 357, "name": "address", "nodeType": "ElementaryTypeName", "src": "2445:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2430:31:1"}, "returnParameters": {"id": 360, "nodeType": "ParameterList", "parameters": [], "src": "2470:0:1"}, "scope": 378, "src": "2412:59:1", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"documentation": {"id": 362, "nodeType": "StructuredDocumentation", "src": "2477:223:1", "text": " @dev Revokes `role` from `account`.\n If `account` had been granted `role`, emits a {RoleRevoked} event.\n Requirements:\n - the caller must have ``role``'s admin role."}, "functionSelector": "d547741f", "id": 369, "implemented": false, "kind": "function", "modifiers": [], "name": "revokeRole", "nameLocation": "2714:10:1", "nodeType": "FunctionDefinition", "parameters": {"id": 367, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 364, "mutability": "mutable", "name": "role", "nameLocation": "2733:4:1", "nodeType": "VariableDeclaration", "scope": 369, "src": "2725:12:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 363, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "2725:7:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 366, "mutability": "mutable", "name": "account", "nameLocation": "2747:7:1", "nodeType": "VariableDeclaration", "scope": 369, "src": "2739:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 365, "name": "address", "nodeType": "ElementaryTypeName", "src": "2739:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2724:31:1"}, "returnParameters": {"id": 368, "nodeType": "ParameterList", "parameters": [], "src": "2764:0:1"}, "scope": 378, "src": "2705:60:1", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"documentation": {"id": 370, "nodeType": "StructuredDocumentation", "src": "2771:491:1", "text": " @dev Revokes `role` from the calling account.\n Roles are often managed via {grantRole} and {revokeRole}: this function's\n purpose is to provide a mechanism for accounts to lose their privileges\n if they are compromised (such as when a trusted device is misplaced).\n If the calling account had been granted `role`, emits a {RoleRevoked}\n event.\n Requirements:\n - the caller must be `callerConfirmation`."}, "functionSelector": "36568abe", "id": 377, "implemented": false, "kind": "function", "modifiers": [], "name": "renounceRole", "nameLocation": "3276:12:1", "nodeType": "FunctionDefinition", "parameters": {"id": 375, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 372, "mutability": "mutable", "name": "role", "nameLocation": "3297:4:1", "nodeType": "VariableDeclaration", "scope": 377, "src": "3289:12:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 371, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "3289:7:1", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 374, "mutability": "mutable", "name": "callerConfirmation", "nameLocation": "3311:18:1", "nodeType": "VariableDeclaration", "scope": 377, "src": "3303:26:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 373, "name": "address", "nodeType": "ElementaryTypeName", "src": "3303:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "3288:42:1"}, "returnParameters": {"id": 376, "nodeType": "ParameterList", "parameters": [], "src": "3339:0:1"}, "scope": 378, "src": "3267:73:1", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}], "scope": 379, "src": "226:3116:1", "usedErrors": [305, 308], "usedEvents": [317, 326, 335]}], "src": "109:3234:1"}, "id": 1}, "@openzeppelin/contracts/utils/Context.sol": {"ast": {"absolutePath": "@openzeppelin/contracts/utils/Context.sol", "exportedSymbols": {"Context": [408]}, "id": 409, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 380, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "101:24:2"}, {"abstract": true, "baseContracts": [], "canonicalName": "Context", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 381, "nodeType": "StructuredDocumentation", "src": "127:496:2", "text": " @dev Provides information about the current execution context, including the\n sender of the transaction and its data. While these are generally available\n via msg.sender and msg.data, they should not be accessed in such a direct\n manner, since when dealing with meta-transactions the account sending and\n paying for execution may not be the actual sender (as far as an application\n is concerned).\n This contract is only required for intermediate, library-like contracts."}, "fullyImplemented": true, "id": 408, "linearizedBaseContracts": [408], "name": "Context", "nameLocation": "642:7:2", "nodeType": "ContractDefinition", "nodes": [{"body": {"id": 389, "nodeType": "Block", "src": "718:34:2", "statements": [{"expression": {"expression": {"id": 386, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "735:3:2", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 387, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "739:6:2", "memberName": "sender", "nodeType": "MemberAccess", "src": "735:10:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 385, "id": 388, "nodeType": "Return", "src": "728:17:2"}]}, "id": 390, "implemented": true, "kind": "function", "modifiers": [], "name": "_msgSender", "nameLocation": "665:10:2", "nodeType": "FunctionDefinition", "parameters": {"id": 382, "nodeType": "ParameterList", "parameters": [], "src": "675:2:2"}, "returnParameters": {"id": 385, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 384, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 390, "src": "709:7:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 383, "name": "address", "nodeType": "ElementaryTypeName", "src": "709:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "708:9:2"}, "scope": 408, "src": "656:96:2", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"body": {"id": 398, "nodeType": "Block", "src": "825:32:2", "statements": [{"expression": {"expression": {"id": 395, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "842:3:2", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 396, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "846:4:2", "memberName": "data", "nodeType": "MemberAccess", "src": "842:8:2", "typeDescriptions": {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes calldata"}}, "functionReturnParameters": 394, "id": 397, "nodeType": "Return", "src": "835:15:2"}]}, "id": 399, "implemented": true, "kind": "function", "modifiers": [], "name": "_msgData", "nameLocation": "767:8:2", "nodeType": "FunctionDefinition", "parameters": {"id": 391, "nodeType": "ParameterList", "parameters": [], "src": "775:2:2"}, "returnParameters": {"id": 394, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 393, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 399, "src": "809:14:2", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes"}, "typeName": {"id": 392, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "809:5:2", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "808:16:2"}, "scope": 408, "src": "758:99:2", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"body": {"id": 406, "nodeType": "Block", "src": "935:25:2", "statements": [{"expression": {"hexValue": "30", "id": 404, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "952:1:2", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "functionReturnParameters": 403, "id": 405, "nodeType": "Return", "src": "945:8:2"}]}, "id": 407, "implemented": true, "kind": "function", "modifiers": [], "name": "_contextSuffixLength", "nameLocation": "872:20:2", "nodeType": "FunctionDefinition", "parameters": {"id": 400, "nodeType": "ParameterList", "parameters": [], "src": "892:2:2"}, "returnParameters": {"id": 403, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 402, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 407, "src": "926:7:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 401, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "926:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "925:9:2"}, "scope": 408, "src": "863:97:2", "stateMutability": "view", "virtual": true, "visibility": "internal"}], "scope": 409, "src": "624:338:2", "usedErrors": [], "usedEvents": []}], "src": "101:862:2"}, "id": 2}, "@openzeppelin/contracts/utils/introspection/ERC165.sol": {"ast": {"absolutePath": "@openzeppelin/contracts/utils/introspection/ERC165.sol", "exportedSymbols": {"ERC165": [432], "IERC165": [444]}, "id": 433, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 410, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "114:24:3"}, {"absolutePath": "@openzeppelin/contracts/utils/introspection/IERC165.sol", "file": "./IERC165.sol", "id": 412, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 433, "sourceUnit": 445, "src": "140:38:3", "symbolAliases": [{"foreign": {"id": 411, "name": "IERC165", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 444, "src": "148:7:3", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"abstract": true, "baseContracts": [{"baseName": {"id": 414, "name": "IERC165", "nameLocations": ["688:7:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 444, "src": "688:7:3"}, "id": 415, "nodeType": "InheritanceSpecifier", "src": "688:7:3"}], "canonicalName": "ERC165", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 413, "nodeType": "StructuredDocumentation", "src": "180:479:3", "text": " @dev Implementation of the {IERC165} interface.\n Contracts that want to implement ERC-165 should inherit from this contract and override {supportsInterface} to check\n for the additional interface id that will be supported. For example:\n ```solidity\n function supportsInterface(bytes4 interfaceId) public view virtual override returns (bool) {\n     return interfaceId == type(MyInterface).interfaceId || super.supportsInterface(interfaceId);\n }\n ```"}, "fullyImplemented": true, "id": 432, "linearizedBaseContracts": [432, 444], "name": "ERC165", "nameLocation": "678:6:3", "nodeType": "ContractDefinition", "nodes": [{"baseFunctions": [443], "body": {"id": 430, "nodeType": "Block", "src": "845:64:3", "statements": [{"expression": {"commonType": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}, "id": 428, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 423, "name": "interfaceId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 418, "src": "862:11:3", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"arguments": [{"id": 425, "name": "IERC165", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 444, "src": "882:7:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IERC165_$444_$", "typeString": "type(contract IERC165)"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_type$_t_contract$_IERC165_$444_$", "typeString": "type(contract IERC165)"}], "id": 424, "name": "type", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -27, "src": "877:4:3", "typeDescriptions": {"typeIdentifier": "t_function_metatype_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 426, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "877:13:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_magic_meta_type_t_contract$_IERC165_$444", "typeString": "type(contract IERC165)"}}, "id": 427, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "891:11:3", "memberName": "interfaceId", "nodeType": "MemberAccess", "src": "877:25:3", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}}, "src": "862:40:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 422, "id": 429, "nodeType": "Return", "src": "855:47:3"}]}, "documentation": {"id": 416, "nodeType": "StructuredDocumentation", "src": "702:56:3", "text": " @dev See {IERC165-supportsInterface}."}, "functionSelector": "01ffc9a7", "id": 431, "implemented": true, "kind": "function", "modifiers": [], "name": "supportsInterface", "nameLocation": "772:17:3", "nodeType": "FunctionDefinition", "parameters": {"id": 419, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 418, "mutability": "mutable", "name": "interfaceId", "nameLocation": "797:11:3", "nodeType": "VariableDeclaration", "scope": 431, "src": "790:18:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}, "typeName": {"id": 417, "name": "bytes4", "nodeType": "ElementaryTypeName", "src": "790:6:3", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}}, "visibility": "internal"}], "src": "789:20:3"}, "returnParameters": {"id": 422, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 421, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 431, "src": "839:4:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 420, "name": "bool", "nodeType": "ElementaryTypeName", "src": "839:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "838:6:3"}, "scope": 432, "src": "763:146:3", "stateMutability": "view", "virtual": true, "visibility": "public"}], "scope": 433, "src": "660:251:3", "usedErrors": [], "usedEvents": []}], "src": "114:798:3"}, "id": 3}, "@openzeppelin/contracts/utils/introspection/IERC165.sol": {"ast": {"absolutePath": "@openzeppelin/contracts/utils/introspection/IERC165.sol", "exportedSymbols": {"IERC165": [444]}, "id": 445, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 434, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "115:24:4"}, {"abstract": false, "baseContracts": [], "canonicalName": "IERC165", "contractDependencies": [], "contractKind": "interface", "documentation": {"id": 435, "nodeType": "StructuredDocumentation", "src": "141:280:4", "text": " @dev Interface of the ERC-165 standard, as defined in the\n https://eips.ethereum.org/EIPS/eip-165[ERC].\n Implementers can declare support of contract interfaces, which can then be\n queried by others ({ERC165<PERSON><PERSON><PERSON>}).\n For an implementation, see {ERC165}."}, "fullyImplemented": false, "id": 444, "linearizedBaseContracts": [444], "name": "IERC165", "nameLocation": "432:7:4", "nodeType": "ContractDefinition", "nodes": [{"documentation": {"id": 436, "nodeType": "StructuredDocumentation", "src": "446:340:4", "text": " @dev Returns true if this contract implements the interface defined by\n `interfaceId`. See the corresponding\n https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[ERC section]\n to learn more about how these ids are created.\n This function call must use less than 30 000 gas."}, "functionSelector": "01ffc9a7", "id": 443, "implemented": false, "kind": "function", "modifiers": [], "name": "supportsInterface", "nameLocation": "800:17:4", "nodeType": "FunctionDefinition", "parameters": {"id": 439, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 438, "mutability": "mutable", "name": "interfaceId", "nameLocation": "825:11:4", "nodeType": "VariableDeclaration", "scope": 443, "src": "818:18:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}, "typeName": {"id": 437, "name": "bytes4", "nodeType": "ElementaryTypeName", "src": "818:6:4", "typeDescriptions": {"typeIdentifier": "t_bytes4", "typeString": "bytes4"}}, "visibility": "internal"}], "src": "817:20:4"}, "returnParameters": {"id": 442, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 441, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 443, "src": "861:4:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 440, "name": "bool", "nodeType": "ElementaryTypeName", "src": "861:4:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "860:6:4"}, "scope": 444, "src": "791:76:4", "stateMutability": "view", "virtual": false, "visibility": "external"}], "scope": 445, "src": "422:447:4", "usedErrors": [], "usedEvents": []}], "src": "115:755:4"}, "id": 4}, "contracts/SimpleClaimRegistry.sol": {"ast": {"absolutePath": "contracts/SimpleClaimRegistry.sol", "exportedSymbols": {"AccessControl": [295], "Context": [408], "ERC165": [432], "IAccessControl": [378], "SimpleClaimRegistry": [1101]}, "id": 1102, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 446, "literals": ["solidity", "^", "0.8", ".19"], "nodeType": "PragmaDirective", "src": "32:24:5"}, {"absolutePath": "@openzeppelin/contracts/access/AccessControl.sol", "file": "@openzeppelin/contracts/access/AccessControl.sol", "id": 447, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 1102, "sourceUnit": 296, "src": "58:58:5", "symbolAliases": [], "unitAlias": ""}, {"abstract": false, "baseContracts": [{"baseName": {"id": 449, "name": "AccessControl", "nameLocations": ["258:13:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 295, "src": "258:13:5"}, "id": 450, "nodeType": "InheritanceSpecifier", "src": "258:13:5"}], "canonicalName": "SimpleClaimRegistry", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 448, "nodeType": "StructuredDocumentation", "src": "118:107:5", "text": " @title SimpleClaimRegistry\n @dev A simplified claim registry with custom claim type management"}, "fullyImplemented": true, "id": 1101, "linearizedBaseContracts": [1101, 295, 432, 444, 378, 408], "name": "SimpleClaimRegistry", "nameLocation": "235:19:5", "nodeType": "ContractDefinition", "nodes": [{"constant": true, "functionSelector": "a044e70f", "id": 455, "mutability": "constant", "name": "CLAIM_ISSUER_ROLE", "nameLocation": "302:17:5", "nodeType": "VariableDeclaration", "scope": 1101, "src": "278:74:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 451, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "278:7:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "value": {"arguments": [{"hexValue": "434c41494d5f4953535545525f524f4c45", "id": 453, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "332:19:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_df6bc58af35302f8541fb5d0da6c4472be7fc3a416bf34042d13743ac0a50915", "typeString": "literal_string \"CLAIM_ISSUER_ROLE\""}, "value": "CLAIM_ISSUER_ROLE"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_df6bc58af35302f8541fb5d0da6c4472be7fc3a416bf34042d13743ac0a50915", "typeString": "literal_string \"CLAIM_ISSUER_ROLE\""}], "id": 452, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "322:9:5", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 454, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "322:30:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "public"}, {"constant": true, "functionSelector": "e60ecdd2", "id": 460, "mutability": "constant", "name": "CLAIM_VERIFIER_ROLE", "nameLocation": "382:19:5", "nodeType": "VariableDeclaration", "scope": 1101, "src": "358:78:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 456, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "358:7:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "value": {"arguments": [{"hexValue": "434c41494d5f56455249464945525f524f4c45", "id": 458, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "414:21:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_a9ef30cacd3c540e9d2b47058ce383d745f3e72389b3ea103db79727ba9cce89", "typeString": "literal_string \"CLAIM_VERIFIER_ROLE\""}, "value": "CLAIM_VERIFIER_ROLE"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_a9ef30cacd3c540e9d2b47058ce383d745f3e72389b3ea103db79727ba9cce89", "typeString": "literal_string \"CLAIM_VERIFIER_ROLE\""}], "id": 457, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "404:9:5", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 459, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "404:32:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "public"}, {"constant": false, "id": 462, "mutability": "mutable", "name": "_nextClaimTypeId", "nameLocation": "490:16:5", "nodeType": "VariableDeclaration", "scope": 1101, "src": "474:32:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 461, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "474:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "private"}, {"canonicalName": "SimpleClaimRegistry.ClaimType", "id": 475, "members": [{"constant": false, "id": 464, "mutability": "mutable", "name": "id", "nameLocation": "552:2:5", "nodeType": "VariableDeclaration", "scope": 475, "src": "544:10:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 463, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "544:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 466, "mutability": "mutable", "name": "name", "nameLocation": "571:4:5", "nodeType": "VariableDeclaration", "scope": 475, "src": "564:11:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}, "typeName": {"id": 465, "name": "string", "nodeType": "ElementaryTypeName", "src": "564:6:5", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 468, "mutability": "mutable", "name": "description", "nameLocation": "592:11:5", "nodeType": "VariableDeclaration", "scope": 475, "src": "585:18:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}, "typeName": {"id": 467, "name": "string", "nodeType": "ElementaryTypeName", "src": "585:6:5", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 470, "mutability": "mutable", "name": "creator", "nameLocation": "621:7:5", "nodeType": "VariableDeclaration", "scope": 475, "src": "613:15:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 469, "name": "address", "nodeType": "ElementaryTypeName", "src": "613:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 472, "mutability": "mutable", "name": "createdAt", "nameLocation": "646:9:5", "nodeType": "VariableDeclaration", "scope": 475, "src": "638:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 471, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "638:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 474, "mutability": "mutable", "name": "active", "nameLocation": "670:6:5", "nodeType": "VariableDeclaration", "scope": 475, "src": "665:11:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 473, "name": "bool", "nodeType": "ElementaryTypeName", "src": "665:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "name": "ClaimType", "nameLocation": "524:9:5", "nodeType": "StructDefinition", "scope": 1101, "src": "517:166:5", "visibility": "public"}, {"constant": false, "functionSelector": "c1edcdc1", "id": 480, "mutability": "mutable", "name": "claimTypes", "nameLocation": "771:10:5", "nodeType": "VariableDeclaration", "scope": 1101, "src": "734:47:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_ClaimType_$475_storage_$", "typeString": "mapping(uint256 => struct SimpleClaimRegistry.ClaimType)"}, "typeName": {"id": 479, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 476, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "742:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Mapping", "src": "734:29:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_ClaimType_$475_storage_$", "typeString": "mapping(uint256 => struct SimpleClaimRegistry.ClaimType)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 478, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 477, "name": "ClaimType", "nameLocations": ["753:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 475, "src": "753:9:5"}, "referencedDeclaration": 475, "src": "753:9:5", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_storage_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType"}}}, "visibility": "public"}, {"constant": false, "functionSelector": "8ccf00fb", "id": 485, "mutability": "mutable", "name": "creatorClaimTypes", "nameLocation": "871:17:5", "nodeType": "VariableDeclaration", "scope": 1101, "src": "834:54:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_array$_t_uint256_$dyn_storage_$", "typeString": "mapping(address => uint256[])"}, "typeName": {"id": 484, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 481, "name": "address", "nodeType": "ElementaryTypeName", "src": "842:7:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "834:29:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_array$_t_uint256_$dyn_storage_$", "typeString": "mapping(address => uint256[])"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"baseType": {"id": 482, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "853:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 483, "nodeType": "ArrayTypeName", "src": "853:9:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}}, "visibility": "public"}, {"canonicalName": "SimpleClaimRegistry.Claim", "id": 504, "members": [{"constant": false, "id": 487, "mutability": "mutable", "name": "claimType", "nameLocation": "947:9:5", "nodeType": "VariableDeclaration", "scope": 504, "src": "939:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 486, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "939:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 489, "mutability": "mutable", "name": "issuer", "nameLocation": "974:6:5", "nodeType": "VariableDeclaration", "scope": 504, "src": "966:14:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 488, "name": "address", "nodeType": "ElementaryTypeName", "src": "966:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 491, "mutability": "mutable", "name": "subject", "nameLocation": "998:7:5", "nodeType": "VariableDeclaration", "scope": 504, "src": "990:15:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 490, "name": "address", "nodeType": "ElementaryTypeName", "src": "990:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 493, "mutability": "mutable", "name": "signature", "nameLocation": "1021:9:5", "nodeType": "VariableDeclaration", "scope": 504, "src": "1015:15:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}, "typeName": {"id": 492, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "1015:5:5", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}, {"constant": false, "id": 495, "mutability": "mutable", "name": "data", "nameLocation": "1046:4:5", "nodeType": "VariableDeclaration", "scope": 504, "src": "1040:10:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}, "typeName": {"id": 494, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "1040:5:5", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}, {"constant": false, "id": 497, "mutability": "mutable", "name": "uri", "nameLocation": "1067:3:5", "nodeType": "VariableDeclaration", "scope": 504, "src": "1060:10:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}, "typeName": {"id": 496, "name": "string", "nodeType": "ElementaryTypeName", "src": "1060:6:5", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 499, "mutability": "mutable", "name": "issuedAt", "nameLocation": "1088:8:5", "nodeType": "VariableDeclaration", "scope": 504, "src": "1080:16:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 498, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1080:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 501, "mutability": "mutable", "name": "expiresAt", "nameLocation": "1114:9:5", "nodeType": "VariableDeclaration", "scope": 504, "src": "1106:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 500, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1106:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 503, "mutability": "mutable", "name": "revoked", "nameLocation": "1138:7:5", "nodeType": "VariableDeclaration", "scope": 504, "src": "1133:12:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 502, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1133:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "name": "<PERSON><PERSON><PERSON>", "nameLocation": "923:5:5", "nodeType": "StructDefinition", "scope": 1101, "src": "916:236:5", "visibility": "public"}, {"constant": false, "functionSelector": "eff0f592", "id": 509, "mutability": "mutable", "name": "claims", "nameLocation": "1224:6:5", "nodeType": "VariableDeclaration", "scope": 1101, "src": "1191:39:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_struct$_Claim_$504_storage_$", "typeString": "mapping(bytes32 => struct SimpleClaimRegistry.Claim)"}, "typeName": {"id": 508, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 505, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "1199:7:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "Mapping", "src": "1191:25:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_struct$_Claim_$504_storage_$", "typeString": "mapping(bytes32 => struct SimpleClaimRegistry.Claim)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 507, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 506, "name": "<PERSON><PERSON><PERSON>", "nameLocations": ["1210:5:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 504, "src": "1210:5:5"}, "referencedDeclaration": 504, "src": "1210:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Claim_$504_storage_ptr", "typeString": "struct SimpleClaimRegistry.Claim"}}}, "visibility": "public"}, {"constant": false, "functionSelector": "60cf25e2", "id": 516, "mutability": "mutable", "name": "claimIds", "nameLocation": "1349:8:5", "nodeType": "VariableDeclaration", "scope": 1101, "src": "1292:65:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_uint256_$_t_array$_t_bytes32_$dyn_storage_$_$", "typeString": "mapping(address => mapping(uint256 => bytes32[]))"}, "typeName": {"id": 515, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 510, "name": "address", "nodeType": "ElementaryTypeName", "src": "1300:7:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "1292:49:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_uint256_$_t_array$_t_bytes32_$dyn_storage_$_$", "typeString": "mapping(address => mapping(uint256 => bytes32[]))"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 514, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 511, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1319:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Mapping", "src": "1311:29:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_array$_t_bytes32_$dyn_storage_$", "typeString": "mapping(uint256 => bytes32[])"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"baseType": {"id": 512, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "1330:7:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 513, "nodeType": "ArrayTypeName", "src": "1330:9:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage_ptr", "typeString": "bytes32[]"}}}}, "visibility": "public"}, {"anonymous": false, "eventSelector": "294c37b9b884b1967835179d3c147bddc32a4b714710b0885cc4d55c4640fbf1", "id": 528, "name": "ClaimIssued", "nameLocation": "1384:11:5", "nodeType": "EventDefinition", "parameters": {"id": 527, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 518, "indexed": true, "mutability": "mutable", "name": "subject", "nameLocation": "1421:7:5", "nodeType": "VariableDeclaration", "scope": 528, "src": "1405:23:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 517, "name": "address", "nodeType": "ElementaryTypeName", "src": "1405:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 520, "indexed": true, "mutability": "mutable", "name": "claimType", "nameLocation": "1454:9:5", "nodeType": "VariableDeclaration", "scope": 528, "src": "1438:25:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 519, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1438:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 522, "indexed": true, "mutability": "mutable", "name": "claimId", "nameLocation": "1489:7:5", "nodeType": "VariableDeclaration", "scope": 528, "src": "1473:23:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 521, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "1473:7:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 524, "indexed": false, "mutability": "mutable", "name": "issuer", "nameLocation": "1514:6:5", "nodeType": "VariableDeclaration", "scope": 528, "src": "1506:14:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 523, "name": "address", "nodeType": "ElementaryTypeName", "src": "1506:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 526, "indexed": false, "mutability": "mutable", "name": "uri", "nameLocation": "1537:3:5", "nodeType": "VariableDeclaration", "scope": 528, "src": "1530:10:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 525, "name": "string", "nodeType": "ElementaryTypeName", "src": "1530:6:5", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1395:151:5"}, "src": "1378:169:5"}, {"anonymous": false, "eventSelector": "59d2454fe5d0407b3488ae0686b418d569af73ff568a1544fc5c8babaaf9fc0c", "id": 538, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nameLocation": "1559:12:5", "nodeType": "EventDefinition", "parameters": {"id": 537, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 530, "indexed": true, "mutability": "mutable", "name": "subject", "nameLocation": "1597:7:5", "nodeType": "VariableDeclaration", "scope": 538, "src": "1581:23:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 529, "name": "address", "nodeType": "ElementaryTypeName", "src": "1581:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 532, "indexed": true, "mutability": "mutable", "name": "claimType", "nameLocation": "1630:9:5", "nodeType": "VariableDeclaration", "scope": 538, "src": "1614:25:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 531, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1614:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 534, "indexed": true, "mutability": "mutable", "name": "claimId", "nameLocation": "1665:7:5", "nodeType": "VariableDeclaration", "scope": 538, "src": "1649:23:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 533, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "1649:7:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 536, "indexed": false, "mutability": "mutable", "name": "issuer", "nameLocation": "1690:6:5", "nodeType": "VariableDeclaration", "scope": 538, "src": "1682:14:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 535, "name": "address", "nodeType": "ElementaryTypeName", "src": "1682:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1571:131:5"}, "src": "1553:150:5"}, {"anonymous": false, "eventSelector": "b32c7c206740e72055364db6657b89f760889a8633965910821d462a0e21682b", "id": 548, "name": "ClaimTypeCreated", "nameLocation": "1715:16:5", "nodeType": "EventDefinition", "parameters": {"id": 547, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 540, "indexed": true, "mutability": "mutable", "name": "claimTypeId", "nameLocation": "1757:11:5", "nodeType": "VariableDeclaration", "scope": 548, "src": "1741:27:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 539, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1741:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 542, "indexed": false, "mutability": "mutable", "name": "name", "nameLocation": "1785:4:5", "nodeType": "VariableDeclaration", "scope": 548, "src": "1778:11:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 541, "name": "string", "nodeType": "ElementaryTypeName", "src": "1778:6:5", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 544, "indexed": false, "mutability": "mutable", "name": "description", "nameLocation": "1806:11:5", "nodeType": "VariableDeclaration", "scope": 548, "src": "1799:18:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 543, "name": "string", "nodeType": "ElementaryTypeName", "src": "1799:6:5", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 546, "indexed": true, "mutability": "mutable", "name": "creator", "nameLocation": "1843:7:5", "nodeType": "VariableDeclaration", "scope": 548, "src": "1827:23:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 545, "name": "address", "nodeType": "ElementaryTypeName", "src": "1827:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1731:125:5"}, "src": "1709:148:5"}, {"anonymous": false, "eventSelector": "bee22c24db7f76bc89936193c155555cc1a2301094f46bbfa4ed0b7b5dd38d4b", "id": 558, "name": "ClaimTypeUpdated", "nameLocation": "1869:16:5", "nodeType": "EventDefinition", "parameters": {"id": 557, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 550, "indexed": true, "mutability": "mutable", "name": "claimTypeId", "nameLocation": "1911:11:5", "nodeType": "VariableDeclaration", "scope": 558, "src": "1895:27:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 549, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1895:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 552, "indexed": false, "mutability": "mutable", "name": "name", "nameLocation": "1939:4:5", "nodeType": "VariableDeclaration", "scope": 558, "src": "1932:11:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 551, "name": "string", "nodeType": "ElementaryTypeName", "src": "1932:6:5", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 554, "indexed": false, "mutability": "mutable", "name": "description", "nameLocation": "1960:11:5", "nodeType": "VariableDeclaration", "scope": 558, "src": "1953:18:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 553, "name": "string", "nodeType": "ElementaryTypeName", "src": "1953:6:5", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 556, "indexed": false, "mutability": "mutable", "name": "active", "nameLocation": "1986:6:5", "nodeType": "VariableDeclaration", "scope": 558, "src": "1981:11:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 555, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1981:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "1885:113:5"}, "src": "1863:136:5"}, {"body": {"id": 602, "nodeType": "Block", "src": "2032:671:5", "statements": [{"expression": {"arguments": [{"id": 564, "name": "DEFAULT_ADMIN_ROLE", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 29, "src": "2053:18:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 565, "name": "admin", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 560, "src": "2073:5:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 563, "name": "_grantRole", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 256, "src": "2042:10:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_bytes32_$_t_address_$returns$_t_bool_$", "typeString": "function (bytes32,address) returns (bool)"}}, "id": 566, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2042:37:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 567, "nodeType": "ExpressionStatement", "src": "2042:37:5"}, {"expression": {"arguments": [{"id": 569, "name": "CLAIM_ISSUER_ROLE", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 455, "src": "2100:17:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 570, "name": "admin", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 560, "src": "2119:5:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 568, "name": "_grantRole", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 256, "src": "2089:10:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_bytes32_$_t_address_$returns$_t_bool_$", "typeString": "function (bytes32,address) returns (bool)"}}, "id": 571, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2089:36:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 572, "nodeType": "ExpressionStatement", "src": "2089:36:5"}, {"expression": {"arguments": [{"id": 574, "name": "CLAIM_VERIFIER_ROLE", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 460, "src": "2146:19:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 575, "name": "admin", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 560, "src": "2167:5:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 573, "name": "_grantRole", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 256, "src": "2135:10:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_bytes32_$_t_address_$returns$_t_bool_$", "typeString": "function (bytes32,address) returns (bool)"}}, "id": 576, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2135:38:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 577, "nodeType": "ExpressionStatement", "src": "2135:38:5"}, {"expression": {"id": 580, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 578, "name": "_nextClaimTypeId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 462, "src": "2233:16:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "31", "id": 579, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2252:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "2233:20:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 581, "nodeType": "ExpressionStatement", "src": "2233:20:5"}, {"expression": {"arguments": [{"hexValue": "4b594320566572696669636174696f6e", "id": 583, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2354:18:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_86790c3f37c27e314ff6a7d31f4cccdfd293045cf7ba727d1aa710099f0fe7a8", "typeString": "literal_string \"KYC Verification\""}, "value": "KYC Verification"}, {"hexValue": "4261736963206964656e7469747920766572696669636174696f6e207468726f756768204b59432070726f63657373", "id": 584, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2374:49:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_c68393f47a53e3b1e4bfc2753a60f1fdf9c822f348f481eeb6ca21b135280b87", "typeString": "literal_string \"Basic identity verification through KYC process\""}, "value": "Basic identity verification through KYC process"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_86790c3f37c27e314ff6a7d31f4cccdfd293045cf7ba727d1aa710099f0fe7a8", "typeString": "literal_string \"KYC Verification\""}, {"typeIdentifier": "t_stringliteral_c68393f47a53e3b1e4bfc2753a60f1fdf9c822f348f481eeb6ca21b135280b87", "typeString": "literal_string \"Basic identity verification through KYC process\""}], "id": 582, "name": "_createClaimType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 671, "src": "2337:16:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_uint256_$", "typeString": "function (string memory,string memory) returns (uint256)"}}, "id": 585, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2337:87:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 586, "nodeType": "ExpressionStatement", "src": "2337:87:5"}, {"expression": {"arguments": [{"hexValue": "4163637265646974656420496e766573746f72", "id": 588, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2451:21:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_dd4bb6b0eba13642e4ee71304a6245fc178e4262094b4bd28c6e2f04519974ca", "typeString": "literal_string \"Accredited Investor\""}, "value": "Accredited Investor"}, {"hexValue": "5175616c696669656420617320616e206163637265646974656420696e766573746f72", "id": 589, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2474:37:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_ea50e28b2960e20fe97d3c34016f0b2fd81e77f8f55997822b0892b074cfa427", "typeString": "literal_string \"Qualified as an accredited investor\""}, "value": "Qualified as an accredited investor"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_dd4bb6b0eba13642e4ee71304a6245fc178e4262094b4bd28c6e2f04519974ca", "typeString": "literal_string \"Accredited Investor\""}, {"typeIdentifier": "t_stringliteral_ea50e28b2960e20fe97d3c34016f0b2fd81e77f8f55997822b0892b074cfa427", "typeString": "literal_string \"Qualified as an accredited investor\""}], "id": 587, "name": "_createClaimType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 671, "src": "2434:16:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_uint256_$", "typeString": "function (string memory,string memory) returns (uint256)"}}, "id": 590, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2434:78:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 591, "nodeType": "ExpressionStatement", "src": "2434:78:5"}, {"expression": {"arguments": [{"hexValue": "4a7572697364696374696f6e20436f6d706c69616e6365", "id": 593, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2539:25:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_54adb5ec5cf90875612cf8f675f6569688ccf3bb37b577165c3dad04ab20f630", "typeString": "literal_string \"Jurisdiction Compliance\""}, "value": "Jurisdiction Compliance"}, {"hexValue": "4d65657473207370656369666963206a7572697364696374696f6e20726571756972656d656e7473", "id": 594, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2566:42:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_2dbcc6148775b34a0a599fceeaa848a17f8a8fe663e45dab60468c4d57c42706", "typeString": "literal_string \"Meets specific jurisdiction requirements\""}, "value": "Meets specific jurisdiction requirements"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_54adb5ec5cf90875612cf8f675f6569688ccf3bb37b577165c3dad04ab20f630", "typeString": "literal_string \"Jurisdiction Compliance\""}, {"typeIdentifier": "t_stringliteral_2dbcc6148775b34a0a599fceeaa848a17f8a8fe663e45dab60468c4d57c42706", "typeString": "literal_string \"Meets specific jurisdiction requirements\""}], "id": 592, "name": "_createClaimType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 671, "src": "2522:16:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_uint256_$", "typeString": "function (string memory,string memory) returns (uint256)"}}, "id": 595, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2522:87:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 596, "nodeType": "ExpressionStatement", "src": "2522:87:5"}, {"expression": {"arguments": [{"hexValue": "47656e6572616c205175616c696669636174696f6e", "id": 598, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2636:23:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_bc6af3d760336409deae6b38c0387216555cd2a69a3638daca6993d1445b6e5c", "typeString": "literal_string \"General Qualification\""}, "value": "General Qualification"}, {"hexValue": "47656e6572616c20696e766573746d656e74207175616c696669636174696f6e", "id": 599, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2661:34:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_4cec969ecba30de2c5d739dad80f05abbd9d41d13dd9998fad2ebeab2a8d1066", "typeString": "literal_string \"General investment qualification\""}, "value": "General investment qualification"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_bc6af3d760336409deae6b38c0387216555cd2a69a3638daca6993d1445b6e5c", "typeString": "literal_string \"General Qualification\""}, {"typeIdentifier": "t_stringliteral_4cec969ecba30de2c5d739dad80f05abbd9d41d13dd9998fad2ebeab2a8d1066", "typeString": "literal_string \"General investment qualification\""}], "id": 597, "name": "_createClaimType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 671, "src": "2619:16:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_uint256_$", "typeString": "function (string memory,string memory) returns (uint256)"}}, "id": 600, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2619:77:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 601, "nodeType": "ExpressionStatement", "src": "2619:77:5"}]}, "id": 603, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "nodeType": "FunctionDefinition", "parameters": {"id": 561, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 560, "mutability": "mutable", "name": "admin", "nameLocation": "2025:5:5", "nodeType": "VariableDeclaration", "scope": 603, "src": "2017:13:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 559, "name": "address", "nodeType": "ElementaryTypeName", "src": "2017:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2016:15:5"}, "returnParameters": {"id": 562, "nodeType": "ParameterList", "parameters": [], "src": "2032:0:5"}, "scope": 1101, "src": "2005:698:5", "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"body": {"id": 621, "nodeType": "Block", "src": "2914:59:5", "statements": [{"expression": {"arguments": [{"id": 617, "name": "name", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 606, "src": "2948:4:5", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}}, {"id": 618, "name": "description", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 608, "src": "2954:11:5", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}, {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}], "id": 616, "name": "_createClaimType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 671, "src": "2931:16:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$_t_string_memory_ptr_$returns$_t_uint256_$", "typeString": "function (string memory,string memory) returns (uint256)"}}, "id": 619, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2931:35:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 615, "id": 620, "nodeType": "Return", "src": "2924:42:5"}]}, "documentation": {"id": 604, "nodeType": "StructuredDocumentation", "src": "2709:47:5", "text": " @dev Create a new claim type"}, "functionSelector": "2e0d3857", "id": 622, "implemented": true, "kind": "function", "modifiers": [{"arguments": [{"id": 611, "name": "CLAIM_ISSUER_ROLE", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 455, "src": "2877:17:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "id": 612, "kind": "modifierInvocation", "modifierName": {"id": 610, "name": "only<PERSON><PERSON>", "nameLocations": ["2868:8:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 40, "src": "2868:8:5"}, "nodeType": "ModifierInvocation", "src": "2868:27:5"}], "name": "createClaimType", "nameLocation": "2770:15:5", "nodeType": "FunctionDefinition", "parameters": {"id": 609, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 606, "mutability": "mutable", "name": "name", "nameLocation": "2811:4:5", "nodeType": "VariableDeclaration", "scope": 622, "src": "2795:20:5", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string"}, "typeName": {"id": 605, "name": "string", "nodeType": "ElementaryTypeName", "src": "2795:6:5", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 608, "mutability": "mutable", "name": "description", "nameLocation": "2841:11:5", "nodeType": "VariableDeclaration", "scope": 622, "src": "2825:27:5", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string"}, "typeName": {"id": 607, "name": "string", "nodeType": "ElementaryTypeName", "src": "2825:6:5", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "2785:73:5"}, "returnParameters": {"id": 615, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 614, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 622, "src": "2905:7:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 613, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2905:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2904:9:5"}, "scope": 1101, "src": "2761:212:5", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 670, "nodeType": "Block", "src": "3168:491:5", "statements": [{"assignments": [633], "declarations": [{"constant": false, "id": 633, "mutability": "mutable", "name": "claimTypeId", "nameLocation": "3186:11:5", "nodeType": "VariableDeclaration", "scope": 670, "src": "3178:19:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 632, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3178:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 636, "initialValue": {"id": 635, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "3200:18:5", "subExpression": {"id": 634, "name": "_nextClaimTypeId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 462, "src": "3200:16:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "3178:40:5"}, {"expression": {"id": 650, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 637, "name": "claimTypes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 480, "src": "3237:10:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_ClaimType_$475_storage_$", "typeString": "mapping(uint256 => struct SimpleClaimRegistry.ClaimType storage ref)"}}, "id": 639, "indexExpression": {"id": 638, "name": "claimTypeId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 633, "src": "3248:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "3237:23:5", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_storage", "typeString": "struct SimpleClaimRegistry.ClaimType storage ref"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 641, "name": "claimTypeId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 633, "src": "3291:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 642, "name": "name", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 625, "src": "3322:4:5", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 643, "name": "description", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 627, "src": "3353:11:5", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"expression": {"id": 644, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3387:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 645, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3391:6:5", "memberName": "sender", "nodeType": "MemberAccess", "src": "3387:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"expression": {"id": 646, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "3422:5:5", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 647, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3428:9:5", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "3422:15:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"hexValue": "********", "id": 648, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "3459:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 640, "name": "ClaimType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 475, "src": "3263:9:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_ClaimType_$475_storage_ptr_$", "typeString": "type(struct SimpleClaimRegistry.ClaimType storage pointer)"}}, "id": 649, "isConstant": false, "isLValue": false, "isPure": false, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": ["3287:2:5", "3316:4:5", "3340:11:5", "3378:7:5", "3411:9:5", "3451:6:5"], "names": ["id", "name", "description", "creator", "createdAt", "active"], "nodeType": "FunctionCall", "src": "3263:211:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_memory_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType memory"}}, "src": "3237:237:5", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_storage", "typeString": "struct SimpleClaimRegistry.ClaimType storage ref"}}, "id": 651, "nodeType": "ExpressionStatement", "src": "3237:237:5"}, {"expression": {"arguments": [{"id": 657, "name": "claimTypeId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 633, "src": "3528:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"baseExpression": {"id": 652, "name": "creatorClaimTypes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 485, "src": "3493:17:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_array$_t_uint256_$dyn_storage_$", "typeString": "mapping(address => uint256[] storage ref)"}}, "id": 655, "indexExpression": {"expression": {"id": 653, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3511:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 654, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3515:6:5", "memberName": "sender", "nodeType": "MemberAccess", "src": "3511:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3493:29:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage", "typeString": "uint256[] storage ref"}}, "id": 656, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3523:4:5", "memberName": "push", "nodeType": "MemberAccess", "src": "3493:34:5", "typeDescriptions": {"typeIdentifier": "t_function_arraypush_nonpayable$_t_array$_t_uint256_$dyn_storage_ptr_$_t_uint256_$returns$__$attached_to$_t_array$_t_uint256_$dyn_storage_ptr_$", "typeString": "function (uint256[] storage pointer,uint256)"}}, "id": 658, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3493:47:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 659, "nodeType": "ExpressionStatement", "src": "3493:47:5"}, {"eventCall": {"arguments": [{"id": 661, "name": "claimTypeId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 633, "src": "3581:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 662, "name": "name", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 625, "src": "3594:4:5", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 663, "name": "description", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 627, "src": "3600:11:5", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"expression": {"id": 664, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "3613:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 665, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "3617:6:5", "memberName": "sender", "nodeType": "MemberAccess", "src": "3613:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 660, "name": "ClaimTypeCreated", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 548, "src": "3564:16:5", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_address_$returns$__$", "typeString": "function (uint256,string memory,string memory,address)"}}, "id": 666, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3564:60:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 667, "nodeType": "EmitStatement", "src": "3559:65:5"}, {"expression": {"id": 668, "name": "claimTypeId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 633, "src": "3641:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 631, "id": 669, "nodeType": "Return", "src": "3634:18:5"}]}, "documentation": {"id": 623, "nodeType": "StructuredDocumentation", "src": "2979:62:5", "text": " @dev Internal function to create claim type"}, "id": 671, "implemented": true, "kind": "function", "modifiers": [], "name": "_createClaimType", "nameLocation": "3055:16:5", "nodeType": "FunctionDefinition", "parameters": {"id": 628, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 625, "mutability": "mutable", "name": "name", "nameLocation": "3095:4:5", "nodeType": "VariableDeclaration", "scope": 671, "src": "3081:18:5", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 624, "name": "string", "nodeType": "ElementaryTypeName", "src": "3081:6:5", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 627, "mutability": "mutable", "name": "description", "nameLocation": "3123:11:5", "nodeType": "VariableDeclaration", "scope": 671, "src": "3109:25:5", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 626, "name": "string", "nodeType": "ElementaryTypeName", "src": "3109:6:5", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "3071:69:5"}, "returnParameters": {"id": 631, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 630, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 671, "src": "3159:7:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 629, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3159:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "3158:9:5"}, "scope": 1101, "src": "3046:613:5", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"body": {"id": 738, "nodeType": "Block", "src": "3876:504:5", "statements": [{"assignments": [685], "declarations": [{"constant": false, "id": 685, "mutability": "mutable", "name": "claimType", "nameLocation": "3904:9:5", "nodeType": "VariableDeclaration", "scope": 738, "src": "3886:27:5", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_storage_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType"}, "typeName": {"id": 684, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 683, "name": "ClaimType", "nameLocations": ["3886:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 475, "src": "3886:9:5"}, "referencedDeclaration": 475, "src": "3886:9:5", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_storage_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType"}}, "visibility": "internal"}], "id": 689, "initialValue": {"baseExpression": {"id": 686, "name": "claimTypes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 480, "src": "3916:10:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_ClaimType_$475_storage_$", "typeString": "mapping(uint256 => struct SimpleClaimRegistry.ClaimType storage ref)"}}, "id": 688, "indexExpression": {"id": 687, "name": "claimTypeId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 674, "src": "3927:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3916:23:5", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_storage", "typeString": "struct SimpleClaimRegistry.ClaimType storage ref"}}, "nodeType": "VariableDeclarationStatement", "src": "3886:53:5"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 694, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 691, "name": "claimType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 685, "src": "3957:9:5", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_storage_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType storage pointer"}}, "id": 692, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "3967:2:5", "memberName": "id", "nodeType": "MemberAccess", "referencedDeclaration": 464, "src": "3957:12:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"hexValue": "30", "id": 693, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3973:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "3957:17:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "436c61696d52656769737472793a20636c61696d207479706520646f6573206e6f74206578697374", "id": 695, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "3976:42:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_e68cd5bded7d56f830366ced48ac88cc33ff00e6bb7df1f579d69e43134912a5", "typeString": "literal_string \"ClaimRegistry: claim type does not exist\""}, "value": "ClaimRegistry: claim type does not exist"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_e68cd5bded7d56f830366ced48ac88cc33ff00e6bb7df1f579d69e43134912a5", "typeString": "literal_string \"ClaimRegistry: claim type does not exist\""}], "id": 690, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "3949:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 696, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3949:70:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 697, "nodeType": "ExpressionStatement", "src": "3949:70:5"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 709, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 703, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 699, "name": "claimType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 685, "src": "4050:9:5", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_storage_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType storage pointer"}}, "id": 700, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4060:7:5", "memberName": "creator", "nodeType": "MemberAccess", "referencedDeclaration": 470, "src": "4050:17:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"expression": {"id": 701, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "4071:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 702, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4075:6:5", "memberName": "sender", "nodeType": "MemberAccess", "src": "4071:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "4050:31:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "||", "rightExpression": {"arguments": [{"id": 705, "name": "DEFAULT_ADMIN_ROLE", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 29, "src": "4093:18:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"expression": {"id": 706, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "4113:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 707, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4117:6:5", "memberName": "sender", "nodeType": "MemberAccess", "src": "4113:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 704, "name": "hasRole", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 80, "src": "4085:7:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_bytes32_$_t_address_$returns$_t_bool_$", "typeString": "function (bytes32,address) view returns (bool)"}}, "id": 708, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4085:39:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "4050:74:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "436c61696d52656769737472793a206e6f7420617574686f72697a656420746f20757064617465", "id": 710, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "4138:41:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_76f40a3172106c5461ca9f14971d2a89a6e47fdf485775d9f4af2624312731d4", "typeString": "literal_string \"ClaimRegistry: not authorized to update\""}, "value": "ClaimRegistry: not authorized to update"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_76f40a3172106c5461ca9f14971d2a89a6e47fdf485775d9f4af2624312731d4", "typeString": "literal_string \"ClaimRegistry: not authorized to update\""}], "id": 698, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "4029:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 711, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4029:160:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 712, "nodeType": "ExpressionStatement", "src": "4029:160:5"}, {"expression": {"id": 717, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 713, "name": "claimType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 685, "src": "4200:9:5", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_storage_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType storage pointer"}}, "id": 715, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "4210:4:5", "memberName": "name", "nodeType": "MemberAccess", "referencedDeclaration": 466, "src": "4200:14:5", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 716, "name": "name", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 676, "src": "4217:4:5", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}}, "src": "4200:21:5", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "id": 718, "nodeType": "ExpressionStatement", "src": "4200:21:5"}, {"expression": {"id": 723, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 719, "name": "claimType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 685, "src": "4231:9:5", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_storage_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType storage pointer"}}, "id": 721, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "4241:11:5", "memberName": "description", "nodeType": "MemberAccess", "referencedDeclaration": 468, "src": "4231:21:5", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 722, "name": "description", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 678, "src": "4255:11:5", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}}, "src": "4231:35:5", "typeDescriptions": {"typeIdentifier": "t_string_storage", "typeString": "string storage ref"}}, "id": 724, "nodeType": "ExpressionStatement", "src": "4231:35:5"}, {"expression": {"id": 729, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 725, "name": "claimType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 685, "src": "4276:9:5", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_storage_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType storage pointer"}}, "id": 727, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "4286:6:5", "memberName": "active", "nodeType": "MemberAccess", "referencedDeclaration": 474, "src": "4276:16:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 728, "name": "active", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 680, "src": "4295:6:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "4276:25:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 730, "nodeType": "ExpressionStatement", "src": "4276:25:5"}, {"eventCall": {"arguments": [{"id": 732, "name": "claimTypeId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 674, "src": "4334:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 733, "name": "name", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 676, "src": "4347:4:5", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}}, {"id": 734, "name": "description", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 678, "src": "4353:11:5", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}}, {"id": 735, "name": "active", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 680, "src": "4366:6:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}, {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}, {"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 731, "name": "ClaimTypeUpdated", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 558, "src": "4317:16:5", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$_t_string_memory_ptr_$_t_string_memory_ptr_$_t_bool_$returns$__$", "typeString": "function (uint256,string memory,string memory,bool)"}}, "id": 736, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4317:56:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 737, "nodeType": "EmitStatement", "src": "4312:61:5"}]}, "documentation": {"id": 672, "nodeType": "StructuredDocumentation", "src": "3665:49:5", "text": " @dev Update claim type details"}, "functionSelector": "b8233248", "id": 739, "implemented": true, "kind": "function", "modifiers": [], "name": "updateClaimType", "nameLocation": "3728:15:5", "nodeType": "FunctionDefinition", "parameters": {"id": 681, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 674, "mutability": "mutable", "name": "claimTypeId", "nameLocation": "3761:11:5", "nodeType": "VariableDeclaration", "scope": 739, "src": "3753:19:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 673, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3753:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 676, "mutability": "mutable", "name": "name", "nameLocation": "3798:4:5", "nodeType": "VariableDeclaration", "scope": 739, "src": "3782:20:5", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string"}, "typeName": {"id": 675, "name": "string", "nodeType": "ElementaryTypeName", "src": "3782:6:5", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 678, "mutability": "mutable", "name": "description", "nameLocation": "3828:11:5", "nodeType": "VariableDeclaration", "scope": 739, "src": "3812:27:5", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string"}, "typeName": {"id": 677, "name": "string", "nodeType": "ElementaryTypeName", "src": "3812:6:5", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 680, "mutability": "mutable", "name": "active", "nameLocation": "3854:6:5", "nodeType": "VariableDeclaration", "scope": 739, "src": "3849:11:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 679, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3849:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "3743:123:5"}, "returnParameters": {"id": 682, "nodeType": "ParameterList", "parameters": [], "src": "3876:0:5"}, "scope": 1101, "src": "3719:661:5", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 831, "nodeType": "Block", "src": "4694:786:5", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 766, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"baseExpression": {"id": 761, "name": "claimTypes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 480, "src": "4712:10:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_ClaimType_$475_storage_$", "typeString": "mapping(uint256 => struct SimpleClaimRegistry.ClaimType storage ref)"}}, "id": 763, "indexExpression": {"id": 762, "name": "claimType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 744, "src": "4723:9:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "4712:21:5", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_storage", "typeString": "struct SimpleClaimRegistry.ClaimType storage ref"}}, "id": 764, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4734:2:5", "memberName": "id", "nodeType": "MemberAccess", "referencedDeclaration": 464, "src": "4712:24:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"hexValue": "30", "id": 765, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4740:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "4712:29:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "436c61696d52656769737472793a20696e76616c696420636c61696d2074797065", "id": 767, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "4743:35:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_d0e53fa750c46ad63b29999f2f5aeeef17c477258fa26cb6623760071e91a69d", "typeString": "literal_string \"ClaimRegistry: invalid claim type\""}, "value": "ClaimRegistry: invalid claim type"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_d0e53fa750c46ad63b29999f2f5aeeef17c477258fa26cb6623760071e91a69d", "typeString": "literal_string \"ClaimRegistry: invalid claim type\""}], "id": 760, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "4704:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 768, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4704:75:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 769, "nodeType": "ExpressionStatement", "src": "4704:75:5"}, {"expression": {"arguments": [{"expression": {"baseExpression": {"id": 771, "name": "claimTypes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 480, "src": "4797:10:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_ClaimType_$475_storage_$", "typeString": "mapping(uint256 => struct SimpleClaimRegistry.ClaimType storage ref)"}}, "id": 773, "indexExpression": {"id": 772, "name": "claimType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 744, "src": "4808:9:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "4797:21:5", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_storage", "typeString": "struct SimpleClaimRegistry.ClaimType storage ref"}}, "id": 774, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4819:6:5", "memberName": "active", "nodeType": "MemberAccess", "referencedDeclaration": 474, "src": "4797:28:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "436c61696d52656769737472793a20636c61696d207479706520697320696e616374697665", "id": 775, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "4827:39:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_b74b8e0f7ac26543caba05be44563c83bff9951dc770d02282ecd2012cf0b2f9", "typeString": "literal_string \"ClaimRegistry: claim type is inactive\""}, "value": "ClaimRegistry: claim type is inactive"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_b74b8e0f7ac26543caba05be44563c83bff9951dc770d02282ecd2012cf0b2f9", "typeString": "literal_string \"ClaimRegistry: claim type is inactive\""}], "id": 770, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "4789:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 776, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4789:78:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 777, "nodeType": "ExpressionStatement", "src": "4789:78:5"}, {"assignments": [779], "declarations": [{"constant": false, "id": 779, "mutability": "mutable", "name": "claimId", "nameLocation": "4886:7:5", "nodeType": "VariableDeclaration", "scope": 831, "src": "4878:15:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 778, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "4878:7:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "id": 792, "initialValue": {"arguments": [{"arguments": [{"id": 783, "name": "subject", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 742, "src": "4936:7:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 784, "name": "claimType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 744, "src": "4945:9:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 785, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "4956:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 786, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4960:6:5", "memberName": "sender", "nodeType": "MemberAccess", "src": "4956:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"expression": {"id": 787, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "4968:5:5", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 788, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4974:9:5", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "4968:15:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 789, "name": "data", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 748, "src": "4985:4:5", "typeDescriptions": {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes calldata"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes calldata"}], "expression": {"id": 781, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "4919:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 782, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "4923:12:5", "memberName": "encodePacked", "nodeType": "MemberAccess", "src": "4919:16:5", "typeDescriptions": {"typeIdentifier": "t_function_abiencodepacked_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 790, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4919:71:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 780, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "4896:9:5", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 791, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4896:104:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "VariableDeclarationStatement", "src": "4878:122:5"}, {"expression": {"id": 809, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 793, "name": "claims", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 509, "src": "5011:6:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_struct$_Claim_$504_storage_$", "typeString": "mapping(bytes32 => struct SimpleClaimRegistry.Claim storage ref)"}}, "id": 795, "indexExpression": {"id": 794, "name": "claimId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 779, "src": "5018:7:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "5011:15:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Claim_$504_storage", "typeString": "struct SimpleClaimRegistry.Claim storage ref"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 797, "name": "claimType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 744, "src": "5060:9:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 798, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "5091:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 799, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5095:6:5", "memberName": "sender", "nodeType": "MemberAccess", "src": "5091:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 800, "name": "subject", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 742, "src": "5124:7:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 801, "name": "signature", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 746, "src": "5156:9:5", "typeDescriptions": {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes calldata"}}, {"id": 802, "name": "data", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 748, "src": "5185:4:5", "typeDescriptions": {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes calldata"}}, {"id": 803, "name": "uri", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 750, "src": "5208:3:5", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}}, {"expression": {"id": 804, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "5235:5:5", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 805, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5241:9:5", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "5235:15:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 806, "name": "expiresAt", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 752, "src": "5275:9:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"hexValue": "66616c7365", "id": 807, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "5307:5:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes calldata"}, {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes calldata"}, {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 796, "name": "<PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 504, "src": "5029:5:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_Claim_$504_storage_ptr_$", "typeString": "type(struct SimpleClaimRegistry.Claim storage pointer)"}}, "id": 808, "isConstant": false, "isLValue": false, "isPure": false, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": ["5049:9:5", "5083:6:5", "5115:7:5", "5145:9:5", "5179:4:5", "5203:3:5", "5225:8:5", "5264:9:5", "5298:7:5"], "names": ["claimType", "issuer", "subject", "signature", "data", "uri", "issuedAt", "expiresAt", "revoked"], "nodeType": "FunctionCall", "src": "5029:294:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_Claim_$504_memory_ptr", "typeString": "struct SimpleClaimRegistry.Claim memory"}}, "src": "5011:312:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Claim_$504_storage", "typeString": "struct SimpleClaimRegistry.Claim storage ref"}}, "id": 810, "nodeType": "ExpressionStatement", "src": "5011:312:5"}, {"expression": {"arguments": [{"id": 817, "name": "claimId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 779, "src": "5368:7:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}], "expression": {"baseExpression": {"baseExpression": {"id": 811, "name": "claimIds", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 516, "src": "5334:8:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_uint256_$_t_array$_t_bytes32_$dyn_storage_$_$", "typeString": "mapping(address => mapping(uint256 => bytes32[] storage ref))"}}, "id": 814, "indexExpression": {"id": 812, "name": "subject", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 742, "src": "5343:7:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5334:17:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_array$_t_bytes32_$dyn_storage_$", "typeString": "mapping(uint256 => bytes32[] storage ref)"}}, "id": 815, "indexExpression": {"id": 813, "name": "claimType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 744, "src": "5352:9:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5334:28:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage", "typeString": "bytes32[] storage ref"}}, "id": 816, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5363:4:5", "memberName": "push", "nodeType": "MemberAccess", "src": "5334:33:5", "typeDescriptions": {"typeIdentifier": "t_function_arraypush_nonpayable$_t_array$_t_bytes32_$dyn_storage_ptr_$_t_bytes32_$returns$__$attached_to$_t_array$_t_bytes32_$dyn_storage_ptr_$", "typeString": "function (bytes32[] storage pointer,bytes32)"}}, "id": 818, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5334:42:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 819, "nodeType": "ExpressionStatement", "src": "5334:42:5"}, {"eventCall": {"arguments": [{"id": 821, "name": "subject", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 742, "src": "5404:7:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 822, "name": "claimType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 744, "src": "5413:9:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 823, "name": "claimId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 779, "src": "5424:7:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"expression": {"id": 824, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "5433:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 825, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5437:6:5", "memberName": "sender", "nodeType": "MemberAccess", "src": "5433:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 826, "name": "uri", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 750, "src": "5445:3:5", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string calldata"}], "id": 820, "name": "ClaimIssued", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 528, "src": "5392:11:5", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_uint256_$_t_bytes32_$_t_address_$_t_string_memory_ptr_$returns$__$", "typeString": "function (address,uint256,bytes32,address,string memory)"}}, "id": 827, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5392:57:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 828, "nodeType": "EmitStatement", "src": "5387:62:5"}, {"expression": {"id": 829, "name": "claimId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 779, "src": "5466:7:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "functionReturnParameters": 759, "id": 830, "nodeType": "Return", "src": "5459:14:5"}]}, "documentation": {"id": 740, "nodeType": "StructuredDocumentation", "src": "4386:51:5", "text": " @dev Issue a claim for a subject"}, "functionSelector": "dd63e196", "id": 832, "implemented": true, "kind": "function", "modifiers": [{"arguments": [{"id": 755, "name": "CLAIM_ISSUER_ROLE", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 455, "src": "4657:17:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "id": 756, "kind": "modifierInvocation", "modifierName": {"id": 754, "name": "only<PERSON><PERSON>", "nameLocations": ["4648:8:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 40, "src": "4648:8:5"}, "nodeType": "ModifierInvocation", "src": "4648:27:5"}], "name": "issueClaim", "nameLocation": "4451:10:5", "nodeType": "FunctionDefinition", "parameters": {"id": 753, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 742, "mutability": "mutable", "name": "subject", "nameLocation": "4479:7:5", "nodeType": "VariableDeclaration", "scope": 832, "src": "4471:15:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 741, "name": "address", "nodeType": "ElementaryTypeName", "src": "4471:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 744, "mutability": "mutable", "name": "claimType", "nameLocation": "4504:9:5", "nodeType": "VariableDeclaration", "scope": 832, "src": "4496:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 743, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4496:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 746, "mutability": "mutable", "name": "signature", "nameLocation": "4538:9:5", "nodeType": "VariableDeclaration", "scope": 832, "src": "4523:24:5", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes"}, "typeName": {"id": 745, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "4523:5:5", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}, {"constant": false, "id": 748, "mutability": "mutable", "name": "data", "nameLocation": "4572:4:5", "nodeType": "VariableDeclaration", "scope": 832, "src": "4557:19:5", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes"}, "typeName": {"id": 747, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "4557:5:5", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}, {"constant": false, "id": 750, "mutability": "mutable", "name": "uri", "nameLocation": "4602:3:5", "nodeType": "VariableDeclaration", "scope": 832, "src": "4586:19:5", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_string_calldata_ptr", "typeString": "string"}, "typeName": {"id": 749, "name": "string", "nodeType": "ElementaryTypeName", "src": "4586:6:5", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 752, "mutability": "mutable", "name": "expiresAt", "nameLocation": "4623:9:5", "nodeType": "VariableDeclaration", "scope": 832, "src": "4615:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 751, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4615:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "4461:177:5"}, "returnParameters": {"id": 759, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 758, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 832, "src": "4685:7:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 757, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "4685:7:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "4684:9:5"}, "scope": 1101, "src": "4442:1038:5", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 896, "nodeType": "Block", "src": "5658:394:5", "statements": [{"assignments": [846], "declarations": [{"constant": false, "id": 846, "mutability": "mutable", "name": "subjectClaimIds", "nameLocation": "5685:15:5", "nodeType": "VariableDeclaration", "scope": 896, "src": "5668:32:5", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[]"}, "typeName": {"baseType": {"id": 844, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "5668:7:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 845, "nodeType": "ArrayTypeName", "src": "5668:9:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage_ptr", "typeString": "bytes32[]"}}, "visibility": "internal"}], "id": 852, "initialValue": {"baseExpression": {"baseExpression": {"id": 847, "name": "claimIds", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 516, "src": "5703:8:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_uint256_$_t_array$_t_bytes32_$dyn_storage_$_$", "typeString": "mapping(address => mapping(uint256 => bytes32[] storage ref))"}}, "id": 849, "indexExpression": {"id": 848, "name": "subject", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 835, "src": "5712:7:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5703:17:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_array$_t_bytes32_$dyn_storage_$", "typeString": "mapping(uint256 => bytes32[] storage ref)"}}, "id": 851, "indexExpression": {"id": 850, "name": "claimType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 837, "src": "5721:9:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5703:28:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage", "typeString": "bytes32[] storage ref"}}, "nodeType": "VariableDeclarationStatement", "src": "5668:63:5"}, {"body": {"id": 892, "nodeType": "Block", "src": "5803:212:5", "statements": [{"assignments": [866], "declarations": [{"constant": false, "id": 866, "mutability": "mutable", "name": "claim", "nameLocation": "5830:5:5", "nodeType": "VariableDeclaration", "scope": 892, "src": "5817:18:5", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_Claim_$504_memory_ptr", "typeString": "struct SimpleClaimRegistry.Claim"}, "typeName": {"id": 865, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 864, "name": "<PERSON><PERSON><PERSON>", "nameLocations": ["5817:5:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 504, "src": "5817:5:5"}, "referencedDeclaration": 504, "src": "5817:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Claim_$504_storage_ptr", "typeString": "struct SimpleClaimRegistry.Claim"}}, "visibility": "internal"}], "id": 872, "initialValue": {"baseExpression": {"id": 867, "name": "claims", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 509, "src": "5838:6:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_struct$_Claim_$504_storage_$", "typeString": "mapping(bytes32 => struct SimpleClaimRegistry.Claim storage ref)"}}, "id": 871, "indexExpression": {"baseExpression": {"id": 868, "name": "subjectClaimIds", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 846, "src": "5845:15:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[] memory"}}, "id": 870, "indexExpression": {"id": 869, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 854, "src": "5861:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5845:18:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5838:26:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Claim_$504_storage", "typeString": "struct SimpleClaimRegistry.Claim storage ref"}}, "nodeType": "VariableDeclarationStatement", "src": "5817:47:5"}, {"condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 887, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 875, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "5882:14:5", "subExpression": {"expression": {"id": 873, "name": "claim", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 866, "src": "5883:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Claim_$504_memory_ptr", "typeString": "struct SimpleClaimRegistry.Claim memory"}}, "id": 874, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5889:7:5", "memberName": "revoked", "nodeType": "MemberAccess", "referencedDeclaration": 503, "src": "5883:13:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"components": [{"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 885, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 879, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 876, "name": "claim", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 866, "src": "5901:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Claim_$504_memory_ptr", "typeString": "struct SimpleClaimRegistry.Claim memory"}}, "id": 877, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5907:9:5", "memberName": "expiresAt", "nodeType": "MemberAccess", "referencedDeclaration": 501, "src": "5901:15:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "30", "id": 878, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "5920:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "5901:20:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "||", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 884, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 880, "name": "claim", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 866, "src": "5925:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Claim_$504_memory_ptr", "typeString": "struct SimpleClaimRegistry.Claim memory"}}, "id": 881, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "5931:9:5", "memberName": "expiresAt", "nodeType": "MemberAccess", "referencedDeclaration": 501, "src": "5925:15:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"expression": {"id": 882, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "5943:5:5", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 883, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5949:9:5", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "5943:15:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "5925:33:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "5901:57:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "id": 886, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "5900:59:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "5882:77:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 891, "nodeType": "IfStatement", "src": "5878:127:5", "trueBody": {"id": 890, "nodeType": "Block", "src": "5961:44:5", "statements": [{"expression": {"hexValue": "********", "id": 888, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "5986:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 841, "id": 889, "nodeType": "Return", "src": "5979:11:5"}]}}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 860, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 857, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 854, "src": "5770:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"expression": {"id": 858, "name": "subjectClaimIds", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 846, "src": "5774:15:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[] memory"}}, "id": 859, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5790:6:5", "memberName": "length", "nodeType": "MemberAccess", "src": "5774:22:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "5770:26:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 893, "initializationExpression": {"assignments": [854], "declarations": [{"constant": false, "id": 854, "mutability": "mutable", "name": "i", "nameLocation": "5763:1:5", "nodeType": "VariableDeclaration", "scope": 893, "src": "5755:9:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 853, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "5755:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 856, "initialValue": {"hexValue": "30", "id": 855, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "5767:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "5755:13:5"}, "isSimpleCounterLoop": true, "loopExpression": {"expression": {"id": 862, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "5798:3:5", "subExpression": {"id": 861, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 854, "src": "5798:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 863, "nodeType": "ExpressionStatement", "src": "5798:3:5"}, "nodeType": "ForStatement", "src": "5750:265:5"}, {"expression": {"hexValue": "66616c7365", "id": 894, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "6040:5:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}, "functionReturnParameters": 841, "id": 895, "nodeType": "Return", "src": "6033:12:5"}]}, "documentation": {"id": 833, "nodeType": "StructuredDocumentation", "src": "5486:79:5", "text": " @dev Check if a subject has a valid claim of a specific type"}, "functionSelector": "2c52e7ab", "id": 897, "implemented": true, "kind": "function", "modifiers": [], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nameLocation": "5579:13:5", "nodeType": "FunctionDefinition", "parameters": {"id": 838, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 835, "mutability": "mutable", "name": "subject", "nameLocation": "5601:7:5", "nodeType": "VariableDeclaration", "scope": 897, "src": "5593:15:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 834, "name": "address", "nodeType": "ElementaryTypeName", "src": "5593:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 837, "mutability": "mutable", "name": "claimType", "nameLocation": "5618:9:5", "nodeType": "VariableDeclaration", "scope": 897, "src": "5610:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 836, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "5610:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "5592:36:5"}, "returnParameters": {"id": 841, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 840, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 897, "src": "5652:4:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 839, "name": "bool", "nodeType": "ElementaryTypeName", "src": "5652:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "5651:6:5"}, "scope": 1101, "src": "5570:482:5", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 1035, "nodeType": "Block", "src": "6228:1076:5", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 916, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 912, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 910, "name": "limit", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 902, "src": "6246:5:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 911, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "6254:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "6246:9:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 915, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 913, "name": "limit", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 902, "src": "6259:5:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"hexValue": "313030", "id": 914, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "6268:3:5", "typeDescriptions": {"typeIdentifier": "t_rational_100_by_1", "typeString": "int_const 100"}, "value": "100"}, "src": "6259:12:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "6246:25:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "436c61696d52656769737472793a20696e76616c6964206c696d6974", "id": 917, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "6273:30:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_aba66d549d0dd3b16530185304e6c3b48aad5f960069d9e42c1107a76631beb3", "typeString": "literal_string \"ClaimRegistry: invalid limit\""}, "value": "ClaimRegistry: invalid limit"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_aba66d549d0dd3b16530185304e6c3b48aad5f960069d9e42c1107a76631beb3", "typeString": "literal_string \"ClaimRegistry: invalid limit\""}], "id": 909, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "6238:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 918, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6238:66:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 919, "nodeType": "ExpressionStatement", "src": "6238:66:5"}, {"assignments": [921], "declarations": [{"constant": false, "id": 921, "mutability": "mutable", "name": "activeCount", "nameLocation": "6367:11:5", "nodeType": "VariableDeclaration", "scope": 1035, "src": "6359:19:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 920, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "6359:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 923, "initialValue": {"hexValue": "30", "id": 922, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "6381:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "6359:23:5"}, {"body": {"id": 943, "nodeType": "Block", "src": "6439:96:5", "statements": [{"condition": {"expression": {"baseExpression": {"id": 934, "name": "claimTypes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 480, "src": "6457:10:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_ClaimType_$475_storage_$", "typeString": "mapping(uint256 => struct SimpleClaimRegistry.ClaimType storage ref)"}}, "id": 936, "indexExpression": {"id": 935, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 925, "src": "6468:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "6457:13:5", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_storage", "typeString": "struct SimpleClaimRegistry.ClaimType storage ref"}}, "id": 937, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "6471:6:5", "memberName": "active", "nodeType": "MemberAccess", "referencedDeclaration": 474, "src": "6457:20:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 942, "nodeType": "IfStatement", "src": "6453:72:5", "trueBody": {"id": 941, "nodeType": "Block", "src": "6479:46:5", "statements": [{"expression": {"id": 939, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "6497:13:5", "subExpression": {"id": 938, "name": "activeCount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 921, "src": "6497:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 940, "nodeType": "ExpressionStatement", "src": "6497:13:5"}]}}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 930, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 928, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 925, "src": "6412:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 929, "name": "_nextClaimTypeId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 462, "src": "6416:16:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6412:20:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 944, "initializationExpression": {"assignments": [925], "declarations": [{"constant": false, "id": 925, "mutability": "mutable", "name": "i", "nameLocation": "6405:1:5", "nodeType": "VariableDeclaration", "scope": 944, "src": "6397:9:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 924, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "6397:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 927, "initialValue": {"hexValue": "31", "id": 926, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "6409:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "nodeType": "VariableDeclarationStatement", "src": "6397:13:5"}, "isSimpleCounterLoop": true, "loopExpression": {"expression": {"id": 932, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "6434:3:5", "subExpression": {"id": 931, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 925, "src": "6434:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 933, "nodeType": "ExpressionStatement", "src": "6434:3:5"}, "nodeType": "ForStatement", "src": "6392:143:5"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 947, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 945, "name": "offset", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 900, "src": "6557:6:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"id": 946, "name": "activeCount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 921, "src": "6567:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6557:21:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 956, "nodeType": "IfStatement", "src": "6553:77:5", "trueBody": {"id": 955, "nodeType": "Block", "src": "6580:50:5", "statements": [{"expression": {"arguments": [{"hexValue": "30", "id": 952, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "6617:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 951, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "NewExpression", "src": "6601:15:5", "typeDescriptions": {"typeIdentifier": "t_function_objectcreation_pure$_t_uint256_$returns$_t_array$_t_struct$_ClaimType_$475_memory_ptr_$dyn_memory_ptr_$", "typeString": "function (uint256) pure returns (struct SimpleClaimRegistry.ClaimType memory[] memory)"}, "typeName": {"baseType": {"id": 949, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 948, "name": "ClaimType", "nameLocations": ["6605:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 475, "src": "6605:9:5"}, "referencedDeclaration": 475, "src": "6605:9:5", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_storage_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType"}}, "id": 950, "nodeType": "ArrayTypeName", "src": "6605:11:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_ClaimType_$475_storage_$dyn_storage_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType[]"}}}, "id": 953, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6601:18:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_ClaimType_$475_memory_ptr_$dyn_memory_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType memory[] memory"}}, "functionReturnParameters": 908, "id": 954, "nodeType": "Return", "src": "6594:25:5"}]}}, {"assignments": [958], "declarations": [{"constant": false, "id": 958, "mutability": "mutable", "name": "result<PERSON><PERSON><PERSON>", "nameLocation": "6656:12:5", "nodeType": "VariableDeclaration", "scope": 1035, "src": "6648:20:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 957, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "6648:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 962, "initialValue": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 961, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 959, "name": "activeCount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 921, "src": "6671:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"id": 960, "name": "offset", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 900, "src": "6685:6:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6671:20:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "6648:43:5"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 965, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 963, "name": "result<PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 958, "src": "6705:12:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"id": 964, "name": "limit", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 902, "src": "6720:5:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6705:20:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 971, "nodeType": "IfStatement", "src": "6701:71:5", "trueBody": {"id": 970, "nodeType": "Block", "src": "6727:45:5", "statements": [{"expression": {"id": 968, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 966, "name": "result<PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 958, "src": "6741:12:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 967, "name": "limit", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 902, "src": "6756:5:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6741:20:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 969, "nodeType": "ExpressionStatement", "src": "6741:20:5"}]}}, {"assignments": [976], "declarations": [{"constant": false, "id": 976, "mutability": "mutable", "name": "result", "nameLocation": "6809:6:5", "nodeType": "VariableDeclaration", "scope": 1035, "src": "6790:25:5", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_ClaimType_$475_memory_ptr_$dyn_memory_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType[]"}, "typeName": {"baseType": {"id": 974, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 973, "name": "ClaimType", "nameLocations": ["6790:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 475, "src": "6790:9:5"}, "referencedDeclaration": 475, "src": "6790:9:5", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_storage_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType"}}, "id": 975, "nodeType": "ArrayTypeName", "src": "6790:11:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_ClaimType_$475_storage_$dyn_storage_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType[]"}}, "visibility": "internal"}], "id": 983, "initialValue": {"arguments": [{"id": 981, "name": "result<PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 958, "src": "6834:12:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 980, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "NewExpression", "src": "6818:15:5", "typeDescriptions": {"typeIdentifier": "t_function_objectcreation_pure$_t_uint256_$returns$_t_array$_t_struct$_ClaimType_$475_memory_ptr_$dyn_memory_ptr_$", "typeString": "function (uint256) pure returns (struct SimpleClaimRegistry.ClaimType memory[] memory)"}, "typeName": {"baseType": {"id": 978, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 977, "name": "ClaimType", "nameLocations": ["6822:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 475, "src": "6822:9:5"}, "referencedDeclaration": 475, "src": "6822:9:5", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_storage_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType"}}, "id": 979, "nodeType": "ArrayTypeName", "src": "6822:11:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_ClaimType_$475_storage_$dyn_storage_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType[]"}}}, "id": 982, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6818:29:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_ClaimType_$475_memory_ptr_$dyn_memory_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType memory[] memory"}}, "nodeType": "VariableDeclarationStatement", "src": "6790:57:5"}, {"assignments": [985], "declarations": [{"constant": false, "id": 985, "mutability": "mutable", "name": "resultIndex", "nameLocation": "6865:11:5", "nodeType": "VariableDeclaration", "scope": 1035, "src": "6857:19:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 984, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "6857:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 987, "initialValue": {"hexValue": "30", "id": 986, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "6879:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "6857:23:5"}, {"assignments": [989], "declarations": [{"constant": false, "id": 989, "mutability": "mutable", "name": "currentOffset", "nameLocation": "6898:13:5", "nodeType": "VariableDeclaration", "scope": 1035, "src": "6890:21:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 988, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "6890:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 991, "initialValue": {"hexValue": "30", "id": 990, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "6914:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "6890:25:5"}, {"body": {"id": 1031, "nodeType": "Block", "src": "7011:255:5", "statements": [{"condition": {"expression": {"baseExpression": {"id": 1006, "name": "claimTypes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 480, "src": "7029:10:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_ClaimType_$475_storage_$", "typeString": "mapping(uint256 => struct SimpleClaimRegistry.ClaimType storage ref)"}}, "id": 1008, "indexExpression": {"id": 1007, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 993, "src": "7040:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "7029:13:5", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_storage", "typeString": "struct SimpleClaimRegistry.ClaimType storage ref"}}, "id": 1009, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "7043:6:5", "memberName": "active", "nodeType": "MemberAccess", "referencedDeclaration": 474, "src": "7029:20:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 1030, "nodeType": "IfStatement", "src": "7025:231:5", "trueBody": {"id": 1029, "nodeType": "Block", "src": "7051:205:5", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 1012, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 1010, "name": "currentOffset", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 989, "src": "7073:13:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"id": 1011, "name": "offset", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 900, "src": "7090:6:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "7073:23:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 1025, "nodeType": "IfStatement", "src": "7069:140:5", "trueBody": {"id": 1024, "nodeType": "Block", "src": "7098:111:5", "statements": [{"expression": {"id": 1019, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 1013, "name": "result", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 976, "src": "7120:6:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_ClaimType_$475_memory_ptr_$dyn_memory_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType memory[] memory"}}, "id": 1015, "indexExpression": {"id": 1014, "name": "resultIndex", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 985, "src": "7127:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "7120:19:5", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_memory_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"baseExpression": {"id": 1016, "name": "claimTypes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 480, "src": "7142:10:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_ClaimType_$475_storage_$", "typeString": "mapping(uint256 => struct SimpleClaimRegistry.ClaimType storage ref)"}}, "id": 1018, "indexExpression": {"id": 1017, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 993, "src": "7153:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "7142:13:5", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_storage", "typeString": "struct SimpleClaimRegistry.ClaimType storage ref"}}, "src": "7120:35:5", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_memory_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType memory"}}, "id": 1020, "nodeType": "ExpressionStatement", "src": "7120:35:5"}, {"expression": {"id": 1022, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "7177:13:5", "subExpression": {"id": 1021, "name": "resultIndex", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 985, "src": "7177:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 1023, "nodeType": "ExpressionStatement", "src": "7177:13:5"}]}}, {"expression": {"id": 1027, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "7226:15:5", "subExpression": {"id": 1026, "name": "currentOffset", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 989, "src": "7226:13:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 1028, "nodeType": "ExpressionStatement", "src": "7226:15:5"}]}}]}, "condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 1002, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 998, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 996, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 993, "src": "6954:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 997, "name": "_nextClaimTypeId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 462, "src": "6958:16:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6954:20:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 1001, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 999, "name": "resultIndex", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 985, "src": "6978:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 1000, "name": "result<PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 958, "src": "6992:12:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6978:26:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "6954:50:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 1032, "initializationExpression": {"assignments": [993], "declarations": [{"constant": false, "id": 993, "mutability": "mutable", "name": "i", "nameLocation": "6947:1:5", "nodeType": "VariableDeclaration", "scope": 1032, "src": "6939:9:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 992, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "6939:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 995, "initialValue": {"hexValue": "31", "id": 994, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "6951:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "nodeType": "VariableDeclarationStatement", "src": "6939:13:5"}, "isSimpleCounterLoop": false, "loopExpression": {"expression": {"id": 1004, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "7006:3:5", "subExpression": {"id": 1003, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 993, "src": "7006:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 1005, "nodeType": "ExpressionStatement", "src": "7006:3:5"}, "nodeType": "ForStatement", "src": "6934:332:5"}, {"expression": {"id": 1033, "name": "result", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 976, "src": "7291:6:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_ClaimType_$475_memory_ptr_$dyn_memory_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType memory[] memory"}}, "functionReturnParameters": 908, "id": 1034, "nodeType": "Return", "src": "7284:13:5"}]}, "documentation": {"id": 898, "nodeType": "StructuredDocumentation", "src": "6058:62:5", "text": " @dev Get all active claim types (paginated)"}, "functionSelector": "a335aca8", "id": 1036, "implemented": true, "kind": "function", "modifiers": [], "name": "getActiveClaimTypes", "nameLocation": "6134:19:5", "nodeType": "FunctionDefinition", "parameters": {"id": 903, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 900, "mutability": "mutable", "name": "offset", "nameLocation": "6162:6:5", "nodeType": "VariableDeclaration", "scope": 1036, "src": "6154:14:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 899, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "6154:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 902, "mutability": "mutable", "name": "limit", "nameLocation": "6178:5:5", "nodeType": "VariableDeclaration", "scope": 1036, "src": "6170:13:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 901, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "6170:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "6153:31:5"}, "returnParameters": {"id": 908, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 907, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 1036, "src": "6208:18:5", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_ClaimType_$475_memory_ptr_$dyn_memory_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType[]"}, "typeName": {"baseType": {"id": 905, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 904, "name": "ClaimType", "nameLocations": ["6208:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 475, "src": "6208:9:5"}, "referencedDeclaration": 475, "src": "6208:9:5", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_storage_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType"}}, "id": 906, "nodeType": "ArrayTypeName", "src": "6208:11:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_ClaimType_$475_storage_$dyn_storage_ptr", "typeString": "struct SimpleClaimRegistry.ClaimType[]"}}, "visibility": "internal"}], "src": "6207:20:5"}, "scope": 1101, "src": "6125:1179:5", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 1046, "nodeType": "Block", "src": "7432:44:5", "statements": [{"expression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 1044, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 1042, "name": "_nextClaimTypeId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 462, "src": "7449:16:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"hexValue": "31", "id": 1043, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "7468:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "7449:20:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 1041, "id": 1045, "nodeType": "Return", "src": "7442:27:5"}]}, "documentation": {"id": 1037, "nodeType": "StructuredDocumentation", "src": "7310:55:5", "text": " @dev Get total number of claim types"}, "functionSelector": "948db1b8", "id": 1047, "implemented": true, "kind": "function", "modifiers": [], "name": "getTotalClaimTypes", "nameLocation": "7379:18:5", "nodeType": "FunctionDefinition", "parameters": {"id": 1038, "nodeType": "ParameterList", "parameters": [], "src": "7397:2:5"}, "returnParameters": {"id": 1041, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1040, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 1047, "src": "7423:7:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1039, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7423:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "7422:9:5"}, "scope": 1101, "src": "7370:106:5", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 1067, "nodeType": "Block", "src": "7627:89:5", "statements": [{"expression": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 1065, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 1060, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"baseExpression": {"id": 1055, "name": "claimTypes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 480, "src": "7644:10:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_ClaimType_$475_storage_$", "typeString": "mapping(uint256 => struct SimpleClaimRegistry.ClaimType storage ref)"}}, "id": 1057, "indexExpression": {"id": 1056, "name": "claimTypeId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1050, "src": "7655:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "7644:23:5", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_storage", "typeString": "struct SimpleClaimRegistry.ClaimType storage ref"}}, "id": 1058, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "7668:2:5", "memberName": "id", "nodeType": "MemberAccess", "referencedDeclaration": 464, "src": "7644:26:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"hexValue": "30", "id": 1059, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "7674:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "7644:31:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"expression": {"baseExpression": {"id": 1061, "name": "claimTypes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 480, "src": "7679:10:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_ClaimType_$475_storage_$", "typeString": "mapping(uint256 => struct SimpleClaimRegistry.ClaimType storage ref)"}}, "id": 1063, "indexExpression": {"id": 1062, "name": "claimTypeId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1050, "src": "7690:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "7679:23:5", "typeDescriptions": {"typeIdentifier": "t_struct$_ClaimType_$475_storage", "typeString": "struct SimpleClaimRegistry.ClaimType storage ref"}}, "id": 1064, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "7703:6:5", "memberName": "active", "nodeType": "MemberAccess", "referencedDeclaration": 474, "src": "7679:30:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "7644:65:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 1054, "id": 1066, "nodeType": "Return", "src": "7637:72:5"}]}, "documentation": {"id": 1048, "nodeType": "StructuredDocumentation", "src": "7482:64:5", "text": " @dev Check if claim type exists and is active"}, "functionSelector": "1d541ea2", "id": 1068, "implemented": true, "kind": "function", "modifiers": [], "name": "isValidClaimType", "nameLocation": "7560:16:5", "nodeType": "FunctionDefinition", "parameters": {"id": 1051, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1050, "mutability": "mutable", "name": "claimTypeId", "nameLocation": "7585:11:5", "nodeType": "VariableDeclaration", "scope": 1068, "src": "7577:19:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1049, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7577:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "7576:21:5"}, "returnParameters": {"id": 1054, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1053, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 1068, "src": "7621:4:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 1052, "name": "bool", "nodeType": "ElementaryTypeName", "src": "7621:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "7620:6:5"}, "scope": 1101, "src": "7551:165:5", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 1081, "nodeType": "Block", "src": "7882:50:5", "statements": [{"expression": {"baseExpression": {"id": 1077, "name": "creatorClaimTypes", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 485, "src": "7899:17:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_array$_t_uint256_$dyn_storage_$", "typeString": "mapping(address => uint256[] storage ref)"}}, "id": 1079, "indexExpression": {"id": 1078, "name": "creator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1071, "src": "7917:7:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "7899:26:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage", "typeString": "uint256[] storage ref"}}, "functionReturnParameters": 1076, "id": 1080, "nodeType": "Return", "src": "7892:33:5"}]}, "documentation": {"id": 1069, "nodeType": "StructuredDocumentation", "src": "7722:65:5", "text": " @dev Get all claim types created by an address"}, "functionSelector": "0ec2d8fe", "id": 1082, "implemented": true, "kind": "function", "modifiers": [], "name": "getClaimTypesByCreator", "nameLocation": "7801:22:5", "nodeType": "FunctionDefinition", "parameters": {"id": 1072, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1071, "mutability": "mutable", "name": "creator", "nameLocation": "7832:7:5", "nodeType": "VariableDeclaration", "scope": 1082, "src": "7824:15:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 1070, "name": "address", "nodeType": "ElementaryTypeName", "src": "7824:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "7823:17:5"}, "returnParameters": {"id": 1076, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1075, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 1082, "src": "7864:16:5", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[]"}, "typeName": {"baseType": {"id": 1073, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7864:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 1074, "nodeType": "ArrayTypeName", "src": "7864:9:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}, "visibility": "internal"}], "src": "7863:18:5"}, "scope": 1101, "src": "7792:140:5", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 1099, "nodeType": "Block", "src": "8111:52:5", "statements": [{"expression": {"baseExpression": {"baseExpression": {"id": 1093, "name": "claimIds", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 516, "src": "8128:8:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_uint256_$_t_array$_t_bytes32_$dyn_storage_$_$", "typeString": "mapping(address => mapping(uint256 => bytes32[] storage ref))"}}, "id": 1095, "indexExpression": {"id": 1094, "name": "subject", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1085, "src": "8137:7:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "8128:17:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_array$_t_bytes32_$dyn_storage_$", "typeString": "mapping(uint256 => bytes32[] storage ref)"}}, "id": 1097, "indexExpression": {"id": 1096, "name": "claimType", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1087, "src": "8146:9:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "8128:28:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage", "typeString": "bytes32[] storage ref"}}, "functionReturnParameters": 1092, "id": 1098, "nodeType": "Return", "src": "8121:35:5"}]}, "documentation": {"id": 1083, "nodeType": "StructuredDocumentation", "src": "7938:70:5", "text": " @dev Get all claim IDs for a subject and claim type"}, "functionSelector": "23f86802", "id": 1100, "implemented": true, "kind": "function", "modifiers": [], "name": "getClaimIds", "nameLocation": "8022:11:5", "nodeType": "FunctionDefinition", "parameters": {"id": 1088, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1085, "mutability": "mutable", "name": "subject", "nameLocation": "8042:7:5", "nodeType": "VariableDeclaration", "scope": 1100, "src": "8034:15:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 1084, "name": "address", "nodeType": "ElementaryTypeName", "src": "8034:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 1087, "mutability": "mutable", "name": "claimType", "nameLocation": "8059:9:5", "nodeType": "VariableDeclaration", "scope": 1100, "src": "8051:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1086, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "8051:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "8033:36:5"}, "returnParameters": {"id": 1092, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1091, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 1100, "src": "8093:16:5", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_memory_ptr", "typeString": "bytes32[]"}, "typeName": {"baseType": {"id": 1089, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "8093:7:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "id": 1090, "nodeType": "ArrayTypeName", "src": "8093:9:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_bytes32_$dyn_storage_ptr", "typeString": "bytes32[]"}}, "visibility": "internal"}], "src": "8092:18:5"}, "scope": 1101, "src": "8013:150:5", "stateMutability": "view", "virtual": false, "visibility": "external"}], "scope": 1102, "src": "226:7939:5", "usedErrors": [305, 308], "usedEvents": [317, 326, 335, 528, 538, 548, 558]}], "src": "32:8134:5"}, "id": 5}}, "contracts": {"@openzeppelin/contracts/access/AccessControl.sol": {"AccessControl": {"abi": [{"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {"DEFAULT_ADMIN_ROLE()": "a217fddf", "getRoleAdmin(bytes32)": "248a9ca3", "grantRole(bytes32,address)": "2f2ff15d", "hasRole(bytes32,address)": "91d14854", "renounceRole(bytes32,address)": "36568abe", "revokeRole(bytes32,address)": "d547741f", "supportsInterface(bytes4)": "01ffc9a7"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.22+commit.4fc1097e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"AccessControlBadConfirmation\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"neededRole\",\"type\":\"bytes32\"}],\"name\":\"AccessControlUnauthorizedAccount\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"previousAdminRole\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"newAdminRole\",\"type\":\"bytes32\"}],\"name\":\"RoleAdminChanged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleGranted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleRevoked\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"DEFAULT_ADMIN_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"getRoleAdmin\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"grantRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"hasRole\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"callerConfirmation\",\"type\":\"address\"}],\"name\":\"renounceRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"revokeRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Contract module that allows children to implement role-based access control mechanisms. This is a lightweight version that doesn't allow enumerating role members except through off-chain means by accessing the contract event logs. Some applications may benefit from on-chain enumerability, for those cases see {AccessControlEnumerable}. Roles are referred to by their `bytes32` identifier. These should be exposed in the external API and be unique. The best way to achieve this is by using `public constant` hash digests: ```solidity bytes32 public constant MY_ROLE = keccak256(\\\"MY_ROLE\\\"); ``` Roles can be used to represent a set of permissions. To restrict access to a function call, use {hasRole}: ```solidity function foo() public {     require(hasRole(MY_ROLE, msg.sender));     ... } ``` Roles can be granted and revoked dynamically via the {grantRole} and {revokeRole} functions. Each role has an associated admin role, and only accounts that have a role's admin role can call {grantRole} and {revokeRole}. By default, the admin role for all roles is `DEFAULT_ADMIN_ROLE`, which means that only accounts with this role will be able to grant or revoke other roles. More complex role relationships can be created by using {_setRoleAdmin}. WARNING: The `DEFAULT_ADMIN_ROLE` is also its own admin: it has permission to grant and revoke this role. Extra precautions should be taken to secure accounts that have been granted it. We recommend using {AccessControlDefaultAdminRules} to enforce additional security measures for this role.\",\"errors\":{\"AccessControlBadConfirmation()\":[{\"details\":\"The caller of a function is not the expected one. NOTE: Don't confuse with {AccessControlUnauthorizedAccount}.\"}],\"AccessControlUnauthorizedAccount(address,bytes32)\":[{\"details\":\"The `account` is missing a role.\"}]},\"events\":{\"RoleAdminChanged(bytes32,bytes32,bytes32)\":{\"details\":\"Emitted when `newAdminRole` is set as ``role``'s admin role, replacing `previousAdminRole` `DEFAULT_ADMIN_ROLE` is the starting admin for all roles, despite {RoleAdminChanged} not being emitted to signal this.\"},\"RoleGranted(bytes32,address,address)\":{\"details\":\"Emitted when `account` is granted `role`. `sender` is the account that originated the contract call. This account bears the admin role (for the granted role). Expected in cases where the role was granted using the internal {AccessControl-_grantRole}.\"},\"RoleRevoked(bytes32,address,address)\":{\"details\":\"Emitted when `account` is revoked `role`. `sender` is the account that originated the contract call:   - if using `revokeRole`, it is the admin role bearer   - if using `renounceRole`, it is the role bearer (i.e. `account`)\"}},\"kind\":\"dev\",\"methods\":{\"getRoleAdmin(bytes32)\":{\"details\":\"Returns the admin role that controls `role`. See {grantRole} and {revokeRole}. To change a role's admin, use {_setRoleAdmin}.\"},\"grantRole(bytes32,address)\":{\"details\":\"Grants `role` to `account`. If `account` had not been already granted `role`, emits a {RoleGranted} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleGranted} event.\"},\"hasRole(bytes32,address)\":{\"details\":\"Returns `true` if `account` has been granted `role`.\"},\"renounceRole(bytes32,address)\":{\"details\":\"Revokes `role` from the calling account. Roles are often managed via {grantRole} and {revokeRole}: this function's purpose is to provide a mechanism for accounts to lose their privileges if they are compromised (such as when a trusted device is misplaced). If the calling account had been revoked `role`, emits a {RoleRevoked} event. Requirements: - the caller must be `callerConfirmation`. May emit a {RoleRevoked} event.\"},\"revokeRole(bytes32,address)\":{\"details\":\"Revokes `role` from `account`. If `account` had been granted `role`, emits a {RoleRevoked} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleRevoked} event.\"},\"supportsInterface(bytes4)\":{\"details\":\"See {IERC165-supportsInterface}.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/access/AccessControl.sol\":\"AccessControl\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/access/AccessControl.sol\":{\"keccak256\":\"0xc1bebdee8943bd5e9ef1e0f2e63296aa1dd4171a66b9e74d0286220e891e1458\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://928cf2f0042c606f3dcb21bd8a272573f462a215cd65285d2d6b407f31e9bd67\",\"dweb:/ipfs/QmWGxjckno6sfjHPX5naPnsfsyisgy4PJDf46eLw9umfpx\"]},\"@openzeppelin/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0\",\"dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf\"]},\"@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"@openzeppelin/contracts/utils/introspection/ERC165.sol\":{\"keccak256\":\"0xddce8e17e3d3f9ed818b4f4c4478a8262aab8b11ed322f1bf5ed705bb4bd97fa\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8084aa71a4cc7d2980972412a88fe4f114869faea3fefa5436431644eb5c0287\",\"dweb:/ipfs/Qmbqfs5dRdPvHVKY8kTaeyc65NdqXRQwRK7h9s5UJEhD1p\"]},\"@openzeppelin/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621\",\"dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL\"]}},\"version\":1}", "storageLayout": {"storage": [{"astId": 26, "contract": "@openzeppelin/contracts/access/AccessControl.sol:AccessControl", "label": "_roles", "offset": 0, "slot": "0", "type": "t_mapping(t_bytes32,t_struct(RoleData)21_storage)"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"encoding": "inplace", "label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => bool)", "numberOfBytes": "32", "value": "t_bool"}, "t_mapping(t_bytes32,t_struct(RoleData)21_storage)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => struct AccessControl.RoleData)", "numberOfBytes": "32", "value": "t_struct(RoleData)21_storage"}, "t_struct(RoleData)21_storage": {"encoding": "inplace", "label": "struct AccessControl.RoleData", "members": [{"astId": 18, "contract": "@openzeppelin/contracts/access/AccessControl.sol:AccessControl", "label": "hasRole", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_bool)"}, {"astId": 20, "contract": "@openzeppelin/contracts/access/AccessControl.sol:AccessControl", "label": "adminRole", "offset": 0, "slot": "1", "type": "t_bytes32"}], "numberOfBytes": "64"}}}}}, "@openzeppelin/contracts/access/IAccessControl.sol": {"IAccessControl": {"abi": [{"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {"getRoleAdmin(bytes32)": "248a9ca3", "grantRole(bytes32,address)": "2f2ff15d", "hasRole(bytes32,address)": "91d14854", "renounceRole(bytes32,address)": "36568abe", "revokeRole(bytes32,address)": "d547741f"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.22+commit.4fc1097e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"AccessControlBadConfirmation\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"neededRole\",\"type\":\"bytes32\"}],\"name\":\"AccessControlUnauthorizedAccount\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"previousAdminRole\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"newAdminRole\",\"type\":\"bytes32\"}],\"name\":\"RoleAdminChanged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleGranted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleRevoked\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"getRoleAdmin\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"grantRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"hasRole\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"callerConfirmation\",\"type\":\"address\"}],\"name\":\"renounceRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"revokeRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"External interface of AccessControl declared to support ERC-165 detection.\",\"errors\":{\"AccessControlBadConfirmation()\":[{\"details\":\"The caller of a function is not the expected one. NOTE: Don't confuse with {AccessControlUnauthorizedAccount}.\"}],\"AccessControlUnauthorizedAccount(address,bytes32)\":[{\"details\":\"The `account` is missing a role.\"}]},\"events\":{\"RoleAdminChanged(bytes32,bytes32,bytes32)\":{\"details\":\"Emitted when `newAdminRole` is set as ``role``'s admin role, replacing `previousAdminRole` `DEFAULT_ADMIN_ROLE` is the starting admin for all roles, despite {RoleAdminChanged} not being emitted to signal this.\"},\"RoleGranted(bytes32,address,address)\":{\"details\":\"Emitted when `account` is granted `role`. `sender` is the account that originated the contract call. This account bears the admin role (for the granted role). Expected in cases where the role was granted using the internal {AccessControl-_grantRole}.\"},\"RoleRevoked(bytes32,address,address)\":{\"details\":\"Emitted when `account` is revoked `role`. `sender` is the account that originated the contract call:   - if using `revokeRole`, it is the admin role bearer   - if using `renounceRole`, it is the role bearer (i.e. `account`)\"}},\"kind\":\"dev\",\"methods\":{\"getRoleAdmin(bytes32)\":{\"details\":\"Returns the admin role that controls `role`. See {grantRole} and {revokeRole}. To change a role's admin, use {AccessControl-_setRoleAdmin}.\"},\"grantRole(bytes32,address)\":{\"details\":\"Grants `role` to `account`. If `account` had not been already granted `role`, emits a {RoleGranted} event. Requirements: - the caller must have ``role``'s admin role.\"},\"hasRole(bytes32,address)\":{\"details\":\"Returns `true` if `account` has been granted `role`.\"},\"renounceRole(bytes32,address)\":{\"details\":\"Revokes `role` from the calling account. Roles are often managed via {grantRole} and {revokeRole}: this function's purpose is to provide a mechanism for accounts to lose their privileges if they are compromised (such as when a trusted device is misplaced). If the calling account had been granted `role`, emits a {RoleRevoked} event. Requirements: - the caller must be `callerConfirmation`.\"},\"revokeRole(bytes32,address)\":{\"details\":\"Revokes `role` from `account`. If `account` had been granted `role`, emits a {RoleRevoked} event. Requirements: - the caller must have ``role``'s admin role.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/access/IAccessControl.sol\":\"IAccessControl\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0\",\"dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf\"]}},\"version\":1}", "storageLayout": {"storage": [], "types": null}}}, "@openzeppelin/contracts/utils/Context.sol": {"Context": {"abi": [], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {}}, "metadata": "{\"compiler\":{\"version\":\"0.8.22+commit.4fc1097e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"details\":\"Provides information about the current execution context, including the sender of the transaction and its data. While these are generally available via msg.sender and msg.data, they should not be accessed in such a direct manner, since when dealing with meta-transactions the account sending and paying for execution may not be the actual sender (as far as an application is concerned). This contract is only required for intermediate, library-like contracts.\",\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/utils/Context.sol\":\"Context\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]}},\"version\":1}", "storageLayout": {"storage": [], "types": null}}}, "@openzeppelin/contracts/utils/introspection/ERC165.sol": {"ERC165": {"abi": [{"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {"supportsInterface(bytes4)": "01ffc9a7"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.22+commit.4fc1097e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Implementation of the {IERC165} interface. Contracts that want to implement ERC-165 should inherit from this contract and override {supportsInterface} to check for the additional interface id that will be supported. For example: ```solidity function supportsInterface(bytes4 interfaceId) public view virtual override returns (bool) {     return interfaceId == type(MyInterface).interfaceId || super.supportsInterface(interfaceId); } ```\",\"kind\":\"dev\",\"methods\":{\"supportsInterface(bytes4)\":{\"details\":\"See {IERC165-supportsInterface}.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/utils/introspection/ERC165.sol\":\"ERC165\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/utils/introspection/ERC165.sol\":{\"keccak256\":\"0xddce8e17e3d3f9ed818b4f4c4478a8262aab8b11ed322f1bf5ed705bb4bd97fa\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8084aa71a4cc7d2980972412a88fe4f114869faea3fefa5436431644eb5c0287\",\"dweb:/ipfs/Qmbqfs5dRdPvHVKY8kTaeyc65NdqXRQwRK7h9s5UJEhD1p\"]},\"@openzeppelin/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621\",\"dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL\"]}},\"version\":1}", "storageLayout": {"storage": [], "types": null}}}, "@openzeppelin/contracts/utils/introspection/IERC165.sol": {"IERC165": {"abi": [{"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {"supportsInterface(bytes4)": "01ffc9a7"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.22+commit.4fc1097e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Interface of the ERC-165 standard, as defined in the https://eips.ethereum.org/EIPS/eip-165[ERC]. Implementers can declare support of contract interfaces, which can then be queried by others ({ERC165<PERSON>he<PERSON>}). For an implementation, see {ERC165}.\",\"kind\":\"dev\",\"methods\":{\"supportsInterface(bytes4)\":{\"details\":\"Returns true if this contract implements the interface defined by `interfaceId`. See the corresponding https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[ERC section] to learn more about how these ids are created. This function call must use less than 30 000 gas.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/utils/introspection/IERC165.sol\":\"IERC165\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621\",\"dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL\"]}},\"version\":1}", "storageLayout": {"storage": [], "types": null}}}, "contracts/SimpleClaimRegistry.sol": {"SimpleClaimRegistry": {"abi": [{"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "subject", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "claimType", "type": "uint256"}, {"indexed": true, "internalType": "bytes32", "name": "claimId", "type": "bytes32"}, {"indexed": false, "internalType": "address", "name": "issuer", "type": "address"}, {"indexed": false, "internalType": "string", "name": "uri", "type": "string"}], "name": "ClaimIssued", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "subject", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "claimType", "type": "uint256"}, {"indexed": true, "internalType": "bytes32", "name": "claimId", "type": "bytes32"}, {"indexed": false, "internalType": "address", "name": "issuer", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "claimTypeId", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}, {"indexed": false, "internalType": "string", "name": "description", "type": "string"}, {"indexed": true, "internalType": "address", "name": "creator", "type": "address"}], "name": "ClaimTypeCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "claimTypeId", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}, {"indexed": false, "internalType": "string", "name": "description", "type": "string"}, {"indexed": false, "internalType": "bool", "name": "active", "type": "bool"}], "name": "ClaimTypeUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"inputs": [], "name": "CLAIM_ISSUER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "CLAIM_VERIFIER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "claimIds", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "claimTypes", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "address", "name": "creator", "type": "address"}, {"internalType": "uint256", "name": "createdAt", "type": "uint256"}, {"internalType": "bool", "name": "active", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "claims", "outputs": [{"internalType": "uint256", "name": "claimType", "type": "uint256"}, {"internalType": "address", "name": "issuer", "type": "address"}, {"internalType": "address", "name": "subject", "type": "address"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "string", "name": "uri", "type": "string"}, {"internalType": "uint256", "name": "issuedAt", "type": "uint256"}, {"internalType": "uint256", "name": "expiresAt", "type": "uint256"}, {"internalType": "bool", "name": "revoked", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}], "name": "createClaimType", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "creatorClaimTypes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "offset", "type": "uint256"}, {"internalType": "uint256", "name": "limit", "type": "uint256"}], "name": "getActiveClaimTypes", "outputs": [{"components": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "address", "name": "creator", "type": "address"}, {"internalType": "uint256", "name": "createdAt", "type": "uint256"}, {"internalType": "bool", "name": "active", "type": "bool"}], "internalType": "struct SimpleClaimRegistry.ClaimType[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "subject", "type": "address"}, {"internalType": "uint256", "name": "claimType", "type": "uint256"}], "name": "getClaimIds", "outputs": [{"internalType": "bytes32[]", "name": "", "type": "bytes32[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "creator", "type": "address"}], "name": "getClaimTypesByCreator", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getTotalClaimTypes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "subject", "type": "address"}, {"internalType": "uint256", "name": "claimType", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "claimTypeId", "type": "uint256"}], "name": "isValidClaimType", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "subject", "type": "address"}, {"internalType": "uint256", "name": "claimType", "type": "uint256"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "string", "name": "uri", "type": "string"}, {"internalType": "uint256", "name": "expiresAt", "type": "uint256"}], "name": "issueClaim", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "claimTypeId", "type": "uint256"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "bool", "name": "active", "type": "bool"}], "name": "updateClaimType", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {"abi_encode_string": {"entryPoint": 1174, "id": null, "parameterSlots": 2, "returnSlots": 1}, "allocate_memory": {"entryPoint": 695, "id": null, "parameterSlots": 0, "returnSlots": 1}, "allocate_memory_6599": {"entryPoint": 663, "id": null, "parameterSlots": 0, "returnSlots": 1}, "fun_createClaimType": {"entryPoint": 1240, "id": 671, "parameterSlots": 2, "returnSlots": 1}, "fun_grantRole": {"entryPoint": 855, "id": 256, "parameterSlots": 1, "returnSlots": 1}, "fun_grantRole_3725": {"entryPoint": 727, "id": 256, "parameterSlots": 1, "returnSlots": 1}, "fun_grantRole_3727": {"entryPoint": 1017, "id": 256, "parameterSlots": 1, "returnSlots": 1}}, "generatedSources": [], "linkReferences": {}, "object": "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", "opcodes": "PUSH1 0x80 CALLVALUE PUSH3 0x27C JUMPI PUSH3 0x2772 CODESIZE DUP2 SWAP1 SUB PUSH1 0x1F DUP2 ADD PUSH1 0x1F NOT AND DUP4 ADD PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP2 GT DUP5 DUP3 LT OR PUSH3 0x281 JUMPI DUP4 SWAP3 DUP3 SWAP2 PUSH1 0x40 MSTORE DUP4 CODECOPY PUSH1 0x20 SWAP3 DUP4 SWAP2 DUP2 ADD SUB SLT PUSH3 0x27C JUMPI MLOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP3 AND DUP3 SUB PUSH3 0x27C JUMPI PUSH3 0x7D DUP3 PUSH3 0x6A PUSH3 0x26B SWAP5 PUSH3 0x2D7 JUMP JUMPDEST POP PUSH3 0x76 DUP2 PUSH3 0x357 JUMP JUMPDEST POP PUSH3 0x3F9 JUMP JUMPDEST POP PUSH1 0x1 DUP1 SSTORE PUSH3 0xFE PUSH3 0x90 PUSH3 0x297 JUMP JUMPDEST PUSH1 0x10 DUP2 MSTORE PUSH16 0x25ACA1902B32B934B334B1B0BA34B7B7 PUSH1 0x81 SHL DUP4 DUP3 ADD MSTORE PUSH3 0xB6 PUSH3 0x2B7 JUMP JUMPDEST SWAP1 PUSH1 0x2F DUP3 MSTORE PUSH32 0x4261736963206964656E7469747920766572696669636174696F6E207468726F DUP5 DUP4 ADD MSTORE PUSH15 0x756768204B59432070726F63657373 PUSH1 0x88 SHL PUSH1 0x40 DUP4 ADD MSTORE PUSH3 0x4D8 JUMP JUMPDEST POP PUSH3 0x17C PUSH3 0x10D PUSH3 0x297 JUMP JUMPDEST PUSH1 0x13 DUP2 MSTORE PUSH32 0x4163637265646974656420496E766573746F7200000000000000000000000000 DUP4 DUP3 ADD MSTORE PUSH3 0x140 PUSH3 0x2B7 JUMP JUMPDEST SWAP1 PUSH1 0x23 DUP3 MSTORE PUSH32 0x5175616C696669656420617320616E206163637265646974656420696E766573 DUP5 DUP4 ADD MSTORE PUSH3 0x3A37B9 PUSH1 0xE9 SHL PUSH1 0x40 DUP4 ADD MSTORE PUSH3 0x4D8 JUMP JUMPDEST POP PUSH3 0x1FF PUSH3 0x18B PUSH3 0x297 JUMP JUMPDEST PUSH1 0x17 DUP2 MSTORE PUSH32 0x4A7572697364696374696F6E20436F6D706C69616E6365000000000000000000 DUP4 DUP3 ADD MSTORE PUSH3 0x1BE PUSH3 0x2B7 JUMP JUMPDEST SWAP1 PUSH1 0x28 DUP3 MSTORE PUSH32 0x4D65657473207370656369666963206A7572697364696374696F6E2072657175 DUP5 DUP4 ADD MSTORE PUSH8 0x6972656D656E7473 PUSH1 0xC0 SHL PUSH1 0x40 DUP4 ADD MSTORE PUSH3 0x4D8 JUMP JUMPDEST POP PUSH3 0x20A PUSH3 0x297 JUMP JUMPDEST PUSH1 0x15 DUP2 MSTORE PUSH32 0x47656E6572616C205175616C696669636174696F6E0000000000000000000000 DUP3 DUP3 ADD MSTORE PUSH32 0x47656E6572616C20696E766573746D656E74207175616C696669636174696F6E PUSH3 0x25E PUSH3 0x297 JUMP JUMPDEST SWAP3 DUP1 DUP5 MSTORE DUP4 ADD MSTORE PUSH3 0x4D8 JUMP JUMPDEST POP PUSH1 0x40 MLOAD PUSH2 0x1E62 SWAP1 DUP2 PUSH3 0x8F0 DUP3 CODECOPY RETURN JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x41 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH1 0x40 DUP1 MLOAD SWAP2 SWAP1 DUP3 ADD PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP2 GT DUP4 DUP3 LT OR PUSH3 0x281 JUMPI PUSH1 0x40 MSTORE JUMP JUMPDEST PUSH1 0x40 MLOAD SWAP1 PUSH1 0x60 DUP3 ADD PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP2 GT DUP4 DUP3 LT OR PUSH3 0x281 JUMPI PUSH1 0x40 MSTORE JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH1 0x0 DUP2 DUP2 MSTORE PUSH32 0xAD3228B676F7D3CD4284A5443F17F1962B36E491B30A40B2405849E597BA5FB5 PUSH1 0x20 MSTORE PUSH1 0x40 DUP2 KECCAK256 SLOAD SWAP1 SWAP2 SWAP1 PUSH1 0xFF AND PUSH3 0x353 JUMPI DUP2 DUP1 MSTORE DUP2 PUSH1 0x20 MSTORE PUSH1 0x40 DUP3 KECCAK256 DUP2 DUP4 MSTORE PUSH1 0x20 MSTORE PUSH1 0x40 DUP3 KECCAK256 PUSH1 0x1 PUSH1 0xFF NOT DUP3 SLOAD AND OR SWAP1 SSTORE CALLER SWAP2 PUSH1 0x0 DUP1 MLOAD PUSH1 0x20 PUSH3 0x2752 DUP4 CODECOPY DUP2 MLOAD SWAP2 MSTORE DUP2 DUP1 LOG4 PUSH1 0x1 SWAP1 JUMP JUMPDEST POP SWAP1 JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH1 0x0 DUP2 DUP2 MSTORE PUSH32 0x52A9CC15D02F8D37B4D6F72E1EF19BC07BF335BB0FE54C472AFEF4CE2D4549F0 PUSH1 0x20 MSTORE PUSH1 0x40 DUP2 KECCAK256 SLOAD SWAP1 SWAP2 SWAP1 PUSH32 0xDF6BC58AF35302F8541FB5D0DA6C4472BE7FC3A416BF34042D13743AC0A50915 SWAP1 PUSH1 0xFF AND PUSH3 0x3F4 JUMPI DUP1 DUP4 MSTORE DUP3 PUSH1 0x20 MSTORE PUSH1 0x40 DUP4 KECCAK256 DUP3 DUP5 MSTORE PUSH1 0x20 MSTORE PUSH1 0x40 DUP4 KECCAK256 PUSH1 0x1 PUSH1 0xFF NOT DUP3 SLOAD AND OR SWAP1 SSTORE PUSH1 0x0 DUP1 MLOAD PUSH1 0x20 PUSH3 0x2752 DUP4 CODECOPY DUP2 MLOAD SWAP2 MSTORE CALLER SWAP4 DUP1 LOG4 PUSH1 0x1 SWAP1 JUMP JUMPDEST POP POP SWAP1 JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH1 0x0 DUP2 DUP2 MSTORE PUSH32 0x970F46A7325A35D7F007EC7EF5360D7D9312777C0FF83802A1EA344634A43D30 PUSH1 0x20 MSTORE PUSH1 0x40 DUP2 KECCAK256 SLOAD SWAP1 SWAP2 SWAP1 PUSH32 0xA9EF30CACD3C540E9D2B47058CE383D745F3E72389B3EA103DB79727BA9CCE89 SWAP1 PUSH1 0xFF AND PUSH3 0x3F4 JUMPI DUP1 DUP4 MSTORE DUP3 PUSH1 0x20 MSTORE PUSH1 0x40 DUP4 KECCAK256 DUP3 DUP5 MSTORE PUSH1 0x20 MSTORE PUSH1 0x40 DUP4 KECCAK256 PUSH1 0x1 PUSH1 0xFF NOT DUP3 SLOAD AND OR SWAP1 SSTORE PUSH1 0x0 DUP1 MLOAD PUSH1 0x20 PUSH3 0x2752 DUP4 CODECOPY DUP2 MLOAD SWAP2 MSTORE CALLER SWAP4 DUP1 LOG4 PUSH1 0x1 SWAP1 JUMP JUMPDEST SWAP2 SWAP1 DUP3 MLOAD SWAP3 DUP4 DUP3 MSTORE PUSH1 0x0 JUMPDEST DUP5 DUP2 LT PUSH3 0x4C3 JUMPI POP POP DUP3 PUSH1 0x0 PUSH1 0x20 DUP1 SWAP5 SWAP6 DUP5 ADD ADD MSTORE PUSH1 0x1F DUP1 NOT SWAP2 ADD AND ADD ADD SWAP1 JUMP JUMPDEST PUSH1 0x20 DUP2 DUP4 ADD DUP2 ADD MLOAD DUP5 DUP4 ADD DUP3 ADD MSTORE ADD PUSH3 0x4A1 JUMP JUMPDEST PUSH1 0x1 SLOAD SWAP2 SWAP1 PUSH1 0x0 NOT DUP4 EQ PUSH3 0x8D9 JUMPI PUSH1 0x1 DUP4 DUP2 ADD SWAP1 SSTORE PUSH1 0x40 DUP1 MLOAD PUSH1 0xC0 DUP2 ADD PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP2 GT DUP3 DUP3 LT OR PUSH3 0x281 JUMPI DUP3 MSTORE DUP5 DUP2 MSTORE PUSH1 0x20 DUP2 ADD DUP4 DUP2 MSTORE DUP5 DUP4 DUP4 ADD MSTORE CALLER PUSH1 0x60 DUP4 ADD MSTORE TIMESTAMP PUSH1 0x80 DUP4 ADD MSTORE PUSH1 0x1 PUSH1 0xA0 DUP4 ADD MSTORE DUP6 PUSH1 0x0 MSTORE PUSH1 0x2 PUSH1 0x20 MSTORE DUP3 PUSH1 0x0 KECCAK256 SWAP1 DUP3 MLOAD DUP3 SSTORE MLOAD DUP1 MLOAD SWAP1 PUSH1 0x1 DUP1 PUSH1 0x40 SHL SUB DUP3 GT PUSH3 0x281 JUMPI PUSH1 0x1 DUP4 ADD SLOAD SWAP1 PUSH1 0x1 DUP3 DUP2 SHR SWAP3 AND DUP1 ISZERO PUSH3 0x8CE JUMPI JUMPDEST PUSH1 0x20 DUP4 LT EQ PUSH3 0x7CF JUMPI DUP2 PUSH1 0x1F DUP5 SWAP4 GT PUSH3 0x873 JUMPI JUMPDEST POP PUSH1 0x20 SWAP1 PUSH1 0x1F DUP4 GT PUSH1 0x1 EQ PUSH3 0x7FC JUMPI PUSH1 0x0 SWAP3 PUSH3 0x7F0 JUMPI JUMPDEST POP POP DUP2 PUSH1 0x1 SHL SWAP2 PUSH1 0x0 NOT SWAP1 PUSH1 0x3 SHL SHR NOT AND OR PUSH1 0x1 DUP3 ADD SSTORE JUMPDEST DUP2 DUP4 ADD MLOAD DUP1 MLOAD PUSH1 0x2 DUP4 ADD SWAP2 PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP3 GT PUSH3 0x281 JUMPI DUP3 SLOAD PUSH1 0x1 DUP2 DUP2 SHR SWAP2 AND DUP1 ISZERO PUSH3 0x7E5 JUMPI JUMPDEST PUSH1 0x20 DUP3 LT EQ PUSH3 0x7CF JUMPI PUSH1 0x1F DUP2 GT PUSH3 0x782 JUMPI JUMPDEST POP PUSH1 0x20 SWAP1 PUSH1 0x1F DUP4 GT PUSH1 0x1 EQ PUSH3 0x70F JUMPI SWAP2 DUP1 PUSH1 0x5 SWAP5 SWAP3 PUSH1 0xA0 SWAP7 SWAP5 PUSH1 0x0 SWAP3 PUSH3 0x703 JUMPI JUMPDEST POP POP DUP2 PUSH1 0x1 SHL SWAP2 PUSH1 0x0 NOT SWAP1 PUSH1 0x3 SHL SHR NOT AND OR SWAP1 SSTORE JUMPDEST PUSH1 0x3 DUP2 ADD PUSH1 0x1 DUP1 DUP6 SHL SUB PUSH1 0x60 DUP7 ADD MLOAD AND PUSH1 0x1 DUP1 DUP7 SHL SUB NOT DUP3 SLOAD AND OR SWAP1 SSTORE PUSH1 0x80 DUP5 ADD MLOAD PUSH1 0x4 DUP3 ADD SSTORE ADD SWAP2 ADD MLOAD ISZERO ISZERO PUSH1 0xFF DUP1 NOT DUP4 SLOAD AND SWAP2 AND OR SWAP1 SSTORE CALLER PUSH1 0x0 MSTORE PUSH1 0x3 PUSH1 0x20 MSTORE DUP1 PUSH1 0x0 KECCAK256 SWAP2 DUP3 SLOAD SWAP2 PUSH9 0x10000000000000000 DUP4 LT ISZERO PUSH3 0x281 JUMPI PUSH1 0x1 DUP4 ADD DUP1 DUP6 SSTORE DUP4 LT ISZERO PUSH3 0x6ED JUMPI DUP6 PUSH32 0xB32C7C206740E72055364DB6657B89F760889A8633965910821D462A0E21682B SWAP4 DUP2 SWAP6 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 ADD SSTORE PUSH3 0x6E7 PUSH3 0x6D7 DUP3 MLOAD SWAP4 DUP4 DUP6 SWAP5 DUP6 MSTORE DUP5 ADD SWAP1 PUSH3 0x496 JUMP JUMPDEST DUP3 DUP2 SUB PUSH1 0x20 DUP5 ADD MSTORE CALLER SWAP7 PUSH3 0x496 JUMP JUMPDEST SUB SWAP1 LOG3 SWAP1 JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x32 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST ADD MLOAD SWAP1 POP CODESIZE DUP1 PUSH3 0x60F JUMP JUMPDEST SWAP1 PUSH1 0x1F NOT DUP4 AND SWAP2 DUP5 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP3 PUSH1 0x0 JUMPDEST DUP2 DUP2 LT PUSH3 0x769 JUMPI POP SWAP3 PUSH1 0x1 SWAP3 DUP6 SWAP3 PUSH1 0xA0 SWAP9 SWAP7 PUSH1 0x5 SWAP9 SWAP7 LT PUSH3 0x74F JUMPI JUMPDEST POP POP POP DUP2 SHL ADD SWAP1 SSTORE PUSH3 0x624 JUMP JUMPDEST ADD MLOAD PUSH1 0x0 NOT PUSH1 0xF8 DUP5 PUSH1 0x3 SHL AND SHR NOT AND SWAP1 SSTORE CODESIZE DUP1 DUP1 PUSH3 0x741 JUMP JUMPDEST SWAP3 SWAP4 PUSH1 0x20 PUSH1 0x1 DUP2 SWAP3 DUP8 DUP7 ADD MLOAD DUP2 SSTORE ADD SWAP6 ADD SWAP4 ADD PUSH3 0x723 JUMP JUMPDEST DUP4 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 PUSH1 0x1F DUP5 ADD PUSH1 0x5 SHR DUP2 ADD SWAP2 PUSH1 0x20 DUP6 LT PUSH3 0x7C4 JUMPI JUMPDEST PUSH1 0x1F ADD PUSH1 0x5 SHR ADD SWAP1 JUMPDEST DUP2 DUP2 LT PUSH3 0x7B7 JUMPI POP PUSH3 0x5EC JUMP JUMPDEST PUSH1 0x0 DUP2 SSTORE PUSH1 0x1 ADD PUSH3 0x7A8 JUMP JUMPDEST SWAP1 SWAP2 POP DUP2 SWAP1 PUSH3 0x79F JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x22 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST SWAP1 PUSH1 0x7F AND SWAP1 PUSH3 0x5D8 JUMP JUMPDEST ADD MLOAD SWAP1 POP CODESIZE DUP1 PUSH3 0x595 JUMP JUMPDEST SWAP3 POP PUSH1 0x1 DUP5 ADD PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x0 SWAP4 JUMPDEST PUSH1 0x1F NOT DUP5 AND DUP6 LT PUSH3 0x857 JUMPI PUSH1 0x1 SWAP5 POP DUP4 PUSH1 0x1F NOT DUP2 AND LT PUSH3 0x83D JUMPI JUMPDEST POP POP POP DUP2 SHL ADD PUSH1 0x1 DUP3 ADD SSTORE PUSH3 0x5AD JUMP JUMPDEST ADD MLOAD PUSH1 0x0 NOT PUSH1 0xF8 DUP5 PUSH1 0x3 SHL AND SHR NOT AND SWAP1 SSTORE CODESIZE DUP1 DUP1 PUSH3 0x82C JUMP JUMPDEST DUP2 DUP2 ADD MLOAD DUP4 SSTORE PUSH1 0x20 SWAP5 DUP6 ADD SWAP5 PUSH1 0x1 SWAP1 SWAP4 ADD SWAP3 SWAP1 SWAP2 ADD SWAP1 PUSH3 0x80F JUMP JUMPDEST SWAP1 SWAP2 POP PUSH1 0x1 DUP5 ADD PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 PUSH1 0x1F DUP5 ADD PUSH1 0x5 SHR DUP2 ADD SWAP2 PUSH1 0x20 DUP6 LT PUSH3 0x8C3 JUMPI JUMPDEST SWAP1 PUSH1 0x1F DUP6 SWAP5 SWAP4 SWAP3 ADD PUSH1 0x5 SHR ADD SWAP1 JUMPDEST DUP2 DUP2 LT PUSH3 0x8B3 JUMPI POP PUSH3 0x57C JUMP JUMPDEST PUSH1 0x0 DUP2 SSTORE DUP5 SWAP4 POP PUSH1 0x1 ADD PUSH3 0x8A4 JUMP JUMPDEST SWAP1 SWAP2 POP DUP2 SWAP1 PUSH3 0x896 JUMP JUMPDEST SWAP2 PUSH1 0x7F AND SWAP2 PUSH3 0x566 JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT INVALID PUSH1 0x80 DUP1 PUSH1 0x40 MSTORE PUSH1 0x4 CALLDATASIZE LT ISZERO PUSH2 0x13 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH1 0x0 CALLDATALOAD PUSH1 0xE0 SHR SWAP1 DUP2 PUSH4 0x1FFC9A7 EQ PUSH2 0x157B JUMPI POP DUP1 PUSH4 0xEC2D8FE EQ PUSH2 0x14C7 JUMPI DUP1 PUSH4 0x1D541EA2 EQ PUSH2 0x147F JUMPI DUP1 PUSH4 0x23F86802 EQ PUSH2 0x13BD JUMPI DUP1 PUSH4 0x248A9CA3 EQ PUSH2 0x138E JUMPI DUP1 PUSH4 0x2C52E7AB EQ PUSH2 0x135D JUMPI DUP1 PUSH4 0x2E0D3857 EQ PUSH2 0x1006 JUMPI DUP1 PUSH4 0x2F2FF15D EQ PUSH2 0xFC7 JUMPI DUP1 PUSH4 0x36568ABE EQ PUSH2 0xF80 JUMPI DUP1 PUSH4 0x60CF25E2 EQ PUSH2 0xF1D JUMPI DUP1 PUSH4 0x8CCF00FB EQ PUSH2 0xEC4 JUMPI DUP1 PUSH4 0x91D14854 EQ PUSH2 0xE77 JUMPI DUP1 PUSH4 0x948DB1B8 EQ PUSH2 0xE36 JUMPI DUP1 PUSH4 0xA044E70F EQ PUSH2 0xDFB JUMPI DUP1 PUSH4 0xA217FDDF EQ PUSH2 0xDDF JUMPI DUP1 PUSH4 0xA335ACA8 EQ PUSH2 0xD0F JUMPI DUP1 PUSH4 0xB8233248 EQ PUSH2 0x949 JUMPI DUP1 PUSH4 0xC1EDCDC1 EQ PUSH2 0x89E JUMPI DUP1 PUSH4 0xD547741F EQ PUSH2 0x85D JUMPI DUP1 PUSH4 0xDD63E196 EQ PUSH2 0x24C JUMPI DUP1 PUSH4 0xE60ECDD2 EQ PUSH2 0x211 JUMPI PUSH4 0xEFF0F592 EQ PUSH2 0x111 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x4 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x4 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 DUP1 SLOAD SWAP1 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB SWAP1 DUP2 PUSH1 0x1 DUP3 ADD SLOAD AND SWAP2 PUSH1 0x2 DUP3 ADD SLOAD AND SWAP3 PUSH1 0x40 MLOAD SWAP2 PUSH2 0x168 DUP4 PUSH2 0x161 DUP2 PUSH1 0x3 DUP6 ADD PUSH2 0x16CF JUMP JUMPDEST SUB DUP5 PUSH2 0x179C JUMP JUMPDEST PUSH2 0x1F5 PUSH1 0x40 MLOAD PUSH2 0x186 DUP2 PUSH2 0x17F DUP2 PUSH1 0x4 DUP8 ADD PUSH2 0x16CF JUMP JUMPDEST SUB DUP3 PUSH2 0x179C JUMP JUMPDEST PUSH2 0x1E7 PUSH1 0x40 MLOAD SWAP2 PUSH2 0x19E DUP4 PUSH2 0x161 DUP2 PUSH1 0x5 DUP10 ADD PUSH2 0x16CF JUMP JUMPDEST PUSH2 0x1D9 PUSH1 0x6 DUP7 ADD SLOAD SWAP8 PUSH1 0xFF PUSH1 0x8 PUSH1 0x7 DUP10 ADD SLOAD SWAP9 ADD SLOAD AND SWAP8 PUSH1 0x40 MLOAD SWAP12 DUP13 SWAP12 PUSH2 0x120 SWAP3 DUP14 MSTORE PUSH1 0x20 DUP14 ADD MSTORE PUSH1 0x40 DUP13 ADD MSTORE DUP1 PUSH1 0x60 DUP13 ADD MSTORE DUP11 ADD SWAP1 PUSH2 0x1655 JUMP JUMPDEST SWAP1 DUP9 DUP3 SUB PUSH1 0x80 DUP11 ADD MSTORE PUSH2 0x1655 JUMP JUMPDEST SWAP1 DUP7 DUP3 SUB PUSH1 0xA0 DUP9 ADD MSTORE PUSH2 0x1655 JUMP JUMPDEST SWAP3 PUSH1 0xC0 DUP6 ADD MSTORE PUSH1 0xE0 DUP5 ADD MSTORE ISZERO ISZERO PUSH2 0x100 DUP4 ADD MSTORE SUB SWAP1 RETURN JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x20 PUSH1 0x40 MLOAD PUSH32 0xA9EF30CACD3C540E9D2B47058CE383D745F3E72389B3EA103DB79727BA9CCE89 DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0xC0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH2 0x265 PUSH2 0x15CE JUMP JUMPDEST PUSH1 0x44 CALLDATALOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP2 GT PUSH2 0x20C JUMPI PUSH2 0x284 SWAP1 CALLDATASIZE SWAP1 PUSH1 0x4 ADD PUSH2 0x15FA JUMP JUMPDEST SWAP1 SWAP2 PUSH1 0x64 CALLDATALOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP2 GT PUSH2 0x20C JUMPI PUSH2 0x2A5 SWAP1 CALLDATASIZE SWAP1 PUSH1 0x4 ADD PUSH2 0x15FA JUMP JUMPDEST SWAP2 SWAP1 SWAP4 PUSH1 0x84 CALLDATALOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP2 GT PUSH2 0x20C JUMPI PUSH2 0x2C7 SWAP1 CALLDATASIZE SWAP1 PUSH1 0x4 ADD PUSH2 0x15FA JUMP JUMPDEST SWAP6 SWAP1 SWAP4 PUSH2 0x2D2 PUSH2 0x1C99 JUMP JUMPDEST PUSH1 0x24 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x2 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 SLOAD ISZERO PUSH2 0x80E JUMPI PUSH1 0x24 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x2 PUSH1 0x20 MSTORE PUSH1 0xFF PUSH1 0x5 PUSH1 0x40 PUSH1 0x0 KECCAK256 ADD SLOAD AND ISZERO PUSH2 0x7BB JUMPI PUSH1 0x40 MLOAD PUSH12 0xFFFFFFFFFFFFFFFFFFFFFFFF NOT DUP6 PUSH1 0x60 SHL AND PUSH1 0x20 DUP3 ADD MSTORE PUSH1 0x24 CALLDATALOAD PUSH1 0x34 DUP3 ADD MSTORE CALLER PUSH1 0x60 SHL PUSH1 0x54 DUP3 ADD MSTORE TIMESTAMP PUSH1 0x68 DUP3 ADD MSTORE DUP2 DUP4 PUSH1 0x88 DUP4 ADD CALLDATACOPY PUSH2 0x35A PUSH1 0x88 DUP3 DUP5 DUP2 ADD PUSH1 0x0 DUP4 DUP3 ADD MSTORE SUB PUSH1 0x68 DUP2 ADD DUP5 MSTORE ADD DUP3 PUSH2 0x179C JUMP JUMPDEST PUSH1 0x20 DUP2 MLOAD SWAP2 ADD KECCAK256 SWAP6 PUSH2 0x3A8 PUSH1 0x40 MLOAD SWAP4 PUSH2 0x372 DUP6 PUSH2 0x1780 JUMP JUMPDEST PUSH1 0x24 CALLDATALOAD DUP6 MSTORE PUSH2 0x399 PUSH1 0x20 DUP7 ADD SWAP7 CALLER DUP9 MSTORE PUSH1 0x40 DUP8 ADD SWAP5 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB DUP11 AND DUP7 MSTORE CALLDATASIZE SWAP2 PUSH2 0x1936 JUMP JUMPDEST SWAP4 PUSH1 0x60 DUP7 ADD SWAP5 DUP6 MSTORE CALLDATASIZE SWAP2 PUSH2 0x1936 JUMP JUMPDEST PUSH1 0x80 DUP5 ADD MSTORE PUSH2 0x3B8 CALLDATASIZE DUP10 DUP9 PUSH2 0x1936 JUMP JUMPDEST PUSH1 0xA0 DUP5 ADD MSTORE TIMESTAMP PUSH1 0xC0 DUP5 ADD MSTORE PUSH1 0xA4 CALLDATALOAD PUSH1 0xE0 DUP5 ADD MSTORE PUSH1 0x0 PUSH2 0x100 DUP5 ADD DUP2 SWAP1 MSTORE DUP8 DUP2 MSTORE PUSH1 0x4 PUSH1 0x20 MSTORE PUSH1 0x40 SWAP1 KECCAK256 DUP4 MLOAD DUP2 SSTORE SWAP4 MLOAD PUSH1 0x1 DUP6 ADD DUP1 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB NOT SWAP1 DUP2 AND PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP4 DUP5 AND OR SWAP1 SWAP2 SSTORE SWAP2 MLOAD PUSH1 0x2 DUP7 ADD DUP1 SLOAD SWAP1 SWAP4 AND SWAP2 AND OR SWAP1 SSTORE MLOAD DUP1 MLOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP3 GT PUSH2 0x637 JUMPI PUSH2 0x444 DUP3 PUSH2 0x43B PUSH1 0x3 DUP8 ADD SLOAD PUSH2 0x1695 JUMP JUMPDEST PUSH1 0x3 DUP8 ADD PUSH2 0x1C22 JUMP JUMPDEST PUSH1 0x20 SWAP1 PUSH1 0x1F DUP4 GT PUSH1 0x1 EQ PUSH2 0x749 JUMPI PUSH2 0x475 SWAP3 SWAP2 PUSH1 0x0 SWAP2 DUP4 PUSH2 0x6D0 JUMPI JUMPDEST POP POP DUP2 PUSH1 0x1 SHL SWAP2 PUSH1 0x0 NOT SWAP1 PUSH1 0x3 SHL SHR NOT AND OR SWAP1 JUMP JUMPDEST PUSH1 0x3 DUP4 ADD SSTORE JUMPDEST PUSH1 0x80 DUP2 ADD MLOAD DUP1 MLOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP3 GT PUSH2 0x637 JUMPI PUSH2 0x4AB DUP3 PUSH2 0x4A2 PUSH1 0x4 DUP8 ADD SLOAD PUSH2 0x1695 JUMP JUMPDEST PUSH1 0x4 DUP8 ADD PUSH2 0x1C22 JUMP JUMPDEST PUSH1 0x20 SWAP1 PUSH1 0x1F DUP4 GT PUSH1 0x1 EQ PUSH2 0x6DB JUMPI PUSH2 0x4DB SWAP3 SWAP2 PUSH1 0x0 SWAP2 DUP4 PUSH2 0x6D0 JUMPI POP POP DUP2 PUSH1 0x1 SHL SWAP2 PUSH1 0x0 NOT SWAP1 PUSH1 0x3 SHL SHR NOT AND OR SWAP1 JUMP JUMPDEST PUSH1 0x4 DUP4 ADD SSTORE JUMPDEST PUSH1 0xA0 DUP2 ADD MLOAD DUP1 MLOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP3 GT PUSH2 0x637 JUMPI PUSH2 0x511 DUP3 PUSH2 0x508 PUSH1 0x5 DUP8 ADD SLOAD PUSH2 0x1695 JUMP JUMPDEST PUSH1 0x5 DUP8 ADD PUSH2 0x1C22 JUMP JUMPDEST PUSH1 0x20 SWAP1 PUSH1 0x1F DUP4 GT PUSH1 0x1 EQ PUSH2 0x658 JUMPI SWAP3 PUSH2 0x54C DUP4 PUSH1 0x8 SWAP5 PUSH2 0x100 SWAP5 PUSH2 0x57D SWAP9 SWAP8 PUSH1 0x0 SWAP3 PUSH2 0x64D JUMPI POP POP DUP2 PUSH1 0x1 SHL SWAP2 PUSH1 0x0 NOT SWAP1 PUSH1 0x3 SHL SHR NOT AND OR SWAP1 JUMP JUMPDEST PUSH1 0x5 DUP6 ADD SSTORE JUMPDEST PUSH1 0xC0 DUP2 ADD MLOAD PUSH1 0x6 DUP6 ADD SSTORE PUSH1 0xE0 DUP2 ADD MLOAD PUSH1 0x7 DUP6 ADD SSTORE ADD MLOAD ISZERO ISZERO SWAP2 ADD SWAP1 PUSH1 0xFF DUP1 NOT DUP4 SLOAD AND SWAP2 ISZERO ISZERO AND OR SWAP1 SSTORE JUMP JUMPDEST PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB DUP2 AND PUSH1 0x0 MSTORE PUSH1 0x5 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 PUSH1 0x24 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 SWAP4 DUP5 SLOAD SWAP5 PUSH1 0x1 PUSH1 0x40 SHL DUP7 LT ISZERO PUSH2 0x637 JUMPI PUSH2 0x62C DUP6 SWAP5 DUP6 PUSH2 0x604 PUSH2 0x5ED DUP11 PUSH32 0x294C37B9B884B1967835179D3C147BDDC32A4B714710B0885CC4D55C4640FBF1 SWAP7 PUSH1 0x1 PUSH1 0x20 SWAP14 ADD DUP2 SSTORE PUSH2 0x1627 JUMP JUMPDEST DUP2 SWAP4 SWAP2 SLOAD SWAP1 PUSH1 0x3 SHL SWAP2 DUP3 SHL SWAP2 PUSH1 0x0 NOT SWAP1 SHL NOT AND OR SWAP1 JUMP JUMPDEST SWAP1 SSTORE PUSH1 0x40 MLOAD SWAP2 DUP3 SWAP2 CALLER DUP4 MSTORE PUSH1 0x40 DUP11 DUP5 ADD MSTORE PUSH1 0x24 CALLDATALOAD SWAP7 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB AND SWAP6 PUSH1 0x40 DUP5 ADD SWAP2 PUSH2 0x1C78 JUMP JUMPDEST SUB SWAP1 LOG4 PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x41 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST ADD MLOAD SWAP1 POP DUP13 DUP1 PUSH2 0x460 JUMP JUMPDEST SWAP1 PUSH1 0x1F NOT DUP4 AND SWAP2 PUSH1 0x5 DUP7 ADD PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP3 PUSH1 0x0 JUMPDEST DUP2 DUP2 LT PUSH2 0x6B8 JUMPI POP SWAP4 PUSH2 0x100 SWAP4 PUSH2 0x57D SWAP8 SWAP7 SWAP4 PUSH1 0x1 SWAP4 DUP4 PUSH1 0x8 SWAP9 LT PUSH2 0x69F JUMPI JUMPDEST POP POP POP DUP2 SHL ADD PUSH1 0x5 DUP6 ADD SSTORE PUSH2 0x552 JUMP JUMPDEST ADD MLOAD PUSH1 0x0 NOT PUSH1 0xF8 DUP5 PUSH1 0x3 SHL AND SHR NOT AND SWAP1 SSTORE DUP12 DUP1 DUP1 PUSH2 0x68F JUMP JUMPDEST SWAP3 SWAP4 PUSH1 0x20 PUSH1 0x1 DUP2 SWAP3 DUP8 DUP7 ADD MLOAD DUP2 SSTORE ADD SWAP6 ADD SWAP4 ADD PUSH2 0x66F JUMP JUMPDEST ADD MLOAD SWAP1 POP DUP10 DUP1 PUSH2 0x460 JUMP JUMPDEST SWAP1 PUSH1 0x1F NOT DUP4 AND SWAP2 PUSH1 0x4 DUP7 ADD PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP3 PUSH1 0x0 JUMPDEST DUP2 DUP2 LT PUSH2 0x731 JUMPI POP SWAP1 DUP5 PUSH1 0x1 SWAP6 SWAP5 SWAP4 SWAP3 LT PUSH2 0x718 JUMPI JUMPDEST POP POP POP DUP2 SHL ADD PUSH1 0x4 DUP4 ADD SSTORE PUSH2 0x4E1 JUMP JUMPDEST ADD MLOAD PUSH1 0x0 NOT PUSH1 0xF8 DUP5 PUSH1 0x3 SHL AND SHR NOT AND SWAP1 SSTORE DUP9 DUP1 DUP1 PUSH2 0x708 JUMP JUMPDEST SWAP3 SWAP4 PUSH1 0x20 PUSH1 0x1 DUP2 SWAP3 DUP8 DUP7 ADD MLOAD DUP2 SSTORE ADD SWAP6 ADD SWAP4 ADD PUSH2 0x6F2 JUMP JUMPDEST SWAP2 SWAP1 PUSH1 0x3 DUP6 ADD PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x0 SWAP4 JUMPDEST PUSH1 0x1F NOT DUP5 AND DUP6 LT PUSH2 0x7A0 JUMPI PUSH1 0x1 SWAP5 POP DUP4 PUSH1 0x1F NOT DUP2 AND LT PUSH2 0x787 JUMPI JUMPDEST POP POP POP DUP2 SHL ADD PUSH1 0x3 DUP4 ADD SSTORE PUSH2 0x47B JUMP JUMPDEST ADD MLOAD PUSH1 0x0 NOT PUSH1 0xF8 DUP5 PUSH1 0x3 SHL AND SHR NOT AND SWAP1 SSTORE DUP9 DUP1 DUP1 PUSH2 0x777 JUMP JUMPDEST DUP2 DUP2 ADD MLOAD DUP4 SSTORE PUSH1 0x20 SWAP5 DUP6 ADD SWAP5 PUSH1 0x1 SWAP1 SWAP4 ADD SWAP3 SWAP1 SWAP2 ADD SWAP1 PUSH2 0x75C JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x25 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x436C61696D52656769737472793A20636C61696D207479706520697320696E61 PUSH1 0x44 DUP3 ADD MSTORE PUSH5 0x6374697665 PUSH1 0xD8 SHL PUSH1 0x64 DUP3 ADD MSTORE PUSH1 0x84 SWAP1 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x21 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x436C61696D52656769737472793A20696E76616C696420636C61696D20747970 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x65 PUSH1 0xF8 SHL PUSH1 0x64 DUP3 ADD MSTORE PUSH1 0x84 SWAP1 REVERT JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH2 0x89C PUSH1 0x4 CALLDATALOAD PUSH2 0x87C PUSH2 0x15E4 JUMP JUMPDEST SWAP1 DUP1 PUSH1 0x0 MSTORE PUSH1 0x0 PUSH1 0x20 MSTORE PUSH2 0x897 PUSH1 0x1 PUSH1 0x40 PUSH1 0x0 KECCAK256 ADD SLOAD PUSH2 0x1D13 JUMP JUMPDEST PUSH2 0x1DB7 JUMP JUMPDEST STOP JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x4 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x2 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 DUP1 SLOAD PUSH1 0x40 MLOAD SWAP2 PUSH2 0x8D6 DUP4 PUSH2 0x161 DUP2 PUSH1 0x1 DUP6 ADD PUSH2 0x16CF JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0x8EA DUP2 PUSH2 0x17F DUP2 PUSH1 0x2 DUP7 ADD PUSH2 0x16CF JUMP JUMPDEST PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB PUSH1 0x3 DUP4 ADD SLOAD AND SWAP2 PUSH2 0x933 PUSH1 0xFF PUSH1 0x5 PUSH1 0x4 DUP5 ADD SLOAD SWAP4 ADD SLOAD AND SWAP3 PUSH2 0x925 PUSH1 0x40 MLOAD SWAP8 DUP9 SWAP8 DUP9 MSTORE PUSH1 0xC0 PUSH1 0x20 DUP10 ADD MSTORE PUSH1 0xC0 DUP9 ADD SWAP1 PUSH2 0x1655 JUMP JUMPDEST SWAP1 DUP7 DUP3 SUB PUSH1 0x40 DUP9 ADD MSTORE PUSH2 0x1655 JUMP JUMPDEST SWAP3 PUSH1 0x60 DUP6 ADD MSTORE PUSH1 0x80 DUP5 ADD MSTORE ISZERO ISZERO PUSH1 0xA0 DUP4 ADD MSTORE SUB SWAP1 RETURN JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x80 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x24 CALLDATALOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP2 GT PUSH2 0x20C JUMPI PUSH2 0x979 SWAP1 CALLDATASIZE SWAP1 PUSH1 0x4 ADD PUSH2 0x15FA JUMP JUMPDEST SWAP1 PUSH1 0x44 CALLDATALOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP2 GT PUSH2 0x20C JUMPI PUSH2 0x999 SWAP1 CALLDATASIZE SWAP1 PUSH1 0x4 ADD PUSH2 0x15FA JUMP JUMPDEST SWAP1 SWAP2 PUSH1 0x64 CALLDATALOAD ISZERO ISZERO PUSH1 0x64 CALLDATALOAD SUB PUSH2 0x20C JUMPI PUSH1 0x4 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x2 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 DUP1 SLOAD ISZERO PUSH2 0xCB9 JUMPI PUSH1 0x3 DUP2 ADD SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND CALLER EQ DUP1 ISZERO PUSH2 0xC81 JUMPI JUMPDEST ISZERO PUSH2 0xC2C JUMPI PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP6 GT PUSH2 0x637 JUMPI PUSH2 0xA03 DUP6 PUSH2 0x9FA PUSH1 0x1 DUP5 ADD SLOAD PUSH2 0x1695 JUMP JUMPDEST PUSH1 0x1 DUP5 ADD PUSH2 0x1C22 JUMP JUMPDEST PUSH1 0x0 DUP6 PUSH1 0x1F DUP2 GT PUSH1 0x1 EQ PUSH2 0xBBF JUMPI DUP1 PUSH2 0xA32 SWAP3 PUSH1 0x0 SWAP2 PUSH2 0xBB4 JUMPI JUMPDEST POP DUP2 PUSH1 0x1 SHL SWAP2 PUSH1 0x0 NOT SWAP1 PUSH1 0x3 SHL SHR NOT AND OR SWAP1 JUMP JUMPDEST PUSH1 0x1 DUP3 ADD SSTORE JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP4 GT PUSH2 0x637 JUMPI PUSH2 0xA60 DUP4 PUSH2 0xA57 PUSH1 0x2 DUP5 ADD SLOAD PUSH2 0x1695 JUMP JUMPDEST PUSH1 0x2 DUP5 ADD PUSH2 0x1C22 JUMP JUMPDEST PUSH1 0x0 PUSH1 0x1F DUP5 GT PUSH1 0x1 EQ PUSH2 0xB18 JUMPI PUSH32 0xBEE22C24DB7F76BC89936193C155555CC1A2301094F46BBFA4ED0B7B5DD38D4B SWAP5 SWAP3 PUSH2 0xAD8 DUP6 SWAP4 PUSH2 0xABC DUP6 PUSH2 0xAF8 SWAP9 PUSH2 0xAEA SWAP7 PUSH1 0x0 SWAP2 PUSH2 0xB0D JUMPI POP DUP2 PUSH1 0x1 SHL SWAP2 PUSH1 0x0 NOT SWAP1 PUSH1 0x3 SHL SHR NOT AND OR SWAP1 JUMP JUMPDEST PUSH1 0x2 DUP3 ADD SSTORE JUMPDEST PUSH1 0x5 ADD DUP1 SLOAD PUSH1 0xFF NOT AND PUSH1 0xFF PUSH1 0x64 CALLDATALOAD ISZERO ISZERO AND OR SWAP1 SSTORE JUMP JUMPDEST PUSH1 0x40 MLOAD SWAP7 PUSH1 0x60 DUP9 MSTORE PUSH1 0x60 DUP9 ADD SWAP2 PUSH2 0x1C78 JUMP JUMPDEST SWAP2 DUP6 DUP4 SUB PUSH1 0x20 DUP8 ADD MSTORE PUSH2 0x1C78 JUMP JUMPDEST SWAP2 PUSH1 0x64 CALLDATALOAD ISZERO ISZERO PUSH1 0x40 DUP3 ADD MSTORE DUP1 PUSH1 0x4 CALLDATALOAD SWAP4 SUB SWAP1 LOG2 STOP JUMPDEST SWAP1 POP DUP9 ADD CALLDATALOAD DUP13 PUSH2 0xA1E JUMP JUMPDEST PUSH1 0x2 DUP3 ADD PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x0 JUMPDEST PUSH1 0x1F NOT DUP7 AND DUP2 LT PUSH2 0xB9C JUMPI POP SWAP3 PUSH2 0xAD8 DUP6 SWAP4 PUSH2 0xAEA SWAP4 PUSH32 0xBEE22C24DB7F76BC89936193C155555CC1A2301094F46BBFA4ED0B7B5DD38D4B SWAP9 SWAP7 PUSH2 0xAF8 SWAP9 PUSH1 0x1F NOT DUP2 AND LT PUSH2 0xB82 JUMPI JUMPDEST POP POP PUSH1 0x1 DUP6 DUP2 SHL ADD PUSH1 0x2 DUP3 ADD SSTORE PUSH2 0xAC2 JUMP JUMPDEST DUP8 ADD CALLDATALOAD PUSH1 0x0 NOT PUSH1 0x3 DUP9 SWAP1 SHL PUSH1 0xF8 AND SHR NOT AND SWAP1 SSTORE DUP10 DUP1 PUSH2 0xB70 JUMP JUMPDEST SWAP1 SWAP2 PUSH1 0x20 PUSH1 0x1 DUP2 SWAP3 DUP6 DUP11 ADD CALLDATALOAD DUP2 SSTORE ADD SWAP4 ADD SWAP2 ADD PUSH2 0xB28 JUMP JUMPDEST SWAP1 POP DUP5 ADD CALLDATALOAD DUP9 PUSH2 0xA1E JUMP JUMPDEST POP PUSH1 0x1 DUP3 ADD PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x0 JUMPDEST PUSH1 0x1F NOT DUP9 AND DUP2 LT PUSH2 0xC14 JUMPI POP DUP7 PUSH1 0x1F NOT DUP2 AND LT PUSH2 0xBFA JUMPI JUMPDEST POP POP PUSH1 0x1 DUP6 DUP2 SHL ADD PUSH1 0x1 DUP3 ADD SSTORE PUSH2 0xA38 JUMP JUMPDEST DUP4 ADD CALLDATALOAD PUSH1 0x0 NOT PUSH1 0x3 DUP9 SWAP1 SHL PUSH1 0xF8 AND SHR NOT AND SWAP1 SSTORE DUP6 DUP1 PUSH2 0xBE8 JUMP JUMPDEST SWAP1 SWAP2 PUSH1 0x20 PUSH1 0x1 DUP2 SWAP3 DUP6 DUP9 ADD CALLDATALOAD DUP2 SSTORE ADD SWAP4 ADD SWAP2 ADD PUSH2 0xBD0 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x27 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x436C61696D52656769737472793A206E6F7420617574686F72697A656420746F PUSH1 0x44 DUP3 ADD MSTORE PUSH7 0x20757064617465 PUSH1 0xC8 SHL PUSH1 0x64 DUP3 ADD MSTORE PUSH1 0x84 SWAP1 REVERT JUMPDEST POP CALLER PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH32 0xAD3228B676F7D3CD4284A5443F17F1962B36E491B30A40B2405849E597BA5FB5 PUSH1 0x20 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD PUSH1 0xFF AND PUSH2 0x9D6 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x28 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x436C61696D52656769737472793A20636C61696D207479706520646F6573206E PUSH1 0x44 DUP3 ADD MSTORE PUSH8 0x1BDD08195E1A5CDD PUSH1 0xC2 SHL PUSH1 0x64 DUP3 ADD MSTORE PUSH1 0x84 SWAP1 REVERT JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH2 0xD2E PUSH1 0x24 CALLDATALOAD PUSH1 0x4 CALLDATALOAD PUSH2 0x19A2 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH1 0x20 SWAP2 DUP3 DUP3 ADD SWAP3 DUP1 DUP4 MSTORE DUP2 MLOAD DUP1 SWAP5 MSTORE PUSH1 0x40 DUP4 ADD SWAP4 DUP2 PUSH1 0x40 DUP3 PUSH1 0x5 SHL DUP7 ADD ADD SWAP4 ADD SWAP2 PUSH1 0x0 SWAP6 JUMPDEST DUP3 DUP8 LT PUSH2 0xD63 JUMPI DUP6 DUP6 SUB DUP7 RETURN JUMPDEST SWAP1 SWAP2 SWAP3 SWAP4 DUP3 DUP1 PUSH1 0x1 SWAP3 PUSH1 0x3F NOT DUP10 DUP3 SUB ADD DUP6 MSTORE DUP8 MLOAD SWAP1 PUSH2 0xDA8 PUSH2 0xD96 PUSH1 0xC0 DUP5 MLOAD DUP5 MSTORE DUP6 DUP6 ADD MLOAD SWAP1 DUP1 DUP8 DUP7 ADD MSTORE DUP5 ADD SWAP1 PUSH2 0x1655 JUMP JUMPDEST PUSH1 0x40 DUP5 ADD MLOAD DUP4 DUP3 SUB PUSH1 0x40 DUP6 ADD MSTORE PUSH2 0x1655 JUMP JUMPDEST SWAP2 PUSH1 0x60 DUP7 DUP1 PUSH1 0xA0 SHL SUB DUP2 DUP4 ADD MLOAD AND SWAP1 DUP4 ADD MSTORE PUSH1 0x80 DUP1 DUP3 ADD MLOAD SWAP1 DUP4 ADD MSTORE PUSH1 0xA0 DUP1 SWAP2 ADD MLOAD ISZERO ISZERO SWAP2 ADD MSTORE SWAP7 ADD SWAP3 ADD SWAP7 ADD SWAP6 SWAP3 SWAP2 SWAP1 SWAP3 PUSH2 0xD56 JUMP JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x20 PUSH1 0x40 MLOAD PUSH1 0x0 DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x20 PUSH1 0x40 MLOAD PUSH32 0xDF6BC58AF35302F8541FB5D0DA6C4472BE7FC3A416BF34042D13743AC0A50915 DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x1 SLOAD PUSH1 0x0 NOT DUP2 ADD SWAP1 DUP2 GT PUSH2 0xE61 JUMPI PUSH1 0x20 SWAP1 PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH2 0xE90 PUSH2 0x15E4 JUMP JUMPDEST PUSH1 0x4 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x0 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB AND PUSH1 0x0 MSTORE PUSH1 0x20 MSTORE PUSH1 0x20 PUSH1 0xFF PUSH1 0x40 PUSH1 0x0 KECCAK256 SLOAD AND PUSH1 0x40 MLOAD SWAP1 ISZERO ISZERO DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH2 0xEDD PUSH2 0x15CE JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH1 0x3 PUSH1 0x20 MSTORE PUSH1 0x40 SWAP1 KECCAK256 DUP1 SLOAD PUSH1 0x24 CALLDATALOAD SWAP2 SWAP1 DUP3 LT ISZERO PUSH2 0x20C JUMPI PUSH1 0x20 SWAP2 PUSH2 0xF0E SWAP2 PUSH2 0x1627 JUMP JUMPDEST SWAP1 SLOAD SWAP1 PUSH1 0x3 SHL SHR PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x60 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH2 0xF36 PUSH2 0x15CE JUMP JUMPDEST PUSH1 0x44 CALLDATALOAD SWAP1 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB AND PUSH1 0x0 MSTORE PUSH1 0x5 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 PUSH1 0x24 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 DUP1 SLOAD DUP3 LT ISZERO PUSH2 0x20C JUMPI PUSH1 0x20 SWAP2 PUSH2 0xF72 SWAP2 PUSH2 0x1627 JUMP JUMPDEST SWAP1 SLOAD PUSH1 0x40 MLOAD SWAP2 PUSH1 0x3 SHL SHR DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH2 0xF99 PUSH2 0x15E4 JUMP JUMPDEST CALLER PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP3 AND SUB PUSH2 0xFB5 JUMPI PUSH2 0x89C SWAP1 PUSH1 0x4 CALLDATALOAD PUSH2 0x1DB7 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH4 0x334BD919 PUSH1 0xE1 SHL DUP2 MSTORE PUSH1 0x4 SWAP1 REVERT JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH2 0x89C PUSH1 0x4 CALLDATALOAD PUSH2 0xFE6 PUSH2 0x15E4 JUMP JUMPDEST SWAP1 DUP1 PUSH1 0x0 MSTORE PUSH1 0x0 PUSH1 0x20 MSTORE PUSH2 0x1001 PUSH1 0x1 PUSH1 0x40 PUSH1 0x0 KECCAK256 ADD SLOAD PUSH2 0x1D13 JUMP JUMPDEST PUSH2 0x1D39 JUMP JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x4 CALLDATALOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP2 GT PUSH2 0x20C JUMPI PUSH2 0x1036 SWAP1 CALLDATASIZE SWAP1 PUSH1 0x4 ADD PUSH2 0x15FA JUMP JUMPDEST PUSH1 0x24 CALLDATALOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP3 GT PUSH2 0x20C JUMPI PUSH2 0x106E PUSH2 0x105C PUSH2 0x1076 SWAP4 CALLDATASIZE SWAP1 PUSH1 0x4 ADD PUSH2 0x15FA JUMP JUMPDEST SWAP5 SWAP1 SWAP3 PUSH2 0x1067 PUSH2 0x1C99 JUMP JUMPDEST CALLDATASIZE SWAP2 PUSH2 0x1936 JUMP JUMPDEST SWAP3 CALLDATASIZE SWAP2 PUSH2 0x1936 JUMP JUMPDEST PUSH1 0x1 SLOAD SWAP1 PUSH2 0x1083 DUP3 PUSH2 0x197C JUMP JUMPDEST PUSH1 0x1 SSTORE PUSH1 0x40 MLOAD PUSH2 0x1092 DUP2 PUSH2 0x1765 JUMP JUMPDEST DUP3 DUP2 MSTORE PUSH1 0x20 DUP2 ADD SWAP1 DUP5 DUP3 MSTORE PUSH1 0x40 DUP2 ADD DUP4 DUP2 MSTORE PUSH1 0x60 DUP3 ADD CALLER DUP2 MSTORE PUSH1 0x80 DUP4 ADD SWAP2 TIMESTAMP DUP4 MSTORE PUSH1 0x1 PUSH1 0xA0 DUP6 ADD MSTORE DUP7 PUSH1 0x0 MSTORE PUSH1 0x2 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 SWAP5 DUP5 MLOAD DUP7 SSTORE MLOAD DUP1 MLOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP3 GT PUSH2 0x637 JUMPI PUSH2 0x10F9 DUP3 PUSH2 0x10F0 PUSH1 0x1 DUP11 ADD SLOAD PUSH2 0x1695 JUMP JUMPDEST PUSH1 0x1 DUP11 ADD PUSH2 0x1C22 JUMP JUMPDEST PUSH1 0x20 SWAP1 PUSH1 0x1F DUP4 GT PUSH1 0x1 EQ PUSH2 0x12EF JUMPI PUSH2 0x1129 SWAP3 SWAP2 PUSH1 0x0 SWAP2 DUP4 PUSH2 0x12E4 JUMPI POP POP DUP2 PUSH1 0x1 SHL SWAP2 PUSH1 0x0 NOT SWAP1 PUSH1 0x3 SHL SHR NOT AND OR SWAP1 JUMP JUMPDEST PUSH1 0x1 DUP7 ADD SSTORE JUMPDEST MLOAD DUP1 MLOAD PUSH1 0x2 DUP7 ADD SWAP2 PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP3 GT PUSH2 0x637 JUMPI PUSH2 0x1159 DUP3 PUSH2 0x1153 DUP6 SLOAD PUSH2 0x1695 JUMP JUMPDEST DUP6 PUSH2 0x1C22 JUMP JUMPDEST PUSH1 0x20 SWAP1 PUSH1 0x1F DUP4 GT PUSH1 0x1 EQ PUSH2 0x1270 JUMPI SWAP3 PUSH2 0x1196 DUP4 PUSH1 0x5 SWAP8 SWAP5 PUSH1 0xA0 SWAP8 SWAP5 PUSH2 0x11D9 SWAP12 SWAP11 SWAP8 PUSH1 0x0 SWAP3 PUSH2 0x1265 JUMPI POP POP DUP2 PUSH1 0x1 SHL SWAP2 PUSH1 0x0 NOT SWAP1 PUSH1 0x3 SHL SHR NOT AND OR SWAP1 JUMP JUMPDEST SWAP1 SSTORE JUMPDEST PUSH1 0x3 DUP7 ADD SWAP1 PUSH1 0x1 DUP1 DUP7 SHL SUB SWAP1 MLOAD AND PUSH12 0xFFFFFFFFFFFFFFFFFFFFFFFF DUP6 SHL DUP3 SLOAD AND OR SWAP1 SSTORE MLOAD PUSH1 0x4 DUP6 ADD SSTORE ADD MLOAD ISZERO ISZERO SWAP2 ADD SWAP1 PUSH1 0xFF DUP1 NOT DUP4 SLOAD AND SWAP2 ISZERO ISZERO AND OR SWAP1 SSTORE JUMP JUMPDEST CALLER PUSH1 0x0 MSTORE PUSH1 0x3 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 SWAP3 DUP4 SLOAD SWAP4 PUSH1 0x1 PUSH1 0x40 SHL DUP6 LT ISZERO PUSH2 0x637 JUMPI PUSH2 0x124C DUP5 SWAP3 DUP4 PUSH2 0x1233 PUSH2 0x5ED DUP10 PUSH32 0xB32C7C206740E72055364DB6657B89F760889A8633965910821D462A0E21682B SWAP7 PUSH1 0x1 PUSH1 0x20 SWAP13 ADD DUP2 SSTORE PUSH2 0x1627 JUMP JUMPDEST SWAP1 SSTORE PUSH2 0x125A PUSH1 0x40 MLOAD SWAP3 DUP4 SWAP3 PUSH1 0x40 DUP5 MSTORE PUSH1 0x40 DUP5 ADD SWAP1 PUSH2 0x1655 JUMP JUMPDEST DUP3 DUP2 SUB DUP10 DUP5 ADD MSTORE CALLER SWAP7 PUSH2 0x1655 JUMP JUMPDEST SUB SWAP1 LOG3 PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST ADD MLOAD SWAP1 POP DUP15 DUP1 PUSH2 0x460 JUMP JUMPDEST SWAP1 PUSH1 0x1F NOT DUP4 AND SWAP2 DUP5 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP3 PUSH1 0x0 JUMPDEST DUP2 DUP2 LT PUSH2 0x12CC JUMPI POP SWAP4 PUSH1 0xA0 SWAP7 SWAP4 PUSH2 0x11D9 SWAP11 SWAP10 SWAP7 SWAP4 PUSH1 0x1 SWAP4 DUP4 PUSH1 0x5 SWAP12 SWAP9 LT PUSH2 0x12B3 JUMPI JUMPDEST POP POP POP DUP2 SHL ADD SWAP1 SSTORE PUSH2 0x1199 JUMP JUMPDEST ADD MLOAD PUSH1 0x0 NOT PUSH1 0xF8 DUP5 PUSH1 0x3 SHL AND SHR NOT AND SWAP1 SSTORE DUP14 DUP1 DUP1 PUSH2 0x12A6 JUMP JUMPDEST SWAP3 SWAP4 PUSH1 0x20 PUSH1 0x1 DUP2 SWAP3 DUP8 DUP7 ADD MLOAD DUP2 SSTORE ADD SWAP6 ADD SWAP4 ADD PUSH2 0x1284 JUMP JUMPDEST ADD MLOAD SWAP1 POP DUP12 DUP1 PUSH2 0x460 JUMP JUMPDEST SWAP1 PUSH1 0x1F NOT DUP4 AND SWAP2 PUSH1 0x1 DUP10 ADD PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP3 PUSH1 0x0 JUMPDEST DUP2 DUP2 LT PUSH2 0x1345 JUMPI POP SWAP1 DUP5 PUSH1 0x1 SWAP6 SWAP5 SWAP4 SWAP3 LT PUSH2 0x132C JUMPI JUMPDEST POP POP POP DUP2 SHL ADD PUSH1 0x1 DUP7 ADD SSTORE PUSH2 0x112F JUMP JUMPDEST ADD MLOAD PUSH1 0x0 NOT PUSH1 0xF8 DUP5 PUSH1 0x3 SHL AND SHR NOT AND SWAP1 SSTORE DUP11 DUP1 DUP1 PUSH2 0x131C JUMP JUMPDEST SWAP3 SWAP4 PUSH1 0x20 PUSH1 0x1 DUP2 SWAP3 DUP8 DUP7 ADD MLOAD DUP2 SSTORE ADD SWAP6 ADD SWAP4 ADD PUSH2 0x1306 JUMP JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x20 PUSH2 0x1384 PUSH2 0x137B PUSH2 0x15CE JUMP JUMPDEST PUSH1 0x24 CALLDATALOAD SWAP1 PUSH2 0x17D1 JUMP JUMPDEST PUSH1 0x40 MLOAD SWAP1 ISZERO ISZERO DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x4 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x0 PUSH1 0x20 MSTORE PUSH1 0x20 PUSH1 0x1 PUSH1 0x40 PUSH1 0x0 KECCAK256 ADD SLOAD PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB PUSH2 0x13DE PUSH2 0x15CE JUMP JUMPDEST AND PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x5 DUP2 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 PUSH1 0x24 CALLDATALOAD PUSH1 0x0 MSTORE DUP2 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 PUSH1 0x40 MLOAD SWAP1 DUP2 DUP4 DUP3 SLOAD SWAP2 DUP3 DUP2 MSTORE ADD SWAP1 DUP2 SWAP3 PUSH1 0x0 MSTORE DUP5 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x0 JUMPDEST DUP7 DUP3 DUP3 LT PUSH2 0x146B JUMPI DUP7 DUP7 PUSH2 0x142B DUP3 DUP9 SUB DUP4 PUSH2 0x179C JUMP JUMPDEST PUSH1 0x40 MLOAD SWAP3 DUP4 SWAP3 DUP2 DUP5 ADD SWAP1 DUP3 DUP6 MSTORE MLOAD DUP1 SWAP2 MSTORE PUSH1 0x40 DUP5 ADD SWAP3 SWAP2 PUSH1 0x0 JUMPDEST DUP3 DUP2 LT PUSH2 0x1454 JUMPI POP POP POP POP SUB SWAP1 RETURN JUMPDEST DUP4 MLOAD DUP6 MSTORE DUP7 SWAP6 POP SWAP4 DUP2 ADD SWAP4 SWAP3 DUP2 ADD SWAP3 PUSH1 0x1 ADD PUSH2 0x1445 JUMP JUMPDEST DUP4 SLOAD DUP6 MSTORE SWAP1 SWAP4 ADD SWAP3 PUSH1 0x1 SWAP3 DUP4 ADD SWAP3 ADD PUSH2 0x1415 JUMP JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x4 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x2 PUSH1 0x20 MSTORE PUSH1 0x20 PUSH1 0x40 PUSH1 0x0 KECCAK256 DUP1 SLOAD ISZERO ISZERO SWAP1 DUP2 PUSH2 0x14B8 JUMPI JUMPDEST POP PUSH1 0x40 MLOAD SWAP1 ISZERO ISZERO DUP2 MSTORE RETURN JUMPDEST PUSH1 0xFF SWAP2 POP PUSH1 0x5 ADD SLOAD AND DUP3 PUSH2 0x14AD JUMP JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x20 DUP1 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x20C JUMPI PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB PUSH2 0x14E9 PUSH2 0x15CE JUMP JUMPDEST AND PUSH1 0x0 MSTORE PUSH1 0x3 DUP2 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 PUSH1 0x40 MLOAD SWAP1 DUP2 DUP4 DUP3 SLOAD SWAP2 DUP3 DUP2 MSTORE ADD SWAP1 DUP2 SWAP3 PUSH1 0x0 MSTORE DUP5 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x0 JUMPDEST DUP7 DUP3 DUP3 LT PUSH2 0x1567 JUMPI DUP7 DUP7 PUSH2 0x1527 DUP3 DUP9 SUB DUP4 PUSH2 0x179C JUMP JUMPDEST PUSH1 0x40 MLOAD SWAP3 DUP4 SWAP3 DUP2 DUP5 ADD SWAP1 DUP3 DUP6 MSTORE MLOAD DUP1 SWAP2 MSTORE PUSH1 0x40 DUP5 ADD SWAP3 SWAP2 PUSH1 0x0 JUMPDEST DUP3 DUP2 LT PUSH2 0x1550 JUMPI POP POP POP POP SUB SWAP1 RETURN JUMPDEST DUP4 MLOAD DUP6 MSTORE DUP7 SWAP6 POP SWAP4 DUP2 ADD SWAP4 SWAP3 DUP2 ADD SWAP3 PUSH1 0x1 ADD PUSH2 0x1541 JUMP JUMPDEST DUP4 SLOAD DUP6 MSTORE SWAP1 SWAP4 ADD SWAP3 PUSH1 0x1 SWAP3 DUP4 ADD SWAP3 ADD PUSH2 0x1511 JUMP JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x4 CALLDATALOAD SWAP1 PUSH4 0xFFFFFFFF PUSH1 0xE0 SHL DUP3 AND DUP1 SWAP3 SUB PUSH2 0x20C JUMPI PUSH1 0x20 SWAP2 PUSH4 0x7965DB0B PUSH1 0xE0 SHL DUP2 EQ SWAP1 DUP2 ISZERO PUSH2 0x15BD JUMPI JUMPDEST POP ISZERO ISZERO DUP2 MSTORE RETURN JUMPDEST PUSH4 0x1FFC9A7 PUSH1 0xE0 SHL EQ SWAP1 POP DUP4 PUSH2 0x15B6 JUMP JUMPDEST PUSH1 0x4 CALLDATALOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP3 AND DUP3 SUB PUSH2 0x20C JUMPI JUMP JUMPDEST PUSH1 0x24 CALLDATALOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP3 AND DUP3 SUB PUSH2 0x20C JUMPI JUMP JUMPDEST SWAP2 DUP2 PUSH1 0x1F DUP5 ADD SLT ISZERO PUSH2 0x20C JUMPI DUP3 CALLDATALOAD SWAP2 PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP4 GT PUSH2 0x20C JUMPI PUSH1 0x20 DUP4 DUP2 DUP7 ADD SWAP6 ADD ADD GT PUSH2 0x20C JUMPI JUMP JUMPDEST DUP1 SLOAD DUP3 LT ISZERO PUSH2 0x163F JUMPI PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 ADD SWAP1 PUSH1 0x0 SWAP1 JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x32 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST SWAP2 SWAP1 DUP3 MLOAD SWAP3 DUP4 DUP3 MSTORE PUSH1 0x0 JUMPDEST DUP5 DUP2 LT PUSH2 0x1681 JUMPI POP POP DUP3 PUSH1 0x0 PUSH1 0x20 DUP1 SWAP5 SWAP6 DUP5 ADD ADD MSTORE PUSH1 0x1F DUP1 NOT SWAP2 ADD AND ADD ADD SWAP1 JUMP JUMPDEST PUSH1 0x20 DUP2 DUP4 ADD DUP2 ADD MLOAD DUP5 DUP4 ADD DUP3 ADD MSTORE ADD PUSH2 0x1660 JUMP JUMPDEST SWAP1 PUSH1 0x1 DUP3 DUP2 SHR SWAP3 AND DUP1 ISZERO PUSH2 0x16C5 JUMPI JUMPDEST PUSH1 0x20 DUP4 LT EQ PUSH2 0x16AF JUMPI JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x22 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST SWAP2 PUSH1 0x7F AND SWAP2 PUSH2 0x16A4 JUMP JUMPDEST DUP1 SLOAD PUSH1 0x0 SWAP4 SWAP3 PUSH2 0x16DE DUP3 PUSH2 0x1695 JUMP JUMPDEST SWAP2 DUP3 DUP3 MSTORE PUSH1 0x20 SWAP4 PUSH1 0x1 SWAP2 PUSH1 0x1 DUP2 AND SWAP1 DUP2 PUSH1 0x0 EQ PUSH2 0x1746 JUMPI POP PUSH1 0x1 EQ PUSH2 0x1705 JUMPI JUMPDEST POP POP POP POP POP JUMP JUMPDEST SWAP1 SWAP4 SWAP5 SWAP6 POP PUSH1 0x0 SWAP3 SWAP2 SWAP3 MSTORE DUP4 PUSH1 0x0 KECCAK256 SWAP3 DUP5 PUSH1 0x0 SWAP5 JUMPDEST DUP4 DUP7 LT PUSH2 0x1732 JUMPI POP POP POP POP ADD ADD SWAP1 CODESIZE DUP1 DUP1 DUP1 DUP1 PUSH2 0x16FE JUMP JUMPDEST DUP1 SLOAD DUP6 DUP8 ADD DUP4 ADD MSTORE SWAP5 ADD SWAP4 DUP6 SWAP1 DUP3 ADD PUSH2 0x171A JUMP JUMPDEST PUSH1 0xFF NOT AND DUP7 DUP6 ADD MSTORE POP POP POP SWAP1 ISZERO ISZERO PUSH1 0x5 SHL ADD ADD SWAP2 POP CODESIZE DUP1 DUP1 DUP1 DUP1 PUSH2 0x16FE JUMP JUMPDEST PUSH1 0xC0 DUP2 ADD SWAP1 DUP2 LT PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP3 GT OR PUSH2 0x637 JUMPI PUSH1 0x40 MSTORE JUMP JUMPDEST PUSH2 0x120 DUP2 ADD SWAP1 DUP2 LT PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP3 GT OR PUSH2 0x637 JUMPI PUSH1 0x40 MSTORE JUMP JUMPDEST SWAP1 PUSH1 0x1F DUP1 NOT SWAP2 ADD AND DUP2 ADD SWAP1 DUP2 LT PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP3 GT OR PUSH2 0x637 JUMPI PUSH1 0x40 MSTORE JUMP JUMPDEST DUP1 MLOAD DUP3 LT ISZERO PUSH2 0x163F JUMPI PUSH1 0x20 SWAP2 PUSH1 0x5 SHL ADD ADD SWAP1 JUMP JUMPDEST PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB DUP1 PUSH1 0x0 SWAP3 AND DUP3 MSTORE PUSH1 0x5 SWAP2 PUSH1 0x20 SWAP3 PUSH1 0x5 DUP5 MSTORE PUSH1 0x40 SWAP5 DUP6 DUP4 KECCAK256 SWAP1 DUP4 MSTORE DUP5 MSTORE DUP5 DUP3 KECCAK256 SWAP5 DUP1 MLOAD DUP1 DUP8 DUP8 DUP3 SWAP10 SLOAD SWAP4 DUP5 DUP2 MSTORE ADD SWAP1 DUP7 MSTORE DUP8 DUP7 KECCAK256 SWAP3 DUP7 JUMPDEST DUP10 DUP3 DUP3 LT PUSH2 0x1920 JUMPI POP POP POP PUSH2 0x1825 SWAP3 POP SUB DUP8 PUSH2 0x179C JUMP JUMPDEST DUP3 JUMPDEST DUP7 MLOAD DUP2 LT ISZERO PUSH2 0x1916 JUMPI PUSH2 0x183A DUP2 DUP9 PUSH2 0x17BD JUMP JUMPDEST MLOAD DUP5 MSTORE PUSH1 0x4 DUP1 DUP8 MSTORE DUP3 DUP6 KECCAK256 DUP4 MLOAD SWAP1 PUSH2 0x1851 DUP3 PUSH2 0x1780 JUMP JUMPDEST DUP1 SLOAD DUP3 MSTORE PUSH2 0x17F PUSH2 0x1898 PUSH1 0x1 SWAP5 DUP11 DUP7 DUP6 ADD SLOAD AND DUP13 DUP7 ADD MSTORE DUP11 PUSH1 0x2 DUP6 ADD SLOAD AND DUP9 DUP7 ADD MSTORE DUP8 MLOAD PUSH2 0x1886 DUP2 PUSH2 0x17F DUP2 PUSH1 0x3 DUP10 ADD PUSH2 0x16CF JUMP JUMPDEST PUSH1 0x60 DUP7 ADD MSTORE DUP8 MLOAD SWAP3 DUP4 DUP1 SWAP3 DUP7 ADD PUSH2 0x16CF JUMP JUMPDEST PUSH1 0x80 DUP4 ADD MSTORE DUP5 MLOAD PUSH2 0x18AF DUP2 PUSH2 0x17F DUP2 DUP11 DUP7 ADD PUSH2 0x16CF JUMP JUMPDEST PUSH1 0xA0 DUP4 ADD MSTORE PUSH1 0x6 DUP2 ADD SLOAD PUSH1 0xC0 DUP4 ADD MSTORE PUSH1 0xFF PUSH1 0x8 PUSH1 0x7 DUP4 ADD SLOAD SWAP3 DUP4 PUSH1 0xE0 DUP7 ADD MSTORE ADD SLOAD AND ISZERO SWAP2 PUSH2 0x100 DUP4 ISZERO SWAP2 ADD MSTORE DUP2 PUSH2 0x18FA JUMPI JUMPDEST POP PUSH2 0x18EF JUMPI POP PUSH1 0x1 ADD PUSH2 0x1827 JUMP JUMPDEST SWAP7 POP POP POP POP POP POP POP SWAP1 JUMP JUMPDEST DUP1 ISZERO SWAP2 POP DUP2 ISZERO PUSH2 0x190C JUMPI JUMPDEST POP CODESIZE PUSH2 0x18E1 JUMP JUMPDEST SWAP1 POP TIMESTAMP LT CODESIZE PUSH2 0x1905 JUMP JUMPDEST POP POP POP SWAP3 POP POP POP SWAP1 JUMP JUMPDEST DUP6 SLOAD DUP5 MSTORE PUSH1 0x1 SWAP6 DUP7 ADD SWAP6 DUP13 SWAP6 POP SWAP4 ADD SWAP3 ADD PUSH2 0x180E JUMP JUMPDEST SWAP3 SWAP2 SWAP3 PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP3 GT PUSH2 0x637 JUMPI PUSH1 0x40 MLOAD SWAP2 PUSH2 0x195F PUSH1 0x1F DUP3 ADD PUSH1 0x1F NOT AND PUSH1 0x20 ADD DUP5 PUSH2 0x179C JUMP JUMPDEST DUP3 SWAP5 DUP2 DUP5 MSTORE DUP2 DUP4 ADD GT PUSH2 0x20C JUMPI DUP3 DUP2 PUSH1 0x20 SWAP4 DUP5 PUSH1 0x0 SWAP7 ADD CALLDATACOPY ADD ADD MSTORE JUMP JUMPDEST PUSH1 0x0 NOT DUP2 EQ PUSH2 0xE61 JUMPI PUSH1 0x1 ADD SWAP1 JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP2 GT PUSH2 0x637 JUMPI PUSH1 0x5 SHL PUSH1 0x20 ADD SWAP1 JUMP JUMPDEST DUP2 ISZERO ISZERO DUP1 PUSH2 0x1C17 JUMPI JUMPDEST ISZERO PUSH2 0x1BD2 JUMPI PUSH1 0x0 PUSH1 0x1 SWAP3 PUSH1 0x1 SLOAD SWAP4 JUMPDEST DUP5 DUP2 LT PUSH2 0x1B9C JUMPI POP DUP2 DUP4 LT ISZERO PUSH2 0x1B75 JUMPI DUP3 DUP3 SUB SWAP2 DUP3 GT PUSH2 0xE61 JUMPI DUP1 DUP3 GT PUSH2 0x1B6D JUMPI JUMPDEST POP PUSH2 0x19E6 DUP2 PUSH2 0x198B JUMP JUMPDEST PUSH1 0x40 SWAP1 PUSH2 0x19F5 DUP3 MLOAD SWAP2 DUP3 PUSH2 0x179C JUMP JUMPDEST DUP3 DUP2 MSTORE PUSH1 0x1F NOT PUSH2 0x1A04 DUP5 PUSH2 0x198B JUMP JUMPDEST ADD PUSH1 0x0 JUMPDEST DUP2 DUP2 LT PUSH2 0x1B2F JUMPI POP POP PUSH1 0x0 SWAP1 DUP2 SWAP4 PUSH1 0x1 JUMPDEST DUP8 DUP2 LT DUP1 PUSH2 0x1B26 JUMPI JUMPDEST ISZERO PUSH2 0x1B1B JUMPI DUP1 PUSH1 0x0 MSTORE PUSH1 0x2 PUSH1 0x20 SWAP1 DUP1 DUP3 MSTORE DUP7 PUSH1 0x0 KECCAK256 PUSH1 0xFF PUSH1 0x5 DUP3 ADD SLOAD AND SWAP2 DUP3 PUSH2 0x1A58 JUMPI JUMPDEST POP POP POP POP PUSH2 0x1A53 SWAP1 PUSH2 0x197C JUMP JUMPDEST PUSH2 0x1A19 JUMP JUMPDEST DUP9 DUP12 DUP12 SWAP7 SWAP12 LT ISZERO PUSH2 0x1A7F JUMPI JUMPDEST POP POP POP POP POP PUSH2 0x1A75 PUSH2 0x1A53 SWAP2 PUSH2 0x197C JUMP JUMPDEST SWAP6 SWAP1 CODESIZE DUP1 DUP1 PUSH2 0x1A46 JUMP JUMPDEST SWAP3 PUSH1 0x4 PUSH2 0x1A53 SWAP7 SWAP10 SWAP4 PUSH2 0x17F PUSH2 0x1AD1 PUSH2 0x1A75 SWAP9 SWAP6 DUP9 PUSH2 0x1B11 SWAP10 MLOAD SWAP8 PUSH2 0x1AA3 DUP10 PUSH2 0x1765 JUMP JUMPDEST DUP6 SLOAD DUP10 MSTORE DUP2 MLOAD SWAP1 PUSH2 0x1AC2 DUP3 PUSH2 0x1ABB DUP2 PUSH1 0x1 DUP12 ADD PUSH2 0x16CF JUMP JUMPDEST SUB DUP4 PUSH2 0x179C JUMP JUMPDEST DUP10 ADD MSTORE MLOAD SWAP3 DUP4 DUP1 SWAP3 DUP7 ADD PUSH2 0x16CF JUMP JUMPDEST DUP5 DUP14 ADD MSTORE PUSH1 0x3 DUP2 ADD SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH1 0x60 DUP6 ADD MSTORE ADD SLOAD PUSH1 0x80 DUP4 ADD MSTORE ISZERO ISZERO PUSH1 0xA0 DUP3 ADD MSTORE PUSH2 0x1B00 DUP3 DUP9 PUSH2 0x17BD JUMP JUMPDEST MSTORE PUSH2 0x1B0B DUP2 DUP8 PUSH2 0x17BD JUMP JUMPDEST POP PUSH2 0x197C JUMP JUMPDEST SWAP5 SWAP2 CODESIZE DUP1 DUP9 PUSH2 0x1A64 JUMP JUMPDEST POP POP SWAP5 POP POP POP POP POP SWAP1 JUMP JUMPDEST POP DUP2 DUP5 LT PUSH2 0x1A22 JUMP JUMPDEST PUSH1 0x20 SWAP1 DUP5 MLOAD PUSH2 0x1B3D DUP2 PUSH2 0x1765 JUMP JUMPDEST PUSH1 0x0 DUP2 MSTORE PUSH1 0x60 PUSH1 0x0 DUP5 SWAP2 DUP1 DUP4 DUP6 ADD MSTORE DUP1 DUP10 DUP6 ADD MSTORE DUP4 ADD MSTORE PUSH1 0x0 PUSH1 0x80 DUP4 ADD MSTORE PUSH1 0x0 PUSH1 0xA0 DUP4 ADD MSTORE DUP3 DUP7 ADD ADD MSTORE ADD PUSH2 0x1A08 JUMP JUMPDEST SWAP1 POP CODESIZE PUSH2 0x19DC JUMP JUMPDEST POP POP POP POP PUSH1 0x40 MLOAD PUSH1 0x20 DUP2 ADD DUP2 DUP2 LT PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP3 GT OR PUSH2 0x637 JUMPI PUSH1 0x40 MSTORE PUSH1 0x0 DUP2 MSTORE SWAP1 JUMP JUMPDEST DUP1 PUSH1 0x0 MSTORE PUSH1 0x2 PUSH1 0x20 MSTORE PUSH1 0xFF PUSH1 0x5 PUSH1 0x40 PUSH1 0x0 KECCAK256 ADD SLOAD AND PUSH2 0x1BBE JUMPI JUMPDEST PUSH1 0x1 ADD PUSH2 0x19BA JUMP JUMPDEST SWAP2 PUSH2 0x1BCA PUSH1 0x1 SWAP2 PUSH2 0x197C JUMP JUMPDEST SWAP3 SWAP1 POP PUSH2 0x1BB6 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x1C PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x436C61696D52656769737472793A20696E76616C6964206C696D697400000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST POP PUSH1 0x64 DUP3 GT ISZERO PUSH2 0x19AB JUMP JUMPDEST SWAP1 PUSH1 0x1F DUP2 GT PUSH2 0x1C30 JUMPI POP POP POP JUMP JUMPDEST PUSH1 0x0 SWAP2 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x20 PUSH1 0x1F DUP6 ADD PUSH1 0x5 SHR DUP4 ADD SWAP5 LT PUSH2 0x1C6E JUMPI JUMPDEST PUSH1 0x1F ADD PUSH1 0x5 SHR ADD SWAP2 JUMPDEST DUP3 DUP2 LT PUSH2 0x1C63 JUMPI POP POP POP JUMP JUMPDEST DUP2 DUP2 SSTORE PUSH1 0x1 ADD PUSH2 0x1C57 JUMP JUMPDEST SWAP1 SWAP3 POP DUP3 SWAP1 PUSH2 0x1C4E JUMP JUMPDEST SWAP1 DUP1 PUSH1 0x20 SWAP4 SWAP3 DUP2 DUP5 MSTORE DUP5 DUP5 ADD CALLDATACOPY PUSH1 0x0 DUP3 DUP3 ADD DUP5 ADD MSTORE PUSH1 0x1F ADD PUSH1 0x1F NOT AND ADD ADD SWAP1 JUMP JUMPDEST CALLER PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH32 0x52A9CC15D02F8D37B4D6F72E1EF19BC07BF335BB0FE54C472AFEF4CE2D4549F0 PUSH1 0x20 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD PUSH32 0xDF6BC58AF35302F8541FB5D0DA6C4472BE7FC3A416BF34042D13743AC0A50915 SWAP1 PUSH1 0xFF AND ISZERO PUSH2 0x1CF5 JUMPI POP JUMP JUMPDEST PUSH1 0x44 SWAP1 PUSH1 0x40 MLOAD SWAP1 PUSH4 0xE2517D3F PUSH1 0xE0 SHL DUP3 MSTORE CALLER PUSH1 0x4 DUP4 ADD MSTORE PUSH1 0x24 DUP3 ADD MSTORE REVERT JUMPDEST DUP1 PUSH1 0x0 MSTORE PUSH1 0x0 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 CALLER PUSH1 0x0 MSTORE PUSH1 0x20 MSTORE PUSH1 0xFF PUSH1 0x40 PUSH1 0x0 KECCAK256 SLOAD AND ISZERO PUSH2 0x1CF5 JUMPI POP JUMP JUMPDEST SWAP1 PUSH1 0x0 SWAP2 DUP1 DUP4 MSTORE DUP3 PUSH1 0x20 MSTORE PUSH1 0x40 DUP4 KECCAK256 SWAP2 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB AND SWAP2 DUP3 DUP5 MSTORE PUSH1 0x20 MSTORE PUSH1 0xFF PUSH1 0x40 DUP5 KECCAK256 SLOAD AND ISZERO PUSH1 0x0 EQ PUSH2 0x1DB2 JUMPI DUP1 DUP4 MSTORE DUP3 PUSH1 0x20 MSTORE PUSH1 0x40 DUP4 KECCAK256 DUP3 DUP5 MSTORE PUSH1 0x20 MSTORE PUSH1 0x40 DUP4 KECCAK256 PUSH1 0x1 PUSH1 0xFF NOT DUP3 SLOAD AND OR SWAP1 SSTORE PUSH32 0x2F8788117E7EFF1D82E926EC794901D17C78024A50270940304540A733656F0D CALLER SWAP4 DUP1 LOG4 PUSH1 0x1 SWAP1 JUMP JUMPDEST POP POP SWAP1 JUMP JUMPDEST SWAP1 PUSH1 0x0 SWAP2 DUP1 DUP4 MSTORE DUP3 PUSH1 0x20 MSTORE PUSH1 0x40 DUP4 KECCAK256 SWAP2 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB AND SWAP2 DUP3 DUP5 MSTORE PUSH1 0x20 MSTORE PUSH1 0xFF PUSH1 0x40 DUP5 KECCAK256 SLOAD AND PUSH1 0x0 EQ PUSH2 0x1DB2 JUMPI DUP1 DUP4 MSTORE DUP3 PUSH1 0x20 MSTORE PUSH1 0x40 DUP4 KECCAK256 DUP3 DUP5 MSTORE PUSH1 0x20 MSTORE PUSH1 0x40 DUP4 KECCAK256 PUSH1 0xFF NOT DUP2 SLOAD AND SWAP1 SSTORE PUSH32 0xF6391F5C32D9C69D2A47EA670B442974B53935D1EDC7FD64EB21E047A839171B CALLER SWAP4 DUP1 LOG4 PUSH1 0x1 SWAP1 JUMP INVALID LOG2 PUSH5 0x6970667358 0x22 SLT KECCAK256 0xB4 SWAP11 SLOAD 0xB8 PUSH21 0xCCC1160A18B7D3B9CAD0FCC7D698618D886AE2709A 0x1F PUSH0 SWAP9 0xC1 DUP14 CREATE PUSH5 0x736F6C6343 STOP ADDMOD AND STOP CALLER 0x2F DUP8 DUP9 GT PUSH31 0x7EFF1D82E926EC794901D17C78024A50270940304540A733656F0D00000000 ", "sourceMap": "226:7939:5:-:0;;;;;;;;;;;;-1:-1:-1;;226:7939:5;;;-1:-1:-1;;;;;226:7939:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;226:7939:5;;;;;;2135:38;2042:37;;2619:77;2042:37;;:::i;:::-;;2089:36;;;:::i;:::-;;2135:38;:::i;:::-;;2252:1;404:32;;2337:87;404:32;;:::i;:::-;;;;-1:-1:-1;;;404:32:5;;;;;;:::i;:::-;;;;;;;;;;-1:-1:-1;;;226:7939:5;404:32;;;2337:87;:::i;:::-;;2434:78;404:32;;:::i;:::-;;;;;;;;;;;:::i;:::-;;;;;;;;;;-1:-1:-1;;;226:7939:5;404:32;;;2434:78;:::i;:::-;;2522:87;404:32;;:::i;:::-;;;;;;;;;;;:::i;:::-;;;;;;;;;;-1:-1:-1;;;226:7939:5;404:32;;;2522:87;:::i;:::-;;404:32;;:::i;:::-;;;;;;;;;;;;:::i;:::-;;;;;;;;2619:77;:::i;:::-;;226:7939;;;;;;;;;;-1:-1:-1;226:7939:5;;;;;;-1:-1:-1;226:7939:5;;;;;-1:-1:-1;226:7939:5;;;;;;;;;-1:-1:-1;;;;;226:7939:5;;;;;;;;;;:::o;:::-;;;;404:32;226:7939;;-1:-1:-1;;;;;226:7939:5;;;;;;;;;;:::o;6179:316:0:-;-1:-1:-1;;;;;226:7939:5;2232:4:0;226:7939:5;;;;;;;;;;2232:4:0;;226:7939:5;;;;;;;;;;;;;;;;;;;;;;6347:4:0;226:7939:5;;;;;;;;735:10:2;6370:40:0;-1:-1:-1;;;;;;;;;;;6370:40:0;;;6347:4;6424:11;:::o;6272:217::-;6466:12;;:::o;6179:316::-;-1:-1:-1;;;;;226:7939:5;2954:6:0;226:7939:5;;;;;;;;;;2954:6:0;;226:7939:5;322:30;;226:7939;;;;;;;;;;;;;;;;;;;;;6347:4:0;226:7939:5;;;;;;;;-1:-1:-1;;;;;;;;;;;735:10:2;6370:40:0;;;6347:4;6424:11;:::o;6272:217::-;6466:12;;;:::o;6179:316::-;-1:-1:-1;;;;;226:7939:5;2954:6:0;226:7939:5;;;;;;;;;;2954:6:0;;226:7939:5;404:32;;226:7939;;;;;;;;;;;;;;;;;;;;;6347:4:0;226:7939:5;;;;;;;;-1:-1:-1;;;;;;;;;;;735:10:2;6370:40:0;;;6347:4;6424:11;:::o;226:7939:5:-;;;;;;;;;-1:-1:-1;226:7939:5;;;;;;;;;-1:-1:-1;226:7939:5;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;3046:613;3200:18;226:7939;;3046:613;-1:-1:-1;;226:7939:5;;;;3200:18;226:7939;;;404:32;;226:7939;;;;;;-1:-1:-1;;;;;226:7939:5;;;;;;;;;;;;;3263:211;;;226:7939;;;3263:211;;;;226:7939;3387:10;3263:211;;;226:7939;3422:15;3263:211;;;226:7939;3200:18;3263:211;;;226:7939;;-1:-1:-1;226:7939:5;3237:10;3263:211;226:7939;;-1:-1:-1;226:7939:5;;;;404:32;;226:7939;;;;;;;;;;;;;3200:18;226:7939;;;;3200:18;226:7939;;;;;;;;;3046:613;3263:211;226:7939;;;;;;;;;;;;3046:613;226:7939;3263:211;226:7939;;;;;;;;-1:-1:-1;226:7939:5;;;;;;;3200:18;226:7939;;;;;;;;;;;3200:18;226:7939;;;;3263:211;;;226:7939;;;3237:10;226:7939;;;-1:-1:-1;;;;;226:7939:5;;;;;;3200:18;226:7939;;;;;;;;;;3263:211;226:7939;;;;;;;;;;;;3263:211;226:7939;;;;;;;;;;;;;3263:211;226:7939;;-1:-1:-1;226:7939:5;;;;;;;3200:18;226:7939;;;;;;;;;;;;;;;;;;;;;;3263:211;;;226:7939;;;;;;;;;;;;;;3263:211;;;226:7939;;;;404:32;226:7939;3263:211;;226:7939;;;;;;;;;;;;;;3387:10;-1:-1:-1;226:7939:5;;3263:211;226:7939;;-1:-1:-1;226:7939:5;;;;;;;;;;;3200:18;226:7939;;;;;;;;;;;3564:60;226:7939;;;-1:-1:-1;226:7939:5;3263:211;-1:-1:-1;226:7939:5;;;;;;;;;;;;;;;;;:::i;:::-;;;;3263:211;226:7939;;;3387:10;226:7939;;:::i;:::-;3564:60;;;3046:613;:::o;226:7939::-;;;;-1:-1:-1;226:7939:5;;;;;-1:-1:-1;226:7939:5;;;;;-1:-1:-1;226:7939:5;;;;;;;;;;;;-1:-1:-1;226:7939:5;3263:211;-1:-1:-1;226:7939:5;;-1:-1:-1;226:7939:5;;;;;;;;3200:18;226:7939;;;3263:211;226:7939;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3263:211;3200:18;226:7939;;;;;;;;;;;;;;;;;-1:-1:-1;226:7939:5;3263:211;-1:-1:-1;226:7939:5;;;;;;;;;3263:211;226:7939;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;226:7939:5;;3200:18;226:7939;;;;;;-1:-1:-1;226:7939:5;;;;;;;;-1:-1:-1;226:7939:5;;;;;-1:-1:-1;226:7939:5;;;;;;;;;;;;-1:-1:-1;226:7939:5;;;;;;;3200:18;226:7939;;-1:-1:-1;226:7939:5;3263:211;-1:-1:-1;226:7939:5;;-1:-1:-1;226:7939:5;;-1:-1:-1;;226:7939:5;;;;;;3200:18;226:7939;;;;;;;;;;;;;;;;;3200:18;226:7939;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3263:211;226:7939;;;;3200:18;226:7939;;;;;;;;;;;;;;3200:18;226:7939;;-1:-1:-1;226:7939:5;3263:211;-1:-1:-1;226:7939:5;;;;;;;;;3263:211;226:7939;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;226:7939:5;;;;-1:-1:-1;3200:18:5;226:7939;;;;;;-1:-1:-1;226:7939:5;;;;;;;;;;;;;;;-1:-1:-1;226:7939:5;;;;;-1:-1:-1;226:7939:5"}, "deployedBytecode": {"functionDebugData": {"abi_decode_address": {"entryPoint": 5582, "id": null, "parameterSlots": 0, "returnSlots": 1}, "abi_decode_address_21864": {"entryPoint": 5604, "id": null, "parameterSlots": 0, "returnSlots": 1}, "abi_decode_available_length_string": {"entryPoint": 6454, "id": null, "parameterSlots": 3, "returnSlots": 1}, "abi_decode_string_calldata": {"entryPoint": 5626, "id": null, "parameterSlots": 2, "returnSlots": 2}, "abi_encode_string": {"entryPoint": 5717, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_string_calldata": {"entryPoint": 7288, "id": null, "parameterSlots": 3, "returnSlots": 1}, "abi_encode_string_storage": {"entryPoint": 5839, "id": null, "parameterSlots": 2, "returnSlots": 1}, "array_allocation_size_array_struct_ClaimType_dyn": {"entryPoint": 6539, "id": null, "parameterSlots": 1, "returnSlots": 1}, "clean_up_bytearray_end_slots_string_storage": {"entryPoint": 7202, "id": null, "parameterSlots": 3, "returnSlots": 0}, "extract_byte_array_length": {"entryPoint": 5781, "id": null, "parameterSlots": 1, "returnSlots": 1}, "extract_used_part_and_set_length_of_short_byte_array": {"entryPoint": null, "id": null, "parameterSlots": 2, "returnSlots": 1}, "finalize_allocation": {"entryPoint": 6044, "id": null, "parameterSlots": 2, "returnSlots": 0}, "finalize_allocation_21863": {"entryPoint": 5989, "id": null, "parameterSlots": 1, "returnSlots": 0}, "finalize_allocation_21872": {"entryPoint": 6016, "id": null, "parameterSlots": 1, "returnSlots": 0}, "fun_checkRole": {"entryPoint": 7443, "id": 93, "parameterSlots": 1, "returnSlots": 0}, "fun_checkRole_21862": {"entryPoint": 7321, "id": 93, "parameterSlots": 0, "returnSlots": 0}, "fun_getActiveClaimTypes": {"entryPoint": 6562, "id": 1036, "parameterSlots": 2, "returnSlots": 1}, "fun_grantRole": {"entryPoint": 7481, "id": 256, "parameterSlots": 2, "returnSlots": 1}, "fun_hasValidClaim": {"entryPoint": 6097, "id": 897, "parameterSlots": 2, "returnSlots": 1}, "fun_revokeRole": {"entryPoint": 7607, "id": 294, "parameterSlots": 2, "returnSlots": 1}, "increment_uint256": {"entryPoint": 6524, "id": null, "parameterSlots": 1, "returnSlots": 1}, "memory_array_index_access_bytes32_dyn": {"entryPoint": 6077, "id": null, "parameterSlots": 2, "returnSlots": 1}, "storage_array_index_access_bytes32_dyn": {"entryPoint": 5671, "id": null, "parameterSlots": 2, "returnSlots": 2}, "update_byte_slice_dynamic32": {"entryPoint": null, "id": null, "parameterSlots": 3, "returnSlots": 1}, "update_storage_value_offsett_bool_to_bool": {"entryPoint": null, "id": null, "parameterSlots": 2, "returnSlots": 0}}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "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", "opcodes": "PUSH1 0x80 DUP1 PUSH1 0x40 MSTORE PUSH1 0x4 CALLDATASIZE LT ISZERO PUSH2 0x13 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH1 0x0 CALLDATALOAD PUSH1 0xE0 SHR SWAP1 DUP2 PUSH4 0x1FFC9A7 EQ PUSH2 0x157B JUMPI POP DUP1 PUSH4 0xEC2D8FE EQ PUSH2 0x14C7 JUMPI DUP1 PUSH4 0x1D541EA2 EQ PUSH2 0x147F JUMPI DUP1 PUSH4 0x23F86802 EQ PUSH2 0x13BD JUMPI DUP1 PUSH4 0x248A9CA3 EQ PUSH2 0x138E JUMPI DUP1 PUSH4 0x2C52E7AB EQ PUSH2 0x135D JUMPI DUP1 PUSH4 0x2E0D3857 EQ PUSH2 0x1006 JUMPI DUP1 PUSH4 0x2F2FF15D EQ PUSH2 0xFC7 JUMPI DUP1 PUSH4 0x36568ABE EQ PUSH2 0xF80 JUMPI DUP1 PUSH4 0x60CF25E2 EQ PUSH2 0xF1D JUMPI DUP1 PUSH4 0x8CCF00FB EQ PUSH2 0xEC4 JUMPI DUP1 PUSH4 0x91D14854 EQ PUSH2 0xE77 JUMPI DUP1 PUSH4 0x948DB1B8 EQ PUSH2 0xE36 JUMPI DUP1 PUSH4 0xA044E70F EQ PUSH2 0xDFB JUMPI DUP1 PUSH4 0xA217FDDF EQ PUSH2 0xDDF JUMPI DUP1 PUSH4 0xA335ACA8 EQ PUSH2 0xD0F JUMPI DUP1 PUSH4 0xB8233248 EQ PUSH2 0x949 JUMPI DUP1 PUSH4 0xC1EDCDC1 EQ PUSH2 0x89E JUMPI DUP1 PUSH4 0xD547741F EQ PUSH2 0x85D JUMPI DUP1 PUSH4 0xDD63E196 EQ PUSH2 0x24C JUMPI DUP1 PUSH4 0xE60ECDD2 EQ PUSH2 0x211 JUMPI PUSH4 0xEFF0F592 EQ PUSH2 0x111 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x4 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x4 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 DUP1 SLOAD SWAP1 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB SWAP1 DUP2 PUSH1 0x1 DUP3 ADD SLOAD AND SWAP2 PUSH1 0x2 DUP3 ADD SLOAD AND SWAP3 PUSH1 0x40 MLOAD SWAP2 PUSH2 0x168 DUP4 PUSH2 0x161 DUP2 PUSH1 0x3 DUP6 ADD PUSH2 0x16CF JUMP JUMPDEST SUB DUP5 PUSH2 0x179C JUMP JUMPDEST PUSH2 0x1F5 PUSH1 0x40 MLOAD PUSH2 0x186 DUP2 PUSH2 0x17F DUP2 PUSH1 0x4 DUP8 ADD PUSH2 0x16CF JUMP JUMPDEST SUB DUP3 PUSH2 0x179C JUMP JUMPDEST PUSH2 0x1E7 PUSH1 0x40 MLOAD SWAP2 PUSH2 0x19E DUP4 PUSH2 0x161 DUP2 PUSH1 0x5 DUP10 ADD PUSH2 0x16CF JUMP JUMPDEST PUSH2 0x1D9 PUSH1 0x6 DUP7 ADD SLOAD SWAP8 PUSH1 0xFF PUSH1 0x8 PUSH1 0x7 DUP10 ADD SLOAD SWAP9 ADD SLOAD AND SWAP8 PUSH1 0x40 MLOAD SWAP12 DUP13 SWAP12 PUSH2 0x120 SWAP3 DUP14 MSTORE PUSH1 0x20 DUP14 ADD MSTORE PUSH1 0x40 DUP13 ADD MSTORE DUP1 PUSH1 0x60 DUP13 ADD MSTORE DUP11 ADD SWAP1 PUSH2 0x1655 JUMP JUMPDEST SWAP1 DUP9 DUP3 SUB PUSH1 0x80 DUP11 ADD MSTORE PUSH2 0x1655 JUMP JUMPDEST SWAP1 DUP7 DUP3 SUB PUSH1 0xA0 DUP9 ADD MSTORE PUSH2 0x1655 JUMP JUMPDEST SWAP3 PUSH1 0xC0 DUP6 ADD MSTORE PUSH1 0xE0 DUP5 ADD MSTORE ISZERO ISZERO PUSH2 0x100 DUP4 ADD MSTORE SUB SWAP1 RETURN JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x20 PUSH1 0x40 MLOAD PUSH32 0xA9EF30CACD3C540E9D2B47058CE383D745F3E72389B3EA103DB79727BA9CCE89 DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0xC0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH2 0x265 PUSH2 0x15CE JUMP JUMPDEST PUSH1 0x44 CALLDATALOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP2 GT PUSH2 0x20C JUMPI PUSH2 0x284 SWAP1 CALLDATASIZE SWAP1 PUSH1 0x4 ADD PUSH2 0x15FA JUMP JUMPDEST SWAP1 SWAP2 PUSH1 0x64 CALLDATALOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP2 GT PUSH2 0x20C JUMPI PUSH2 0x2A5 SWAP1 CALLDATASIZE SWAP1 PUSH1 0x4 ADD PUSH2 0x15FA JUMP JUMPDEST SWAP2 SWAP1 SWAP4 PUSH1 0x84 CALLDATALOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP2 GT PUSH2 0x20C JUMPI PUSH2 0x2C7 SWAP1 CALLDATASIZE SWAP1 PUSH1 0x4 ADD PUSH2 0x15FA JUMP JUMPDEST SWAP6 SWAP1 SWAP4 PUSH2 0x2D2 PUSH2 0x1C99 JUMP JUMPDEST PUSH1 0x24 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x2 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 SLOAD ISZERO PUSH2 0x80E JUMPI PUSH1 0x24 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x2 PUSH1 0x20 MSTORE PUSH1 0xFF PUSH1 0x5 PUSH1 0x40 PUSH1 0x0 KECCAK256 ADD SLOAD AND ISZERO PUSH2 0x7BB JUMPI PUSH1 0x40 MLOAD PUSH12 0xFFFFFFFFFFFFFFFFFFFFFFFF NOT DUP6 PUSH1 0x60 SHL AND PUSH1 0x20 DUP3 ADD MSTORE PUSH1 0x24 CALLDATALOAD PUSH1 0x34 DUP3 ADD MSTORE CALLER PUSH1 0x60 SHL PUSH1 0x54 DUP3 ADD MSTORE TIMESTAMP PUSH1 0x68 DUP3 ADD MSTORE DUP2 DUP4 PUSH1 0x88 DUP4 ADD CALLDATACOPY PUSH2 0x35A PUSH1 0x88 DUP3 DUP5 DUP2 ADD PUSH1 0x0 DUP4 DUP3 ADD MSTORE SUB PUSH1 0x68 DUP2 ADD DUP5 MSTORE ADD DUP3 PUSH2 0x179C JUMP JUMPDEST PUSH1 0x20 DUP2 MLOAD SWAP2 ADD KECCAK256 SWAP6 PUSH2 0x3A8 PUSH1 0x40 MLOAD SWAP4 PUSH2 0x372 DUP6 PUSH2 0x1780 JUMP JUMPDEST PUSH1 0x24 CALLDATALOAD DUP6 MSTORE PUSH2 0x399 PUSH1 0x20 DUP7 ADD SWAP7 CALLER DUP9 MSTORE PUSH1 0x40 DUP8 ADD SWAP5 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB DUP11 AND DUP7 MSTORE CALLDATASIZE SWAP2 PUSH2 0x1936 JUMP JUMPDEST SWAP4 PUSH1 0x60 DUP7 ADD SWAP5 DUP6 MSTORE CALLDATASIZE SWAP2 PUSH2 0x1936 JUMP JUMPDEST PUSH1 0x80 DUP5 ADD MSTORE PUSH2 0x3B8 CALLDATASIZE DUP10 DUP9 PUSH2 0x1936 JUMP JUMPDEST PUSH1 0xA0 DUP5 ADD MSTORE TIMESTAMP PUSH1 0xC0 DUP5 ADD MSTORE PUSH1 0xA4 CALLDATALOAD PUSH1 0xE0 DUP5 ADD MSTORE PUSH1 0x0 PUSH2 0x100 DUP5 ADD DUP2 SWAP1 MSTORE DUP8 DUP2 MSTORE PUSH1 0x4 PUSH1 0x20 MSTORE PUSH1 0x40 SWAP1 KECCAK256 DUP4 MLOAD DUP2 SSTORE SWAP4 MLOAD PUSH1 0x1 DUP6 ADD DUP1 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB NOT SWAP1 DUP2 AND PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP4 DUP5 AND OR SWAP1 SWAP2 SSTORE SWAP2 MLOAD PUSH1 0x2 DUP7 ADD DUP1 SLOAD SWAP1 SWAP4 AND SWAP2 AND OR SWAP1 SSTORE MLOAD DUP1 MLOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP3 GT PUSH2 0x637 JUMPI PUSH2 0x444 DUP3 PUSH2 0x43B PUSH1 0x3 DUP8 ADD SLOAD PUSH2 0x1695 JUMP JUMPDEST PUSH1 0x3 DUP8 ADD PUSH2 0x1C22 JUMP JUMPDEST PUSH1 0x20 SWAP1 PUSH1 0x1F DUP4 GT PUSH1 0x1 EQ PUSH2 0x749 JUMPI PUSH2 0x475 SWAP3 SWAP2 PUSH1 0x0 SWAP2 DUP4 PUSH2 0x6D0 JUMPI JUMPDEST POP POP DUP2 PUSH1 0x1 SHL SWAP2 PUSH1 0x0 NOT SWAP1 PUSH1 0x3 SHL SHR NOT AND OR SWAP1 JUMP JUMPDEST PUSH1 0x3 DUP4 ADD SSTORE JUMPDEST PUSH1 0x80 DUP2 ADD MLOAD DUP1 MLOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP3 GT PUSH2 0x637 JUMPI PUSH2 0x4AB DUP3 PUSH2 0x4A2 PUSH1 0x4 DUP8 ADD SLOAD PUSH2 0x1695 JUMP JUMPDEST PUSH1 0x4 DUP8 ADD PUSH2 0x1C22 JUMP JUMPDEST PUSH1 0x20 SWAP1 PUSH1 0x1F DUP4 GT PUSH1 0x1 EQ PUSH2 0x6DB JUMPI PUSH2 0x4DB SWAP3 SWAP2 PUSH1 0x0 SWAP2 DUP4 PUSH2 0x6D0 JUMPI POP POP DUP2 PUSH1 0x1 SHL SWAP2 PUSH1 0x0 NOT SWAP1 PUSH1 0x3 SHL SHR NOT AND OR SWAP1 JUMP JUMPDEST PUSH1 0x4 DUP4 ADD SSTORE JUMPDEST PUSH1 0xA0 DUP2 ADD MLOAD DUP1 MLOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP3 GT PUSH2 0x637 JUMPI PUSH2 0x511 DUP3 PUSH2 0x508 PUSH1 0x5 DUP8 ADD SLOAD PUSH2 0x1695 JUMP JUMPDEST PUSH1 0x5 DUP8 ADD PUSH2 0x1C22 JUMP JUMPDEST PUSH1 0x20 SWAP1 PUSH1 0x1F DUP4 GT PUSH1 0x1 EQ PUSH2 0x658 JUMPI SWAP3 PUSH2 0x54C DUP4 PUSH1 0x8 SWAP5 PUSH2 0x100 SWAP5 PUSH2 0x57D SWAP9 SWAP8 PUSH1 0x0 SWAP3 PUSH2 0x64D JUMPI POP POP DUP2 PUSH1 0x1 SHL SWAP2 PUSH1 0x0 NOT SWAP1 PUSH1 0x3 SHL SHR NOT AND OR SWAP1 JUMP JUMPDEST PUSH1 0x5 DUP6 ADD SSTORE JUMPDEST PUSH1 0xC0 DUP2 ADD MLOAD PUSH1 0x6 DUP6 ADD SSTORE PUSH1 0xE0 DUP2 ADD MLOAD PUSH1 0x7 DUP6 ADD SSTORE ADD MLOAD ISZERO ISZERO SWAP2 ADD SWAP1 PUSH1 0xFF DUP1 NOT DUP4 SLOAD AND SWAP2 ISZERO ISZERO AND OR SWAP1 SSTORE JUMP JUMPDEST PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB DUP2 AND PUSH1 0x0 MSTORE PUSH1 0x5 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 PUSH1 0x24 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 SWAP4 DUP5 SLOAD SWAP5 PUSH1 0x1 PUSH1 0x40 SHL DUP7 LT ISZERO PUSH2 0x637 JUMPI PUSH2 0x62C DUP6 SWAP5 DUP6 PUSH2 0x604 PUSH2 0x5ED DUP11 PUSH32 0x294C37B9B884B1967835179D3C147BDDC32A4B714710B0885CC4D55C4640FBF1 SWAP7 PUSH1 0x1 PUSH1 0x20 SWAP14 ADD DUP2 SSTORE PUSH2 0x1627 JUMP JUMPDEST DUP2 SWAP4 SWAP2 SLOAD SWAP1 PUSH1 0x3 SHL SWAP2 DUP3 SHL SWAP2 PUSH1 0x0 NOT SWAP1 SHL NOT AND OR SWAP1 JUMP JUMPDEST SWAP1 SSTORE PUSH1 0x40 MLOAD SWAP2 DUP3 SWAP2 CALLER DUP4 MSTORE PUSH1 0x40 DUP11 DUP5 ADD MSTORE PUSH1 0x24 CALLDATALOAD SWAP7 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB AND SWAP6 PUSH1 0x40 DUP5 ADD SWAP2 PUSH2 0x1C78 JUMP JUMPDEST SUB SWAP1 LOG4 PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x41 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST ADD MLOAD SWAP1 POP DUP13 DUP1 PUSH2 0x460 JUMP JUMPDEST SWAP1 PUSH1 0x1F NOT DUP4 AND SWAP2 PUSH1 0x5 DUP7 ADD PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP3 PUSH1 0x0 JUMPDEST DUP2 DUP2 LT PUSH2 0x6B8 JUMPI POP SWAP4 PUSH2 0x100 SWAP4 PUSH2 0x57D SWAP8 SWAP7 SWAP4 PUSH1 0x1 SWAP4 DUP4 PUSH1 0x8 SWAP9 LT PUSH2 0x69F JUMPI JUMPDEST POP POP POP DUP2 SHL ADD PUSH1 0x5 DUP6 ADD SSTORE PUSH2 0x552 JUMP JUMPDEST ADD MLOAD PUSH1 0x0 NOT PUSH1 0xF8 DUP5 PUSH1 0x3 SHL AND SHR NOT AND SWAP1 SSTORE DUP12 DUP1 DUP1 PUSH2 0x68F JUMP JUMPDEST SWAP3 SWAP4 PUSH1 0x20 PUSH1 0x1 DUP2 SWAP3 DUP8 DUP7 ADD MLOAD DUP2 SSTORE ADD SWAP6 ADD SWAP4 ADD PUSH2 0x66F JUMP JUMPDEST ADD MLOAD SWAP1 POP DUP10 DUP1 PUSH2 0x460 JUMP JUMPDEST SWAP1 PUSH1 0x1F NOT DUP4 AND SWAP2 PUSH1 0x4 DUP7 ADD PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP3 PUSH1 0x0 JUMPDEST DUP2 DUP2 LT PUSH2 0x731 JUMPI POP SWAP1 DUP5 PUSH1 0x1 SWAP6 SWAP5 SWAP4 SWAP3 LT PUSH2 0x718 JUMPI JUMPDEST POP POP POP DUP2 SHL ADD PUSH1 0x4 DUP4 ADD SSTORE PUSH2 0x4E1 JUMP JUMPDEST ADD MLOAD PUSH1 0x0 NOT PUSH1 0xF8 DUP5 PUSH1 0x3 SHL AND SHR NOT AND SWAP1 SSTORE DUP9 DUP1 DUP1 PUSH2 0x708 JUMP JUMPDEST SWAP3 SWAP4 PUSH1 0x20 PUSH1 0x1 DUP2 SWAP3 DUP8 DUP7 ADD MLOAD DUP2 SSTORE ADD SWAP6 ADD SWAP4 ADD PUSH2 0x6F2 JUMP JUMPDEST SWAP2 SWAP1 PUSH1 0x3 DUP6 ADD PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x0 SWAP4 JUMPDEST PUSH1 0x1F NOT DUP5 AND DUP6 LT PUSH2 0x7A0 JUMPI PUSH1 0x1 SWAP5 POP DUP4 PUSH1 0x1F NOT DUP2 AND LT PUSH2 0x787 JUMPI JUMPDEST POP POP POP DUP2 SHL ADD PUSH1 0x3 DUP4 ADD SSTORE PUSH2 0x47B JUMP JUMPDEST ADD MLOAD PUSH1 0x0 NOT PUSH1 0xF8 DUP5 PUSH1 0x3 SHL AND SHR NOT AND SWAP1 SSTORE DUP9 DUP1 DUP1 PUSH2 0x777 JUMP JUMPDEST DUP2 DUP2 ADD MLOAD DUP4 SSTORE PUSH1 0x20 SWAP5 DUP6 ADD SWAP5 PUSH1 0x1 SWAP1 SWAP4 ADD SWAP3 SWAP1 SWAP2 ADD SWAP1 PUSH2 0x75C JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x25 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x436C61696D52656769737472793A20636C61696D207479706520697320696E61 PUSH1 0x44 DUP3 ADD MSTORE PUSH5 0x6374697665 PUSH1 0xD8 SHL PUSH1 0x64 DUP3 ADD MSTORE PUSH1 0x84 SWAP1 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x21 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x436C61696D52656769737472793A20696E76616C696420636C61696D20747970 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x65 PUSH1 0xF8 SHL PUSH1 0x64 DUP3 ADD MSTORE PUSH1 0x84 SWAP1 REVERT JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH2 0x89C PUSH1 0x4 CALLDATALOAD PUSH2 0x87C PUSH2 0x15E4 JUMP JUMPDEST SWAP1 DUP1 PUSH1 0x0 MSTORE PUSH1 0x0 PUSH1 0x20 MSTORE PUSH2 0x897 PUSH1 0x1 PUSH1 0x40 PUSH1 0x0 KECCAK256 ADD SLOAD PUSH2 0x1D13 JUMP JUMPDEST PUSH2 0x1DB7 JUMP JUMPDEST STOP JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x4 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x2 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 DUP1 SLOAD PUSH1 0x40 MLOAD SWAP2 PUSH2 0x8D6 DUP4 PUSH2 0x161 DUP2 PUSH1 0x1 DUP6 ADD PUSH2 0x16CF JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0x8EA DUP2 PUSH2 0x17F DUP2 PUSH1 0x2 DUP7 ADD PUSH2 0x16CF JUMP JUMPDEST PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB PUSH1 0x3 DUP4 ADD SLOAD AND SWAP2 PUSH2 0x933 PUSH1 0xFF PUSH1 0x5 PUSH1 0x4 DUP5 ADD SLOAD SWAP4 ADD SLOAD AND SWAP3 PUSH2 0x925 PUSH1 0x40 MLOAD SWAP8 DUP9 SWAP8 DUP9 MSTORE PUSH1 0xC0 PUSH1 0x20 DUP10 ADD MSTORE PUSH1 0xC0 DUP9 ADD SWAP1 PUSH2 0x1655 JUMP JUMPDEST SWAP1 DUP7 DUP3 SUB PUSH1 0x40 DUP9 ADD MSTORE PUSH2 0x1655 JUMP JUMPDEST SWAP3 PUSH1 0x60 DUP6 ADD MSTORE PUSH1 0x80 DUP5 ADD MSTORE ISZERO ISZERO PUSH1 0xA0 DUP4 ADD MSTORE SUB SWAP1 RETURN JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x80 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x24 CALLDATALOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP2 GT PUSH2 0x20C JUMPI PUSH2 0x979 SWAP1 CALLDATASIZE SWAP1 PUSH1 0x4 ADD PUSH2 0x15FA JUMP JUMPDEST SWAP1 PUSH1 0x44 CALLDATALOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP2 GT PUSH2 0x20C JUMPI PUSH2 0x999 SWAP1 CALLDATASIZE SWAP1 PUSH1 0x4 ADD PUSH2 0x15FA JUMP JUMPDEST SWAP1 SWAP2 PUSH1 0x64 CALLDATALOAD ISZERO ISZERO PUSH1 0x64 CALLDATALOAD SUB PUSH2 0x20C JUMPI PUSH1 0x4 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x2 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 DUP1 SLOAD ISZERO PUSH2 0xCB9 JUMPI PUSH1 0x3 DUP2 ADD SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND CALLER EQ DUP1 ISZERO PUSH2 0xC81 JUMPI JUMPDEST ISZERO PUSH2 0xC2C JUMPI PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP6 GT PUSH2 0x637 JUMPI PUSH2 0xA03 DUP6 PUSH2 0x9FA PUSH1 0x1 DUP5 ADD SLOAD PUSH2 0x1695 JUMP JUMPDEST PUSH1 0x1 DUP5 ADD PUSH2 0x1C22 JUMP JUMPDEST PUSH1 0x0 DUP6 PUSH1 0x1F DUP2 GT PUSH1 0x1 EQ PUSH2 0xBBF JUMPI DUP1 PUSH2 0xA32 SWAP3 PUSH1 0x0 SWAP2 PUSH2 0xBB4 JUMPI JUMPDEST POP DUP2 PUSH1 0x1 SHL SWAP2 PUSH1 0x0 NOT SWAP1 PUSH1 0x3 SHL SHR NOT AND OR SWAP1 JUMP JUMPDEST PUSH1 0x1 DUP3 ADD SSTORE JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP4 GT PUSH2 0x637 JUMPI PUSH2 0xA60 DUP4 PUSH2 0xA57 PUSH1 0x2 DUP5 ADD SLOAD PUSH2 0x1695 JUMP JUMPDEST PUSH1 0x2 DUP5 ADD PUSH2 0x1C22 JUMP JUMPDEST PUSH1 0x0 PUSH1 0x1F DUP5 GT PUSH1 0x1 EQ PUSH2 0xB18 JUMPI PUSH32 0xBEE22C24DB7F76BC89936193C155555CC1A2301094F46BBFA4ED0B7B5DD38D4B SWAP5 SWAP3 PUSH2 0xAD8 DUP6 SWAP4 PUSH2 0xABC DUP6 PUSH2 0xAF8 SWAP9 PUSH2 0xAEA SWAP7 PUSH1 0x0 SWAP2 PUSH2 0xB0D JUMPI POP DUP2 PUSH1 0x1 SHL SWAP2 PUSH1 0x0 NOT SWAP1 PUSH1 0x3 SHL SHR NOT AND OR SWAP1 JUMP JUMPDEST PUSH1 0x2 DUP3 ADD SSTORE JUMPDEST PUSH1 0x5 ADD DUP1 SLOAD PUSH1 0xFF NOT AND PUSH1 0xFF PUSH1 0x64 CALLDATALOAD ISZERO ISZERO AND OR SWAP1 SSTORE JUMP JUMPDEST PUSH1 0x40 MLOAD SWAP7 PUSH1 0x60 DUP9 MSTORE PUSH1 0x60 DUP9 ADD SWAP2 PUSH2 0x1C78 JUMP JUMPDEST SWAP2 DUP6 DUP4 SUB PUSH1 0x20 DUP8 ADD MSTORE PUSH2 0x1C78 JUMP JUMPDEST SWAP2 PUSH1 0x64 CALLDATALOAD ISZERO ISZERO PUSH1 0x40 DUP3 ADD MSTORE DUP1 PUSH1 0x4 CALLDATALOAD SWAP4 SUB SWAP1 LOG2 STOP JUMPDEST SWAP1 POP DUP9 ADD CALLDATALOAD DUP13 PUSH2 0xA1E JUMP JUMPDEST PUSH1 0x2 DUP3 ADD PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x0 JUMPDEST PUSH1 0x1F NOT DUP7 AND DUP2 LT PUSH2 0xB9C JUMPI POP SWAP3 PUSH2 0xAD8 DUP6 SWAP4 PUSH2 0xAEA SWAP4 PUSH32 0xBEE22C24DB7F76BC89936193C155555CC1A2301094F46BBFA4ED0B7B5DD38D4B SWAP9 SWAP7 PUSH2 0xAF8 SWAP9 PUSH1 0x1F NOT DUP2 AND LT PUSH2 0xB82 JUMPI JUMPDEST POP POP PUSH1 0x1 DUP6 DUP2 SHL ADD PUSH1 0x2 DUP3 ADD SSTORE PUSH2 0xAC2 JUMP JUMPDEST DUP8 ADD CALLDATALOAD PUSH1 0x0 NOT PUSH1 0x3 DUP9 SWAP1 SHL PUSH1 0xF8 AND SHR NOT AND SWAP1 SSTORE DUP10 DUP1 PUSH2 0xB70 JUMP JUMPDEST SWAP1 SWAP2 PUSH1 0x20 PUSH1 0x1 DUP2 SWAP3 DUP6 DUP11 ADD CALLDATALOAD DUP2 SSTORE ADD SWAP4 ADD SWAP2 ADD PUSH2 0xB28 JUMP JUMPDEST SWAP1 POP DUP5 ADD CALLDATALOAD DUP9 PUSH2 0xA1E JUMP JUMPDEST POP PUSH1 0x1 DUP3 ADD PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x0 JUMPDEST PUSH1 0x1F NOT DUP9 AND DUP2 LT PUSH2 0xC14 JUMPI POP DUP7 PUSH1 0x1F NOT DUP2 AND LT PUSH2 0xBFA JUMPI JUMPDEST POP POP PUSH1 0x1 DUP6 DUP2 SHL ADD PUSH1 0x1 DUP3 ADD SSTORE PUSH2 0xA38 JUMP JUMPDEST DUP4 ADD CALLDATALOAD PUSH1 0x0 NOT PUSH1 0x3 DUP9 SWAP1 SHL PUSH1 0xF8 AND SHR NOT AND SWAP1 SSTORE DUP6 DUP1 PUSH2 0xBE8 JUMP JUMPDEST SWAP1 SWAP2 PUSH1 0x20 PUSH1 0x1 DUP2 SWAP3 DUP6 DUP9 ADD CALLDATALOAD DUP2 SSTORE ADD SWAP4 ADD SWAP2 ADD PUSH2 0xBD0 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x27 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x436C61696D52656769737472793A206E6F7420617574686F72697A656420746F PUSH1 0x44 DUP3 ADD MSTORE PUSH7 0x20757064617465 PUSH1 0xC8 SHL PUSH1 0x64 DUP3 ADD MSTORE PUSH1 0x84 SWAP1 REVERT JUMPDEST POP CALLER PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH32 0xAD3228B676F7D3CD4284A5443F17F1962B36E491B30A40B2405849E597BA5FB5 PUSH1 0x20 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD PUSH1 0xFF AND PUSH2 0x9D6 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x28 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x436C61696D52656769737472793A20636C61696D207479706520646F6573206E PUSH1 0x44 DUP3 ADD MSTORE PUSH8 0x1BDD08195E1A5CDD PUSH1 0xC2 SHL PUSH1 0x64 DUP3 ADD MSTORE PUSH1 0x84 SWAP1 REVERT JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH2 0xD2E PUSH1 0x24 CALLDATALOAD PUSH1 0x4 CALLDATALOAD PUSH2 0x19A2 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH1 0x20 SWAP2 DUP3 DUP3 ADD SWAP3 DUP1 DUP4 MSTORE DUP2 MLOAD DUP1 SWAP5 MSTORE PUSH1 0x40 DUP4 ADD SWAP4 DUP2 PUSH1 0x40 DUP3 PUSH1 0x5 SHL DUP7 ADD ADD SWAP4 ADD SWAP2 PUSH1 0x0 SWAP6 JUMPDEST DUP3 DUP8 LT PUSH2 0xD63 JUMPI DUP6 DUP6 SUB DUP7 RETURN JUMPDEST SWAP1 SWAP2 SWAP3 SWAP4 DUP3 DUP1 PUSH1 0x1 SWAP3 PUSH1 0x3F NOT DUP10 DUP3 SUB ADD DUP6 MSTORE DUP8 MLOAD SWAP1 PUSH2 0xDA8 PUSH2 0xD96 PUSH1 0xC0 DUP5 MLOAD DUP5 MSTORE DUP6 DUP6 ADD MLOAD SWAP1 DUP1 DUP8 DUP7 ADD MSTORE DUP5 ADD SWAP1 PUSH2 0x1655 JUMP JUMPDEST PUSH1 0x40 DUP5 ADD MLOAD DUP4 DUP3 SUB PUSH1 0x40 DUP6 ADD MSTORE PUSH2 0x1655 JUMP JUMPDEST SWAP2 PUSH1 0x60 DUP7 DUP1 PUSH1 0xA0 SHL SUB DUP2 DUP4 ADD MLOAD AND SWAP1 DUP4 ADD MSTORE PUSH1 0x80 DUP1 DUP3 ADD MLOAD SWAP1 DUP4 ADD MSTORE PUSH1 0xA0 DUP1 SWAP2 ADD MLOAD ISZERO ISZERO SWAP2 ADD MSTORE SWAP7 ADD SWAP3 ADD SWAP7 ADD SWAP6 SWAP3 SWAP2 SWAP1 SWAP3 PUSH2 0xD56 JUMP JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x20 PUSH1 0x40 MLOAD PUSH1 0x0 DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x20 PUSH1 0x40 MLOAD PUSH32 0xDF6BC58AF35302F8541FB5D0DA6C4472BE7FC3A416BF34042D13743AC0A50915 DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x1 SLOAD PUSH1 0x0 NOT DUP2 ADD SWAP1 DUP2 GT PUSH2 0xE61 JUMPI PUSH1 0x20 SWAP1 PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH2 0xE90 PUSH2 0x15E4 JUMP JUMPDEST PUSH1 0x4 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x0 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB AND PUSH1 0x0 MSTORE PUSH1 0x20 MSTORE PUSH1 0x20 PUSH1 0xFF PUSH1 0x40 PUSH1 0x0 KECCAK256 SLOAD AND PUSH1 0x40 MLOAD SWAP1 ISZERO ISZERO DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH2 0xEDD PUSH2 0x15CE JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH1 0x3 PUSH1 0x20 MSTORE PUSH1 0x40 SWAP1 KECCAK256 DUP1 SLOAD PUSH1 0x24 CALLDATALOAD SWAP2 SWAP1 DUP3 LT ISZERO PUSH2 0x20C JUMPI PUSH1 0x20 SWAP2 PUSH2 0xF0E SWAP2 PUSH2 0x1627 JUMP JUMPDEST SWAP1 SLOAD SWAP1 PUSH1 0x3 SHL SHR PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x60 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH2 0xF36 PUSH2 0x15CE JUMP JUMPDEST PUSH1 0x44 CALLDATALOAD SWAP1 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB AND PUSH1 0x0 MSTORE PUSH1 0x5 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 PUSH1 0x24 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 DUP1 SLOAD DUP3 LT ISZERO PUSH2 0x20C JUMPI PUSH1 0x20 SWAP2 PUSH2 0xF72 SWAP2 PUSH2 0x1627 JUMP JUMPDEST SWAP1 SLOAD PUSH1 0x40 MLOAD SWAP2 PUSH1 0x3 SHL SHR DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH2 0xF99 PUSH2 0x15E4 JUMP JUMPDEST CALLER PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP3 AND SUB PUSH2 0xFB5 JUMPI PUSH2 0x89C SWAP1 PUSH1 0x4 CALLDATALOAD PUSH2 0x1DB7 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH4 0x334BD919 PUSH1 0xE1 SHL DUP2 MSTORE PUSH1 0x4 SWAP1 REVERT JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH2 0x89C PUSH1 0x4 CALLDATALOAD PUSH2 0xFE6 PUSH2 0x15E4 JUMP JUMPDEST SWAP1 DUP1 PUSH1 0x0 MSTORE PUSH1 0x0 PUSH1 0x20 MSTORE PUSH2 0x1001 PUSH1 0x1 PUSH1 0x40 PUSH1 0x0 KECCAK256 ADD SLOAD PUSH2 0x1D13 JUMP JUMPDEST PUSH2 0x1D39 JUMP JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x4 CALLDATALOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP2 GT PUSH2 0x20C JUMPI PUSH2 0x1036 SWAP1 CALLDATASIZE SWAP1 PUSH1 0x4 ADD PUSH2 0x15FA JUMP JUMPDEST PUSH1 0x24 CALLDATALOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP3 GT PUSH2 0x20C JUMPI PUSH2 0x106E PUSH2 0x105C PUSH2 0x1076 SWAP4 CALLDATASIZE SWAP1 PUSH1 0x4 ADD PUSH2 0x15FA JUMP JUMPDEST SWAP5 SWAP1 SWAP3 PUSH2 0x1067 PUSH2 0x1C99 JUMP JUMPDEST CALLDATASIZE SWAP2 PUSH2 0x1936 JUMP JUMPDEST SWAP3 CALLDATASIZE SWAP2 PUSH2 0x1936 JUMP JUMPDEST PUSH1 0x1 SLOAD SWAP1 PUSH2 0x1083 DUP3 PUSH2 0x197C JUMP JUMPDEST PUSH1 0x1 SSTORE PUSH1 0x40 MLOAD PUSH2 0x1092 DUP2 PUSH2 0x1765 JUMP JUMPDEST DUP3 DUP2 MSTORE PUSH1 0x20 DUP2 ADD SWAP1 DUP5 DUP3 MSTORE PUSH1 0x40 DUP2 ADD DUP4 DUP2 MSTORE PUSH1 0x60 DUP3 ADD CALLER DUP2 MSTORE PUSH1 0x80 DUP4 ADD SWAP2 TIMESTAMP DUP4 MSTORE PUSH1 0x1 PUSH1 0xA0 DUP6 ADD MSTORE DUP7 PUSH1 0x0 MSTORE PUSH1 0x2 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 SWAP5 DUP5 MLOAD DUP7 SSTORE MLOAD DUP1 MLOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP3 GT PUSH2 0x637 JUMPI PUSH2 0x10F9 DUP3 PUSH2 0x10F0 PUSH1 0x1 DUP11 ADD SLOAD PUSH2 0x1695 JUMP JUMPDEST PUSH1 0x1 DUP11 ADD PUSH2 0x1C22 JUMP JUMPDEST PUSH1 0x20 SWAP1 PUSH1 0x1F DUP4 GT PUSH1 0x1 EQ PUSH2 0x12EF JUMPI PUSH2 0x1129 SWAP3 SWAP2 PUSH1 0x0 SWAP2 DUP4 PUSH2 0x12E4 JUMPI POP POP DUP2 PUSH1 0x1 SHL SWAP2 PUSH1 0x0 NOT SWAP1 PUSH1 0x3 SHL SHR NOT AND OR SWAP1 JUMP JUMPDEST PUSH1 0x1 DUP7 ADD SSTORE JUMPDEST MLOAD DUP1 MLOAD PUSH1 0x2 DUP7 ADD SWAP2 PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP3 GT PUSH2 0x637 JUMPI PUSH2 0x1159 DUP3 PUSH2 0x1153 DUP6 SLOAD PUSH2 0x1695 JUMP JUMPDEST DUP6 PUSH2 0x1C22 JUMP JUMPDEST PUSH1 0x20 SWAP1 PUSH1 0x1F DUP4 GT PUSH1 0x1 EQ PUSH2 0x1270 JUMPI SWAP3 PUSH2 0x1196 DUP4 PUSH1 0x5 SWAP8 SWAP5 PUSH1 0xA0 SWAP8 SWAP5 PUSH2 0x11D9 SWAP12 SWAP11 SWAP8 PUSH1 0x0 SWAP3 PUSH2 0x1265 JUMPI POP POP DUP2 PUSH1 0x1 SHL SWAP2 PUSH1 0x0 NOT SWAP1 PUSH1 0x3 SHL SHR NOT AND OR SWAP1 JUMP JUMPDEST SWAP1 SSTORE JUMPDEST PUSH1 0x3 DUP7 ADD SWAP1 PUSH1 0x1 DUP1 DUP7 SHL SUB SWAP1 MLOAD AND PUSH12 0xFFFFFFFFFFFFFFFFFFFFFFFF DUP6 SHL DUP3 SLOAD AND OR SWAP1 SSTORE MLOAD PUSH1 0x4 DUP6 ADD SSTORE ADD MLOAD ISZERO ISZERO SWAP2 ADD SWAP1 PUSH1 0xFF DUP1 NOT DUP4 SLOAD AND SWAP2 ISZERO ISZERO AND OR SWAP1 SSTORE JUMP JUMPDEST CALLER PUSH1 0x0 MSTORE PUSH1 0x3 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 SWAP3 DUP4 SLOAD SWAP4 PUSH1 0x1 PUSH1 0x40 SHL DUP6 LT ISZERO PUSH2 0x637 JUMPI PUSH2 0x124C DUP5 SWAP3 DUP4 PUSH2 0x1233 PUSH2 0x5ED DUP10 PUSH32 0xB32C7C206740E72055364DB6657B89F760889A8633965910821D462A0E21682B SWAP7 PUSH1 0x1 PUSH1 0x20 SWAP13 ADD DUP2 SSTORE PUSH2 0x1627 JUMP JUMPDEST SWAP1 SSTORE PUSH2 0x125A PUSH1 0x40 MLOAD SWAP3 DUP4 SWAP3 PUSH1 0x40 DUP5 MSTORE PUSH1 0x40 DUP5 ADD SWAP1 PUSH2 0x1655 JUMP JUMPDEST DUP3 DUP2 SUB DUP10 DUP5 ADD MSTORE CALLER SWAP7 PUSH2 0x1655 JUMP JUMPDEST SUB SWAP1 LOG3 PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST ADD MLOAD SWAP1 POP DUP15 DUP1 PUSH2 0x460 JUMP JUMPDEST SWAP1 PUSH1 0x1F NOT DUP4 AND SWAP2 DUP5 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP3 PUSH1 0x0 JUMPDEST DUP2 DUP2 LT PUSH2 0x12CC JUMPI POP SWAP4 PUSH1 0xA0 SWAP7 SWAP4 PUSH2 0x11D9 SWAP11 SWAP10 SWAP7 SWAP4 PUSH1 0x1 SWAP4 DUP4 PUSH1 0x5 SWAP12 SWAP9 LT PUSH2 0x12B3 JUMPI JUMPDEST POP POP POP DUP2 SHL ADD SWAP1 SSTORE PUSH2 0x1199 JUMP JUMPDEST ADD MLOAD PUSH1 0x0 NOT PUSH1 0xF8 DUP5 PUSH1 0x3 SHL AND SHR NOT AND SWAP1 SSTORE DUP14 DUP1 DUP1 PUSH2 0x12A6 JUMP JUMPDEST SWAP3 SWAP4 PUSH1 0x20 PUSH1 0x1 DUP2 SWAP3 DUP8 DUP7 ADD MLOAD DUP2 SSTORE ADD SWAP6 ADD SWAP4 ADD PUSH2 0x1284 JUMP JUMPDEST ADD MLOAD SWAP1 POP DUP12 DUP1 PUSH2 0x460 JUMP JUMPDEST SWAP1 PUSH1 0x1F NOT DUP4 AND SWAP2 PUSH1 0x1 DUP10 ADD PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP3 PUSH1 0x0 JUMPDEST DUP2 DUP2 LT PUSH2 0x1345 JUMPI POP SWAP1 DUP5 PUSH1 0x1 SWAP6 SWAP5 SWAP4 SWAP3 LT PUSH2 0x132C JUMPI JUMPDEST POP POP POP DUP2 SHL ADD PUSH1 0x1 DUP7 ADD SSTORE PUSH2 0x112F JUMP JUMPDEST ADD MLOAD PUSH1 0x0 NOT PUSH1 0xF8 DUP5 PUSH1 0x3 SHL AND SHR NOT AND SWAP1 SSTORE DUP11 DUP1 DUP1 PUSH2 0x131C JUMP JUMPDEST SWAP3 SWAP4 PUSH1 0x20 PUSH1 0x1 DUP2 SWAP3 DUP8 DUP7 ADD MLOAD DUP2 SSTORE ADD SWAP6 ADD SWAP4 ADD PUSH2 0x1306 JUMP JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x20 PUSH2 0x1384 PUSH2 0x137B PUSH2 0x15CE JUMP JUMPDEST PUSH1 0x24 CALLDATALOAD SWAP1 PUSH2 0x17D1 JUMP JUMPDEST PUSH1 0x40 MLOAD SWAP1 ISZERO ISZERO DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x4 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x0 PUSH1 0x20 MSTORE PUSH1 0x20 PUSH1 0x1 PUSH1 0x40 PUSH1 0x0 KECCAK256 ADD SLOAD PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x40 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB PUSH2 0x13DE PUSH2 0x15CE JUMP JUMPDEST AND PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x5 DUP2 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 PUSH1 0x24 CALLDATALOAD PUSH1 0x0 MSTORE DUP2 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 PUSH1 0x40 MLOAD SWAP1 DUP2 DUP4 DUP3 SLOAD SWAP2 DUP3 DUP2 MSTORE ADD SWAP1 DUP2 SWAP3 PUSH1 0x0 MSTORE DUP5 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x0 JUMPDEST DUP7 DUP3 DUP3 LT PUSH2 0x146B JUMPI DUP7 DUP7 PUSH2 0x142B DUP3 DUP9 SUB DUP4 PUSH2 0x179C JUMP JUMPDEST PUSH1 0x40 MLOAD SWAP3 DUP4 SWAP3 DUP2 DUP5 ADD SWAP1 DUP3 DUP6 MSTORE MLOAD DUP1 SWAP2 MSTORE PUSH1 0x40 DUP5 ADD SWAP3 SWAP2 PUSH1 0x0 JUMPDEST DUP3 DUP2 LT PUSH2 0x1454 JUMPI POP POP POP POP SUB SWAP1 RETURN JUMPDEST DUP4 MLOAD DUP6 MSTORE DUP7 SWAP6 POP SWAP4 DUP2 ADD SWAP4 SWAP3 DUP2 ADD SWAP3 PUSH1 0x1 ADD PUSH2 0x1445 JUMP JUMPDEST DUP4 SLOAD DUP6 MSTORE SWAP1 SWAP4 ADD SWAP3 PUSH1 0x1 SWAP3 DUP4 ADD SWAP3 ADD PUSH2 0x1415 JUMP JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x4 CALLDATALOAD PUSH1 0x0 MSTORE PUSH1 0x2 PUSH1 0x20 MSTORE PUSH1 0x20 PUSH1 0x40 PUSH1 0x0 KECCAK256 DUP1 SLOAD ISZERO ISZERO SWAP1 DUP2 PUSH2 0x14B8 JUMPI JUMPDEST POP PUSH1 0x40 MLOAD SWAP1 ISZERO ISZERO DUP2 MSTORE RETURN JUMPDEST PUSH1 0xFF SWAP2 POP PUSH1 0x5 ADD SLOAD AND DUP3 PUSH2 0x14AD JUMP JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x20 DUP1 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x20C JUMPI PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB PUSH2 0x14E9 PUSH2 0x15CE JUMP JUMPDEST AND PUSH1 0x0 MSTORE PUSH1 0x3 DUP2 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 PUSH1 0x40 MLOAD SWAP1 DUP2 DUP4 DUP3 SLOAD SWAP2 DUP3 DUP2 MSTORE ADD SWAP1 DUP2 SWAP3 PUSH1 0x0 MSTORE DUP5 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x0 JUMPDEST DUP7 DUP3 DUP3 LT PUSH2 0x1567 JUMPI DUP7 DUP7 PUSH2 0x1527 DUP3 DUP9 SUB DUP4 PUSH2 0x179C JUMP JUMPDEST PUSH1 0x40 MLOAD SWAP3 DUP4 SWAP3 DUP2 DUP5 ADD SWAP1 DUP3 DUP6 MSTORE MLOAD DUP1 SWAP2 MSTORE PUSH1 0x40 DUP5 ADD SWAP3 SWAP2 PUSH1 0x0 JUMPDEST DUP3 DUP2 LT PUSH2 0x1550 JUMPI POP POP POP POP SUB SWAP1 RETURN JUMPDEST DUP4 MLOAD DUP6 MSTORE DUP7 SWAP6 POP SWAP4 DUP2 ADD SWAP4 SWAP3 DUP2 ADD SWAP3 PUSH1 0x1 ADD PUSH2 0x1541 JUMP JUMPDEST DUP4 SLOAD DUP6 MSTORE SWAP1 SWAP4 ADD SWAP3 PUSH1 0x1 SWAP3 DUP4 ADD SWAP3 ADD PUSH2 0x1511 JUMP JUMPDEST CALLVALUE PUSH2 0x20C JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x20C JUMPI PUSH1 0x4 CALLDATALOAD SWAP1 PUSH4 0xFFFFFFFF PUSH1 0xE0 SHL DUP3 AND DUP1 SWAP3 SUB PUSH2 0x20C JUMPI PUSH1 0x20 SWAP2 PUSH4 0x7965DB0B PUSH1 0xE0 SHL DUP2 EQ SWAP1 DUP2 ISZERO PUSH2 0x15BD JUMPI JUMPDEST POP ISZERO ISZERO DUP2 MSTORE RETURN JUMPDEST PUSH4 0x1FFC9A7 PUSH1 0xE0 SHL EQ SWAP1 POP DUP4 PUSH2 0x15B6 JUMP JUMPDEST PUSH1 0x4 CALLDATALOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP3 AND DUP3 SUB PUSH2 0x20C JUMPI JUMP JUMPDEST PUSH1 0x24 CALLDATALOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP3 AND DUP3 SUB PUSH2 0x20C JUMPI JUMP JUMPDEST SWAP2 DUP2 PUSH1 0x1F DUP5 ADD SLT ISZERO PUSH2 0x20C JUMPI DUP3 CALLDATALOAD SWAP2 PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP4 GT PUSH2 0x20C JUMPI PUSH1 0x20 DUP4 DUP2 DUP7 ADD SWAP6 ADD ADD GT PUSH2 0x20C JUMPI JUMP JUMPDEST DUP1 SLOAD DUP3 LT ISZERO PUSH2 0x163F JUMPI PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 ADD SWAP1 PUSH1 0x0 SWAP1 JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x32 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST SWAP2 SWAP1 DUP3 MLOAD SWAP3 DUP4 DUP3 MSTORE PUSH1 0x0 JUMPDEST DUP5 DUP2 LT PUSH2 0x1681 JUMPI POP POP DUP3 PUSH1 0x0 PUSH1 0x20 DUP1 SWAP5 SWAP6 DUP5 ADD ADD MSTORE PUSH1 0x1F DUP1 NOT SWAP2 ADD AND ADD ADD SWAP1 JUMP JUMPDEST PUSH1 0x20 DUP2 DUP4 ADD DUP2 ADD MLOAD DUP5 DUP4 ADD DUP3 ADD MSTORE ADD PUSH2 0x1660 JUMP JUMPDEST SWAP1 PUSH1 0x1 DUP3 DUP2 SHR SWAP3 AND DUP1 ISZERO PUSH2 0x16C5 JUMPI JUMPDEST PUSH1 0x20 DUP4 LT EQ PUSH2 0x16AF JUMPI JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x22 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST SWAP2 PUSH1 0x7F AND SWAP2 PUSH2 0x16A4 JUMP JUMPDEST DUP1 SLOAD PUSH1 0x0 SWAP4 SWAP3 PUSH2 0x16DE DUP3 PUSH2 0x1695 JUMP JUMPDEST SWAP2 DUP3 DUP3 MSTORE PUSH1 0x20 SWAP4 PUSH1 0x1 SWAP2 PUSH1 0x1 DUP2 AND SWAP1 DUP2 PUSH1 0x0 EQ PUSH2 0x1746 JUMPI POP PUSH1 0x1 EQ PUSH2 0x1705 JUMPI JUMPDEST POP POP POP POP POP JUMP JUMPDEST SWAP1 SWAP4 SWAP5 SWAP6 POP PUSH1 0x0 SWAP3 SWAP2 SWAP3 MSTORE DUP4 PUSH1 0x0 KECCAK256 SWAP3 DUP5 PUSH1 0x0 SWAP5 JUMPDEST DUP4 DUP7 LT PUSH2 0x1732 JUMPI POP POP POP POP ADD ADD SWAP1 CODESIZE DUP1 DUP1 DUP1 DUP1 PUSH2 0x16FE JUMP JUMPDEST DUP1 SLOAD DUP6 DUP8 ADD DUP4 ADD MSTORE SWAP5 ADD SWAP4 DUP6 SWAP1 DUP3 ADD PUSH2 0x171A JUMP JUMPDEST PUSH1 0xFF NOT AND DUP7 DUP6 ADD MSTORE POP POP POP SWAP1 ISZERO ISZERO PUSH1 0x5 SHL ADD ADD SWAP2 POP CODESIZE DUP1 DUP1 DUP1 DUP1 PUSH2 0x16FE JUMP JUMPDEST PUSH1 0xC0 DUP2 ADD SWAP1 DUP2 LT PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP3 GT OR PUSH2 0x637 JUMPI PUSH1 0x40 MSTORE JUMP JUMPDEST PUSH2 0x120 DUP2 ADD SWAP1 DUP2 LT PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP3 GT OR PUSH2 0x637 JUMPI PUSH1 0x40 MSTORE JUMP JUMPDEST SWAP1 PUSH1 0x1F DUP1 NOT SWAP2 ADD AND DUP2 ADD SWAP1 DUP2 LT PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP3 GT OR PUSH2 0x637 JUMPI PUSH1 0x40 MSTORE JUMP JUMPDEST DUP1 MLOAD DUP3 LT ISZERO PUSH2 0x163F JUMPI PUSH1 0x20 SWAP2 PUSH1 0x5 SHL ADD ADD SWAP1 JUMP JUMPDEST PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB DUP1 PUSH1 0x0 SWAP3 AND DUP3 MSTORE PUSH1 0x5 SWAP2 PUSH1 0x20 SWAP3 PUSH1 0x5 DUP5 MSTORE PUSH1 0x40 SWAP5 DUP6 DUP4 KECCAK256 SWAP1 DUP4 MSTORE DUP5 MSTORE DUP5 DUP3 KECCAK256 SWAP5 DUP1 MLOAD DUP1 DUP8 DUP8 DUP3 SWAP10 SLOAD SWAP4 DUP5 DUP2 MSTORE ADD SWAP1 DUP7 MSTORE DUP8 DUP7 KECCAK256 SWAP3 DUP7 JUMPDEST DUP10 DUP3 DUP3 LT PUSH2 0x1920 JUMPI POP POP POP PUSH2 0x1825 SWAP3 POP SUB DUP8 PUSH2 0x179C JUMP JUMPDEST DUP3 JUMPDEST DUP7 MLOAD DUP2 LT ISZERO PUSH2 0x1916 JUMPI PUSH2 0x183A DUP2 DUP9 PUSH2 0x17BD JUMP JUMPDEST MLOAD DUP5 MSTORE PUSH1 0x4 DUP1 DUP8 MSTORE DUP3 DUP6 KECCAK256 DUP4 MLOAD SWAP1 PUSH2 0x1851 DUP3 PUSH2 0x1780 JUMP JUMPDEST DUP1 SLOAD DUP3 MSTORE PUSH2 0x17F PUSH2 0x1898 PUSH1 0x1 SWAP5 DUP11 DUP7 DUP6 ADD SLOAD AND DUP13 DUP7 ADD MSTORE DUP11 PUSH1 0x2 DUP6 ADD SLOAD AND DUP9 DUP7 ADD MSTORE DUP8 MLOAD PUSH2 0x1886 DUP2 PUSH2 0x17F DUP2 PUSH1 0x3 DUP10 ADD PUSH2 0x16CF JUMP JUMPDEST PUSH1 0x60 DUP7 ADD MSTORE DUP8 MLOAD SWAP3 DUP4 DUP1 SWAP3 DUP7 ADD PUSH2 0x16CF JUMP JUMPDEST PUSH1 0x80 DUP4 ADD MSTORE DUP5 MLOAD PUSH2 0x18AF DUP2 PUSH2 0x17F DUP2 DUP11 DUP7 ADD PUSH2 0x16CF JUMP JUMPDEST PUSH1 0xA0 DUP4 ADD MSTORE PUSH1 0x6 DUP2 ADD SLOAD PUSH1 0xC0 DUP4 ADD MSTORE PUSH1 0xFF PUSH1 0x8 PUSH1 0x7 DUP4 ADD SLOAD SWAP3 DUP4 PUSH1 0xE0 DUP7 ADD MSTORE ADD SLOAD AND ISZERO SWAP2 PUSH2 0x100 DUP4 ISZERO SWAP2 ADD MSTORE DUP2 PUSH2 0x18FA JUMPI JUMPDEST POP PUSH2 0x18EF JUMPI POP PUSH1 0x1 ADD PUSH2 0x1827 JUMP JUMPDEST SWAP7 POP POP POP POP POP POP POP SWAP1 JUMP JUMPDEST DUP1 ISZERO SWAP2 POP DUP2 ISZERO PUSH2 0x190C JUMPI JUMPDEST POP CODESIZE PUSH2 0x18E1 JUMP JUMPDEST SWAP1 POP TIMESTAMP LT CODESIZE PUSH2 0x1905 JUMP JUMPDEST POP POP POP SWAP3 POP POP POP SWAP1 JUMP JUMPDEST DUP6 SLOAD DUP5 MSTORE PUSH1 0x1 SWAP6 DUP7 ADD SWAP6 DUP13 SWAP6 POP SWAP4 ADD SWAP3 ADD PUSH2 0x180E JUMP JUMPDEST SWAP3 SWAP2 SWAP3 PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP3 GT PUSH2 0x637 JUMPI PUSH1 0x40 MLOAD SWAP2 PUSH2 0x195F PUSH1 0x1F DUP3 ADD PUSH1 0x1F NOT AND PUSH1 0x20 ADD DUP5 PUSH2 0x179C JUMP JUMPDEST DUP3 SWAP5 DUP2 DUP5 MSTORE DUP2 DUP4 ADD GT PUSH2 0x20C JUMPI DUP3 DUP2 PUSH1 0x20 SWAP4 DUP5 PUSH1 0x0 SWAP7 ADD CALLDATACOPY ADD ADD MSTORE JUMP JUMPDEST PUSH1 0x0 NOT DUP2 EQ PUSH2 0xE61 JUMPI PUSH1 0x1 ADD SWAP1 JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP2 GT PUSH2 0x637 JUMPI PUSH1 0x5 SHL PUSH1 0x20 ADD SWAP1 JUMP JUMPDEST DUP2 ISZERO ISZERO DUP1 PUSH2 0x1C17 JUMPI JUMPDEST ISZERO PUSH2 0x1BD2 JUMPI PUSH1 0x0 PUSH1 0x1 SWAP3 PUSH1 0x1 SLOAD SWAP4 JUMPDEST DUP5 DUP2 LT PUSH2 0x1B9C JUMPI POP DUP2 DUP4 LT ISZERO PUSH2 0x1B75 JUMPI DUP3 DUP3 SUB SWAP2 DUP3 GT PUSH2 0xE61 JUMPI DUP1 DUP3 GT PUSH2 0x1B6D JUMPI JUMPDEST POP PUSH2 0x19E6 DUP2 PUSH2 0x198B JUMP JUMPDEST PUSH1 0x40 SWAP1 PUSH2 0x19F5 DUP3 MLOAD SWAP2 DUP3 PUSH2 0x179C JUMP JUMPDEST DUP3 DUP2 MSTORE PUSH1 0x1F NOT PUSH2 0x1A04 DUP5 PUSH2 0x198B JUMP JUMPDEST ADD PUSH1 0x0 JUMPDEST DUP2 DUP2 LT PUSH2 0x1B2F JUMPI POP POP PUSH1 0x0 SWAP1 DUP2 SWAP4 PUSH1 0x1 JUMPDEST DUP8 DUP2 LT DUP1 PUSH2 0x1B26 JUMPI JUMPDEST ISZERO PUSH2 0x1B1B JUMPI DUP1 PUSH1 0x0 MSTORE PUSH1 0x2 PUSH1 0x20 SWAP1 DUP1 DUP3 MSTORE DUP7 PUSH1 0x0 KECCAK256 PUSH1 0xFF PUSH1 0x5 DUP3 ADD SLOAD AND SWAP2 DUP3 PUSH2 0x1A58 JUMPI JUMPDEST POP POP POP POP PUSH2 0x1A53 SWAP1 PUSH2 0x197C JUMP JUMPDEST PUSH2 0x1A19 JUMP JUMPDEST DUP9 DUP12 DUP12 SWAP7 SWAP12 LT ISZERO PUSH2 0x1A7F JUMPI JUMPDEST POP POP POP POP POP PUSH2 0x1A75 PUSH2 0x1A53 SWAP2 PUSH2 0x197C JUMP JUMPDEST SWAP6 SWAP1 CODESIZE DUP1 DUP1 PUSH2 0x1A46 JUMP JUMPDEST SWAP3 PUSH1 0x4 PUSH2 0x1A53 SWAP7 SWAP10 SWAP4 PUSH2 0x17F PUSH2 0x1AD1 PUSH2 0x1A75 SWAP9 SWAP6 DUP9 PUSH2 0x1B11 SWAP10 MLOAD SWAP8 PUSH2 0x1AA3 DUP10 PUSH2 0x1765 JUMP JUMPDEST DUP6 SLOAD DUP10 MSTORE DUP2 MLOAD SWAP1 PUSH2 0x1AC2 DUP3 PUSH2 0x1ABB DUP2 PUSH1 0x1 DUP12 ADD PUSH2 0x16CF JUMP JUMPDEST SUB DUP4 PUSH2 0x179C JUMP JUMPDEST DUP10 ADD MSTORE MLOAD SWAP3 DUP4 DUP1 SWAP3 DUP7 ADD PUSH2 0x16CF JUMP JUMPDEST DUP5 DUP14 ADD MSTORE PUSH1 0x3 DUP2 ADD SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH1 0x60 DUP6 ADD MSTORE ADD SLOAD PUSH1 0x80 DUP4 ADD MSTORE ISZERO ISZERO PUSH1 0xA0 DUP3 ADD MSTORE PUSH2 0x1B00 DUP3 DUP9 PUSH2 0x17BD JUMP JUMPDEST MSTORE PUSH2 0x1B0B DUP2 DUP8 PUSH2 0x17BD JUMP JUMPDEST POP PUSH2 0x197C JUMP JUMPDEST SWAP5 SWAP2 CODESIZE DUP1 DUP9 PUSH2 0x1A64 JUMP JUMPDEST POP POP SWAP5 POP POP POP POP POP SWAP1 JUMP JUMPDEST POP DUP2 DUP5 LT PUSH2 0x1A22 JUMP JUMPDEST PUSH1 0x20 SWAP1 DUP5 MLOAD PUSH2 0x1B3D DUP2 PUSH2 0x1765 JUMP JUMPDEST PUSH1 0x0 DUP2 MSTORE PUSH1 0x60 PUSH1 0x0 DUP5 SWAP2 DUP1 DUP4 DUP6 ADD MSTORE DUP1 DUP10 DUP6 ADD MSTORE DUP4 ADD MSTORE PUSH1 0x0 PUSH1 0x80 DUP4 ADD MSTORE PUSH1 0x0 PUSH1 0xA0 DUP4 ADD MSTORE DUP3 DUP7 ADD ADD MSTORE ADD PUSH2 0x1A08 JUMP JUMPDEST SWAP1 POP CODESIZE PUSH2 0x19DC JUMP JUMPDEST POP POP POP POP PUSH1 0x40 MLOAD PUSH1 0x20 DUP2 ADD DUP2 DUP2 LT PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP3 GT OR PUSH2 0x637 JUMPI PUSH1 0x40 MSTORE PUSH1 0x0 DUP2 MSTORE SWAP1 JUMP JUMPDEST DUP1 PUSH1 0x0 MSTORE PUSH1 0x2 PUSH1 0x20 MSTORE PUSH1 0xFF PUSH1 0x5 PUSH1 0x40 PUSH1 0x0 KECCAK256 ADD SLOAD AND PUSH2 0x1BBE JUMPI JUMPDEST PUSH1 0x1 ADD PUSH2 0x19BA JUMP JUMPDEST SWAP2 PUSH2 0x1BCA PUSH1 0x1 SWAP2 PUSH2 0x197C JUMP JUMPDEST SWAP3 SWAP1 POP PUSH2 0x1BB6 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x1C PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x436C61696D52656769737472793A20696E76616C6964206C696D697400000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST POP PUSH1 0x64 DUP3 GT ISZERO PUSH2 0x19AB JUMP JUMPDEST SWAP1 PUSH1 0x1F DUP2 GT PUSH2 0x1C30 JUMPI POP POP POP JUMP JUMPDEST PUSH1 0x0 SWAP2 PUSH1 0x0 MSTORE PUSH1 0x20 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x20 PUSH1 0x1F DUP6 ADD PUSH1 0x5 SHR DUP4 ADD SWAP5 LT PUSH2 0x1C6E JUMPI JUMPDEST PUSH1 0x1F ADD PUSH1 0x5 SHR ADD SWAP2 JUMPDEST DUP3 DUP2 LT PUSH2 0x1C63 JUMPI POP POP POP JUMP JUMPDEST DUP2 DUP2 SSTORE PUSH1 0x1 ADD PUSH2 0x1C57 JUMP JUMPDEST SWAP1 SWAP3 POP DUP3 SWAP1 PUSH2 0x1C4E JUMP JUMPDEST SWAP1 DUP1 PUSH1 0x20 SWAP4 SWAP3 DUP2 DUP5 MSTORE DUP5 DUP5 ADD CALLDATACOPY PUSH1 0x0 DUP3 DUP3 ADD DUP5 ADD MSTORE PUSH1 0x1F ADD PUSH1 0x1F NOT AND ADD ADD SWAP1 JUMP JUMPDEST CALLER PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH32 0x52A9CC15D02F8D37B4D6F72E1EF19BC07BF335BB0FE54C472AFEF4CE2D4549F0 PUSH1 0x20 MSTORE PUSH1 0x40 SWAP1 KECCAK256 SLOAD PUSH32 0xDF6BC58AF35302F8541FB5D0DA6C4472BE7FC3A416BF34042D13743AC0A50915 SWAP1 PUSH1 0xFF AND ISZERO PUSH2 0x1CF5 JUMPI POP JUMP JUMPDEST PUSH1 0x44 SWAP1 PUSH1 0x40 MLOAD SWAP1 PUSH4 0xE2517D3F PUSH1 0xE0 SHL DUP3 MSTORE CALLER PUSH1 0x4 DUP4 ADD MSTORE PUSH1 0x24 DUP3 ADD MSTORE REVERT JUMPDEST DUP1 PUSH1 0x0 MSTORE PUSH1 0x0 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 CALLER PUSH1 0x0 MSTORE PUSH1 0x20 MSTORE PUSH1 0xFF PUSH1 0x40 PUSH1 0x0 KECCAK256 SLOAD AND ISZERO PUSH2 0x1CF5 JUMPI POP JUMP JUMPDEST SWAP1 PUSH1 0x0 SWAP2 DUP1 DUP4 MSTORE DUP3 PUSH1 0x20 MSTORE PUSH1 0x40 DUP4 KECCAK256 SWAP2 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB AND SWAP2 DUP3 DUP5 MSTORE PUSH1 0x20 MSTORE PUSH1 0xFF PUSH1 0x40 DUP5 KECCAK256 SLOAD AND ISZERO PUSH1 0x0 EQ PUSH2 0x1DB2 JUMPI DUP1 DUP4 MSTORE DUP3 PUSH1 0x20 MSTORE PUSH1 0x40 DUP4 KECCAK256 DUP3 DUP5 MSTORE PUSH1 0x20 MSTORE PUSH1 0x40 DUP4 KECCAK256 PUSH1 0x1 PUSH1 0xFF NOT DUP3 SLOAD AND OR SWAP1 SSTORE PUSH32 0x2F8788117E7EFF1D82E926EC794901D17C78024A50270940304540A733656F0D CALLER SWAP4 DUP1 LOG4 PUSH1 0x1 SWAP1 JUMP JUMPDEST POP POP SWAP1 JUMP JUMPDEST SWAP1 PUSH1 0x0 SWAP2 DUP1 DUP4 MSTORE DUP3 PUSH1 0x20 MSTORE PUSH1 0x40 DUP4 KECCAK256 SWAP2 PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB AND SWAP2 DUP3 DUP5 MSTORE PUSH1 0x20 MSTORE PUSH1 0xFF PUSH1 0x40 DUP5 KECCAK256 SLOAD AND PUSH1 0x0 EQ PUSH2 0x1DB2 JUMPI DUP1 DUP4 MSTORE DUP3 PUSH1 0x20 MSTORE PUSH1 0x40 DUP4 KECCAK256 DUP3 DUP5 MSTORE PUSH1 0x20 MSTORE PUSH1 0x40 DUP4 KECCAK256 PUSH1 0xFF NOT DUP2 SLOAD AND SWAP1 SSTORE PUSH32 0xF6391F5C32D9C69D2A47EA670B442974B53935D1EDC7FD64EB21E047A839171B CALLER SWAP4 DUP1 LOG4 PUSH1 0x1 SWAP1 JUMP INVALID LOG2 PUSH5 0x6970667358 0x22 SLT KECCAK256 0xB4 SWAP11 SLOAD 0xB8 PUSH21 0xCCC1160A18B7D3B9CAD0FCC7D698618D886AE2709A 0x1F PUSH0 SWAP9 0xC1 DUP14 CREATE PUSH5 0x736F6C6343 STOP ADDMOD AND STOP CALLER ", "sourceMap": "226:7939:5:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;226:7939:5;;;;;;;;;;;;;;;;;;;;;;1191:39;;226:7939;1191:39;;226:7939;;1191:39;;;;226:7939;;;;;1191:39;226:7939;1191:39;226:7939;1191:39;;;;226:7939;:::i;:::-;;;;:::i;:::-;;;;;1191:39;226:7939;1191:39;226:7939;1191:39;;226:7939;:::i;:::-;;;;:::i;:::-;;;;1191:39;226:7939;1191:39;226:7939;1191:39;;;;226:7939;:::i;:::-;;1191:39;;;226:7939;1191:39;226:7939;1191:39;;;;226:7939;1191:39;;226:7939;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;:::i;:::-;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;226:7939:5;;;;;;;404:32;226:7939;;;;;;;;;-1:-1:-1;;226:7939:5;;;;;;:::i;:::-;;;-1:-1:-1;;;;;226:7939:5;;;;;;;;;;;:::i;:::-;;;;;-1:-1:-1;;;;;226:7939:5;;;;;;;;;;;:::i;:::-;;;;;;-1:-1:-1;;;;;226:7939:5;;;;;;;;;;;:::i;:::-;2475:4:0;;;;;:::i;:::-;226:7939:5;;;;4712:10;226:7939;;;;;;4712:29;226:7939;;;;;;4712:10;226:7939;;;4797:28;226:7939;;;4797:28;226:7939;;;;;;;;;;;;;;4919:71;;226:7939;;;;;;;4956:10;226:7939;;;;;;4968:15;226:7939;;;;;;;;;;4919:71;226:7939;;;;;;;;;;4919:71;226:7939;4919:71;;;;;;;:::i;:::-;226:7939;;;4919:71;;4896:104;226:7939;;;;;;;;:::i;:::-;;;;;;;5029:294;;4956:10;;226:7939;;;5029:294;;226:7939;;;;;;;;;;;;;:::i;:::-;5029:294;226:7939;5029:294;;226:7939;;;;;;:::i;:::-;;5029:294;;226:7939;;;;;;:::i;:::-;;5029:294;;226:7939;4968:15;226:7939;5029:294;;226:7939;;;;5029:294;;226:7939;-1:-1:-1;5029:294:5;;;226:7939;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;226:7939:5;;;-1:-1:-1;;;;;226:7939:5;;;;;;;;;4712:10;226:7939;;;;;;;;;;;;;;;;-1:-1:-1;;;;;226:7939:5;;;;;;;;;;;;:::i;:::-;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5029:294;;226:7939;;;;-1:-1:-1;;;;;226:7939:5;;;;;;;;;;;;:::i;:::-;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5029:294;;226:7939;;;;-1:-1:-1;;;;;226:7939:5;;;;;;;4797:28;226:7939;;;;:::i;:::-;4797:28;226:7939;;;:::i;:::-;;;;;;;;;;;;;;;5029:294;226:7939;;;;;;;;;;;;;;;;;;;;;;;;;;4797:28;226:7939;;;;;5029:294;;226:7939;;;;;;5029:294;;226:7939;;;;;5029:294;226:7939;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4797:28;226:7939;;;;;;;;;;;;;;;;;;-1:-1:-1;;;226:7939:5;;;;;;;;;;;;5392:57;226:7939;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;4956:10;;;;226:7939;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5392:57;;;226:7939;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;226:7939:5;;;;;;;;;;;4797:28;226:7939;;;;;;;;;;;;;;;;;5029:294;226:7939;;;;;;;;;;;;;;;;;;;;4797:28;226:7939;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;226:7939:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;226:7939:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;226:7939:5;;;;;;;;;;;;;;;;;-1:-1:-1;;;226:7939:5;;;;;;;;;;-1:-1:-1;;;226:7939:5;;;;;;;;;;;;;;;;;-1:-1:-1;;;226:7939:5;;;;;;;;;;;;;-1:-1:-1;;226:7939:5;;;;4747:26:0;226:7939:5;;;;:::i;:::-;;;;;;;;2475:4:0;226:7939:5;;;;3901:22:0;226:7939:5;2475:4:0;:::i;:::-;4747:26;:::i;:::-;226:7939:5;;;;;;;-1:-1:-1;;226:7939:5;;;;;;;;734:47;226:7939;;;;;;;;;734:47;226:7939;734:47;226:7939;734:47;226:7939;734:47;;226:7939;:::i;:::-;;;;734:47;226:7939;734:47;;;;226:7939;:::i;:::-;;;;;;734:47;;;226:7939;;734:47;226:7939;;734:47;226:7939;734:47;;226:7939;734:47;;226:7939;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;226:7939:5;;;;;;-1:-1:-1;;;;;226:7939:5;;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;;226:7939:5;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;3916:10;226:7939;;;;;;;3957:17;226:7939;;4050:17;;;226:7939;-1:-1:-1;;;;;226:7939:5;4071:10;4050:31;:74;;;;226:7939;;;;-1:-1:-1;;;;;226:7939:5;;;;;4200:14;226:7939;;4200:14;;226:7939;;:::i;:::-;;4200:14;;226:7939;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4200:14;;226:7939;;-1:-1:-1;;;;;226:7939:5;;;;;4231:21;226:7939;3916:10;4231:21;;226:7939;;:::i;:::-;3916:10;4231:21;;226:7939;:::i;:::-;;;;;;;;;4317:56;226:7939;;4276:25;226:7939;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3916:10;4231:21;;226:7939;;4276:16;;226:7939;;-1:-1:-1;;226:7939:5;;;;;;;;;;;4276:25;226:7939;;;;;;;;;;;:::i;:::-;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;4317:56;;;;226:7939;;;;;;;;;;;3916:10;4231:21;;226:7939;;;;;;;;-1:-1:-1;;226:7939:5;;;;;;;;4276:25;226:7939;;;;4317:56;226:7939;;;;;;;;;;;;;;;;;;;3916:10;4231:21;;226:7939;;;;;;;-1:-1:-1;;4050:17:5;226:7939;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4200:14;226:7939;4200:14;;226:7939;;;;;;;;-1:-1:-1;;226:7939:5;;;;;;;;;;;;;;;;;;;;;;;;4200:14;;226:7939;;;;;;;-1:-1:-1;;4050:17:5;226:7939;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;226:7939:5;;;;;;;;;;;;;;;;;-1:-1:-1;;;226:7939:5;;;;;;;4050:74;-1:-1:-1;4071:10:5;226:7939;;;;;;;;;;;;;4050:74;;226:7939;;;-1:-1:-1;;;226:7939:5;;;;;;;;;;;;;;;;;-1:-1:-1;;;226:7939:5;;;;;;;;;;;;;-1:-1:-1;;226:7939:5;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;226:7939:5;;;;;;;;;;;;;;;;;-1:-1:-1;;226:7939:5;;;;;;;322:30;226:7939;;;;;;;;;-1:-1:-1;;226:7939:5;;;;7449:16;226:7939;-1:-1:-1;;226:7939:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;226:7939:5;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;226:7939:5;;;;;;:::i;:::-;-1:-1:-1;;;;;226:7939:5;;;;;834:54;226:7939;;;;;;;;;;;834:54;;;;;226:7939;834:54;;;;:::i;:::-;226:7939;;;834:54;226:7939;;;;;;;;;;;;;;-1:-1:-1;;226:7939:5;;;;;;:::i;:::-;;;;;;;;;;;;1292:65;226:7939;;;;;;;;;;;;;;;;1292:65;;;;;226:7939;1292:65;;;;:::i;:::-;226:7939;;;;;;;;;;;;;;;;;-1:-1:-1;;226:7939:5;;;;;;:::i;:::-;735:10:2;-1:-1:-1;;;;;226:7939:5;;5421:34:0;5417:102;;5529:37;226:7939:5;;;5529:37:0;:::i;5417:102::-;226:7939:5;;-1:-1:-1;;;5478:30:0;;226:7939:5;;5478:30:0;226:7939:5;;;;;;-1:-1:-1;;226:7939:5;;;;4330:25:0;226:7939:5;;;;:::i;:::-;;;;;;;;2475:4:0;226:7939:5;;;;3901:22:0;226:7939:5;2475:4:0;:::i;:::-;4330:25;:::i;226:7939:5:-;;;;;;-1:-1:-1;;226:7939:5;;;;;;-1:-1:-1;;;;;226:7939:5;;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;;;;226:7939:5;;;;;;;;;;;;;:::i;:::-;2475:4:0;;;;;:::i;:::-;226:7939:5;;;:::i;:::-;;;;;:::i;:::-;3200:18;226:7939;3200:18;;;;:::i;:::-;;226:7939;;;;;;:::i;:::-;;;;;3263:211;;226:7939;;;;;3263:211;;226:7939;;;3263:211;;;3387:10;226:7939;;3263:211;;;3422:15;;226:7939;;3200:18;3263:211;;;226:7939;;;;3237:10;226:7939;;;;;;;;;;;;;;-1:-1:-1;;;;;226:7939:5;;;;;;;3200:18;226:7939;;;;:::i;:::-;3200:18;226:7939;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3200:18;226:7939;;;;;;;3237:10;226:7939;;;-1:-1:-1;;;;;226:7939:5;;;;;;;;;;:::i;:::-;;;:::i;:::-;;;;;;;;;;;;;;;;3263:211;226:7939;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3263:211;226:7939;;;;;;;;;;;;;;;;;;;;;3387:10;226:7939;;;;;;;;;;;;-1:-1:-1;;;226:7939:5;;;;;;;;;;;;3564:60;226:7939;3200:18;226:7939;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;3387:10;226:7939;;:::i;:::-;3564:60;;;226:7939;;;;;;;;;;-1:-1:-1;226:7939:5;;;;;;;;;;;;;;;;;;;;;;;;;;;3263:211;226:7939;;;;;;;3200:18;226:7939;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3200:18;226:7939;;;;;;;;;;;;;;;;;;;-1:-1:-1;226:7939:5;;;;;;;;;;;3200:18;226:7939;;;;;;;;;;;;;;;;;;3200:18;226:7939;;;;;;;;;;;;;;3200:18;226:7939;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3200:18;226:7939;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;226:7939:5;;;;;;;;:::i;:::-;;;;;:::i;:::-;;;;;;;;;;;;;;;-1:-1:-1;;226:7939:5;;;;;;;;;;;;;;;;3901:22:0;226:7939:5;;;;;;;;;;;;;-1:-1:-1;;226:7939:5;;;;-1:-1:-1;;;;;226:7939:5;;:::i;:::-;;;;;8128:8;226:7939;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;226:7939:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;226:7939:5;;;;;;;;7644:10;226:7939;;;;;;;;7644:31;;:65;;;;226:7939;;;;;;;;;;7644:65;226:7939;7679:30;;;;226:7939;;7644:65;;;226:7939;;;;;;;;;;;;;-1:-1:-1;;;;;226:7939:5;;:::i;:::-;;;;7899:17;226:7939;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;226:7939:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;226:7939:5;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;2673:47:0;;;:87;;;;226:7939:5;;;;;;;2673:87:0;-1:-1:-1;;;862:40:3;;-1:-1:-1;2673:87:0;;;226:7939:5;;;;-1:-1:-1;;;;;226:7939:5;;;;;;:::o;:::-;;;;-1:-1:-1;;;;;226:7939:5;;;;;;:::o;:::-;;;;;;;;;;;;;-1:-1:-1;;;;;226:7939:5;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;-1:-1:-1;226:7939:5;;-1:-1:-1;226:7939:5;;;-1:-1:-1;226:7939:5;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;-1:-1:-1;226:7939:5;;;;;-1:-1:-1;226:7939:5;;;-1:-1:-1;226:7939:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;226:7939:5;;;;;-1:-1:-1;;;226:7939:5;;;;;;;;-1:-1:-1;226:7939:5;;;;;;;;;;;;;;-1:-1:-1;;;;;226:7939:5;;;;;;;:::o;:::-;;;;;;;-1:-1:-1;;;;;226:7939:5;;;;;;;:::o;:::-;;;;;;;;;;;;;-1:-1:-1;;;;;226:7939:5;;;;;;;:::o;:::-;;;;;;;;;;;;;;;:::o;5570:482::-;226:7939;;;;;-1:-1:-1;;226:7939:5;;;;5703:8;226:7939;;;5703:8;226:7939;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5755:13;5798:3;226:7939;;5770:26;;;;;5845:18;;;;:::i;:::-;226:7939;;;5838:6;226:7939;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5882:77;;;5798:3;5878:127;;;5798:3;226:7939;;5755:13;;5878:127;5979:11;;;;;;;;;:::o;5882:77::-;5901:20;;;-1:-1:-1;5901:57:5;;;;5882:77;;;;;5901:57;5943:15;;;-1:-1:-1;5901:57:5;;;5770:26;;;;;;;;5570:482;:::o;226:7939::-;;;;;;;;;;;;-1:-1:-1;226:7939:5;;;;;;;;;;-1:-1:-1;;;;;226:7939:5;;;;;;;;;;;-1:-1:-1;;226:7939:5;;;;;:::i;:::-;;;;;;;;;;;;;;;;;-1:-1:-1;226:7939:5;;;;;;:::o;:::-;-1:-1:-1;;226:7939:5;;;;;;;:::o;:::-;-1:-1:-1;;;;;226:7939:5;;;;;;;;;:::o;6125:1179::-;6246:9;;;:25;;;6125:1179;226:7939;;;6254:1;6409;226:7939;6409:1;226:7939;6392:143;6412:20;;;;;;6557:21;;;;;6553:77;;226:7939;;;;;;;;6648:43;;6705:20;6701:71;;6392:143;226:7939;;;;:::i;:::-;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;226:7939:5;;;:::i;:::-;;6254:1;226:7939;;;;;;6857:23;;6254:1;6890:25;;6939:13;6409:1;7006:3;6954:20;;;:50;;;7006:3;6954:50;;;226:7939;6254:1;226:7939;6457:10;226:7939;;;;;;6254:1;226:7939;;6457:20;7029;;226:7939;;7025:231;;;;7006:3;;;;;;;;:::i;:::-;6939:13;;7025:231;7073:23;;;;;;;7069:140;;7025:231;7226:15;;;;;;7006:3;7226:15;;:::i;:::-;7025:231;;;;;;;7069:140;226:7939;;7006:3;226:7939;;;;;7226:15;226:7939;;;7177:13;226:7939;;;;;;:::i;:::-;;;;;;;;;;;;6409:1;226:7939;;;:::i;:::-;;;;:::i;:::-;;;;;;;;;;;;:::i;:::-;;;;;;;;;-1:-1:-1;;;;;226:7939:5;;;;;;;;;;;;;;;;;7120:35;;;;:::i;:::-;;;;;;:::i;:::-;;7177:13;:::i;:::-;7069:140;;;;;;;6954:50;;;;;;;;;6125:1179;:::o;6954:50::-;6978:26;;;;6954:50;;226:7939;;;;;;;;:::i;:::-;6254:1;226:7939;;;6254:1;226:7939;;;;;;;;;;;;;;;6254:1;226:7939;;;;6254:1;226:7939;;;;;;;;;;;;6701:71;6741:20;;6701:71;;;6553:77;226:7939;;;;;;;;;;;;-1:-1:-1;;;;;226:7939:5;;;;;;;6254:1;226:7939;;6594:25;:::o;6434:3::-;226:7939;6254:1;226:7939;6457:10;226:7939;;;6457:20;226:7939;6254:1;226:7939;6457:20;226:7939;;6453:72;;6434:3;6409:1;226:7939;6397:13;;6453:72;6497:13;;6409:1;6497:13;;:::i;:::-;6453:72;;;;;226:7939;;;-1:-1:-1;;;226:7939:5;;;;;;;;;;;;;;;;;;;;6246:25;6259:12;6268:3;6259:12;;;6246:25;;226:7939;;;;;;;;;;:::o;:::-;-1:-1:-1;226:7939:5;-1:-1:-1;226:7939:5;;-1:-1:-1;226:7939:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;-1:-1:-1;226:7939:5;;;;;;;;;;;;;;;;;-1:-1:-1;226:7939:5;;;;;;;;-1:-1:-1;;226:7939:5;;;;:::o;3199:103:0:-;735:10:2;2954:6:0;226:7939:5;;;;;;;;;;322:30;;226:7939;;3519:23:0;3515:108;;3199:103;:::o;3515:108::-;226:7939:5;;;;3565:47:0;;;;;;735:10:2;3565:47:0;;;226:7939:5;;;;;3565:47:0;3199:103;226:7939:5;2954:6:0;226:7939:5;2954:6:0;226:7939:5;;;2954:6:0;226:7939:5;735:10:2;2954:6:0;226:7939:5;;;;;2954:6:0;226:7939:5;;;3519:23:0;3515:108;;3199:103;:::o;6179:316::-;;2954:6;226:7939:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6276:23:0;6272:217;226:7939:5;;;;;;;;;;;;;;;;;;;;6347:4:0;226:7939:5;;;;;;;;6370:40:0;735:10:2;6370:40:0;;;6347:4;6424:11;:::o;6272:217::-;6466:12;;;:::o;6732:317::-;;2954:6;226:7939:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6826:217:0;226:7939:5;;;;;;;;;;;;;;;;;;;;;;;;;;;6924:40:0;735:10:2;6924:40:0;;;226:7939:5;6978:11:0;:::o"}, "methodIdentifiers": {"CLAIM_ISSUER_ROLE()": "a044e70f", "CLAIM_VERIFIER_ROLE()": "e60ecdd2", "DEFAULT_ADMIN_ROLE()": "a217fddf", "claimIds(address,uint256,uint256)": "60cf25e2", "claimTypes(uint256)": "c1edcdc1", "claims(bytes32)": "eff0f592", "createClaimType(string,string)": "2e0d3857", "creatorClaimTypes(address,uint256)": "8ccf00fb", "getActiveClaimTypes(uint256,uint256)": "a335aca8", "getClaimIds(address,uint256)": "23f86802", "getClaimTypesByCreator(address)": "0ec2d8fe", "getRoleAdmin(bytes32)": "248a9ca3", "getTotalClaimTypes()": "948db1b8", "grantRole(bytes32,address)": "2f2ff15d", "hasRole(bytes32,address)": "91d14854", "hasValidClaim(address,uint256)": "2c52e7ab", "isValidClaimType(uint256)": "1d541ea2", "issueClaim(address,uint256,bytes,bytes,string,uint256)": "dd63e196", "renounceRole(bytes32,address)": "36568abe", "revokeRole(bytes32,address)": "d547741f", "supportsInterface(bytes4)": "01ffc9a7", "updateClaimType(uint256,string,string,bool)": "b8233248"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.22+commit.4fc1097e\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"admin\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"AccessControlBadConfirmation\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"neededRole\",\"type\":\"bytes32\"}],\"name\":\"AccessControlUnauthorizedAccount\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"subject\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"claimType\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"claimId\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"issuer\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"uri\",\"type\":\"string\"}],\"name\":\"ClaimIssued\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"subject\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"claimType\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"claimId\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"issuer\",\"type\":\"address\"}],\"name\":\"ClaimRevoked\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"claimTypeId\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"creator\",\"type\":\"address\"}],\"name\":\"ClaimTypeCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"claimTypeId\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"active\",\"type\":\"bool\"}],\"name\":\"ClaimTypeUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"previousAdminRole\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"newAdminRole\",\"type\":\"bytes32\"}],\"name\":\"RoleAdminChanged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleGranted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleRevoked\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"CLAIM_ISSUER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"CLAIM_VERIFIER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DEFAULT_ADMIN_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"claimIds\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"claimTypes\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"},{\"internalType\":\"address\",\"name\":\"creator\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"createdAt\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"active\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"claims\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"claimType\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"issuer\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"subject\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"},{\"internalType\":\"string\",\"name\":\"uri\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"issuedAt\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"expiresAt\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"revoked\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"}],\"name\":\"createClaimType\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"creatorClaimTypes\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"offset\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"limit\",\"type\":\"uint256\"}],\"name\":\"getActiveClaimTypes\",\"outputs\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"},{\"internalType\":\"address\",\"name\":\"creator\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"createdAt\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"active\",\"type\":\"bool\"}],\"internalType\":\"struct SimpleClaimRegistry.ClaimType[]\",\"name\":\"\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"subject\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"claimType\",\"type\":\"uint256\"}],\"name\":\"getClaimIds\",\"outputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"\",\"type\":\"bytes32[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"creator\",\"type\":\"address\"}],\"name\":\"getClaimTypesByCreator\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"getRoleAdmin\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getTotalClaimTypes\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"grantRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"hasRole\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"subject\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"claimType\",\"type\":\"uint256\"}],\"name\":\"hasValidClaim\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"claimTypeId\",\"type\":\"uint256\"}],\"name\":\"isValidClaimType\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"subject\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"claimType\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"},{\"internalType\":\"string\",\"name\":\"uri\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"expiresAt\",\"type\":\"uint256\"}],\"name\":\"issueClaim\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"callerConfirmation\",\"type\":\"address\"}],\"name\":\"renounceRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"revokeRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"claimTypeId\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"},{\"internalType\":\"bool\",\"name\":\"active\",\"type\":\"bool\"}],\"name\":\"updateClaimType\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"A simplified claim registry with custom claim type management\",\"errors\":{\"AccessControlBadConfirmation()\":[{\"details\":\"The caller of a function is not the expected one. NOTE: Don't confuse with {AccessControlUnauthorizedAccount}.\"}],\"AccessControlUnauthorizedAccount(address,bytes32)\":[{\"details\":\"The `account` is missing a role.\"}]},\"events\":{\"RoleAdminChanged(bytes32,bytes32,bytes32)\":{\"details\":\"Emitted when `newAdminRole` is set as ``role``'s admin role, replacing `previousAdminRole` `DEFAULT_ADMIN_ROLE` is the starting admin for all roles, despite {RoleAdminChanged} not being emitted to signal this.\"},\"RoleGranted(bytes32,address,address)\":{\"details\":\"Emitted when `account` is granted `role`. `sender` is the account that originated the contract call. This account bears the admin role (for the granted role). Expected in cases where the role was granted using the internal {AccessControl-_grantRole}.\"},\"RoleRevoked(bytes32,address,address)\":{\"details\":\"Emitted when `account` is revoked `role`. `sender` is the account that originated the contract call:   - if using `revokeRole`, it is the admin role bearer   - if using `renounceRole`, it is the role bearer (i.e. `account`)\"}},\"kind\":\"dev\",\"methods\":{\"createClaimType(string,string)\":{\"details\":\"Create a new claim type\"},\"getActiveClaimTypes(uint256,uint256)\":{\"details\":\"Get all active claim types (paginated)\"},\"getClaimIds(address,uint256)\":{\"details\":\"Get all claim IDs for a subject and claim type\"},\"getClaimTypesByCreator(address)\":{\"details\":\"Get all claim types created by an address\"},\"getRoleAdmin(bytes32)\":{\"details\":\"Returns the admin role that controls `role`. See {grantRole} and {revokeRole}. To change a role's admin, use {_setRoleAdmin}.\"},\"getTotalClaimTypes()\":{\"details\":\"Get total number of claim types\"},\"grantRole(bytes32,address)\":{\"details\":\"Grants `role` to `account`. If `account` had not been already granted `role`, emits a {RoleGranted} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleGranted} event.\"},\"hasRole(bytes32,address)\":{\"details\":\"Returns `true` if `account` has been granted `role`.\"},\"hasValidClaim(address,uint256)\":{\"details\":\"Check if a subject has a valid claim of a specific type\"},\"isValidClaimType(uint256)\":{\"details\":\"Check if claim type exists and is active\"},\"issueClaim(address,uint256,bytes,bytes,string,uint256)\":{\"details\":\"Issue a claim for a subject\"},\"renounceRole(bytes32,address)\":{\"details\":\"Revokes `role` from the calling account. Roles are often managed via {grantRole} and {revokeRole}: this function's purpose is to provide a mechanism for accounts to lose their privileges if they are compromised (such as when a trusted device is misplaced). If the calling account had been revoked `role`, emits a {RoleRevoked} event. Requirements: - the caller must be `callerConfirmation`. May emit a {RoleRevoked} event.\"},\"revokeRole(bytes32,address)\":{\"details\":\"Revokes `role` from `account`. If `account` had been granted `role`, emits a {RoleRevoked} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleRevoked} event.\"},\"supportsInterface(bytes4)\":{\"details\":\"See {IERC165-supportsInterface}.\"},\"updateClaimType(uint256,string,string,bool)\":{\"details\":\"Update claim type details\"}},\"title\":\"SimpleClaimRegistry\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/SimpleClaimRegistry.sol\":\"SimpleClaimRegistry\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/access/AccessControl.sol\":{\"keccak256\":\"0xc1bebdee8943bd5e9ef1e0f2e63296aa1dd4171a66b9e74d0286220e891e1458\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://928cf2f0042c606f3dcb21bd8a272573f462a215cd65285d2d6b407f31e9bd67\",\"dweb:/ipfs/QmWGxjckno6sfjHPX5naPnsfsyisgy4PJDf46eLw9umfpx\"]},\"@openzeppelin/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0\",\"dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf\"]},\"@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"@openzeppelin/contracts/utils/introspection/ERC165.sol\":{\"keccak256\":\"0xddce8e17e3d3f9ed818b4f4c4478a8262aab8b11ed322f1bf5ed705bb4bd97fa\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8084aa71a4cc7d2980972412a88fe4f114869faea3fefa5436431644eb5c0287\",\"dweb:/ipfs/Qmbqfs5dRdPvHVKY8kTaeyc65NdqXRQwRK7h9s5UJEhD1p\"]},\"@openzeppelin/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621\",\"dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL\"]},\"contracts/SimpleClaimRegistry.sol\":{\"keccak256\":\"0xb14e9ae5541a31cd0d4b4e8bd7bce5bdb13a8b9dcb6e14fea49d5d451e950dae\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7f8765019a0dad7a500c0aa068fa971cde411101f59f631230829c62f128c545\",\"dweb:/ipfs/QmcxASGWAQWu4DcALvCCjVzujtswJwnSGS7NsdutSXEupz\"]}},\"version\":1}", "storageLayout": {"storage": [{"astId": 26, "contract": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "label": "_roles", "offset": 0, "slot": "0", "type": "t_mapping(t_bytes32,t_struct(RoleData)21_storage)"}, {"astId": 462, "contract": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "label": "_nextClaimTypeId", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 480, "contract": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "label": "claimTypes", "offset": 0, "slot": "2", "type": "t_mapping(t_uint256,t_struct(ClaimType)475_storage)"}, {"astId": 485, "contract": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "label": "creatorClaimTypes", "offset": 0, "slot": "3", "type": "t_mapping(t_address,t_array(t_uint256)dyn_storage)"}, {"astId": 509, "contract": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "label": "claims", "offset": 0, "slot": "4", "type": "t_mapping(t_bytes32,t_struct(Claim)504_storage)"}, {"astId": 516, "contract": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "label": "claimIds", "offset": 0, "slot": "5", "type": "t_mapping(t_address,t_mapping(t_uint256,t_array(t_bytes32)dyn_storage))"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_bytes32)dyn_storage": {"base": "t_bytes32", "encoding": "dynamic_array", "label": "bytes32[]", "numberOfBytes": "32"}, "t_array(t_uint256)dyn_storage": {"base": "t_uint256", "encoding": "dynamic_array", "label": "uint256[]", "numberOfBytes": "32"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"encoding": "inplace", "label": "bytes32", "numberOfBytes": "32"}, "t_bytes_storage": {"encoding": "bytes", "label": "bytes", "numberOfBytes": "32"}, "t_mapping(t_address,t_array(t_uint256)dyn_storage)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => uint256[])", "numberOfBytes": "32", "value": "t_array(t_uint256)dyn_storage"}, "t_mapping(t_address,t_bool)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => bool)", "numberOfBytes": "32", "value": "t_bool"}, "t_mapping(t_address,t_mapping(t_uint256,t_array(t_bytes32)dyn_storage))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(uint256 => bytes32[]))", "numberOfBytes": "32", "value": "t_mapping(t_uint256,t_array(t_bytes32)dyn_storage)"}, "t_mapping(t_bytes32,t_struct(Claim)504_storage)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => struct SimpleClaimRegistry.Claim)", "numberOfBytes": "32", "value": "t_struct(Claim)504_storage"}, "t_mapping(t_bytes32,t_struct(RoleData)21_storage)": {"encoding": "mapping", "key": "t_bytes32", "label": "mapping(bytes32 => struct AccessControl.RoleData)", "numberOfBytes": "32", "value": "t_struct(RoleData)21_storage"}, "t_mapping(t_uint256,t_array(t_bytes32)dyn_storage)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => bytes32[])", "numberOfBytes": "32", "value": "t_array(t_bytes32)dyn_storage"}, "t_mapping(t_uint256,t_struct(ClaimType)475_storage)": {"encoding": "mapping", "key": "t_uint256", "label": "mapping(uint256 => struct SimpleClaimRegistry.ClaimType)", "numberOfBytes": "32", "value": "t_struct(ClaimType)475_storage"}, "t_string_storage": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_struct(Claim)504_storage": {"encoding": "inplace", "label": "struct SimpleClaimRegistry.Claim", "members": [{"astId": 487, "contract": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "label": "claimType", "offset": 0, "slot": "0", "type": "t_uint256"}, {"astId": 489, "contract": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "label": "issuer", "offset": 0, "slot": "1", "type": "t_address"}, {"astId": 491, "contract": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "label": "subject", "offset": 0, "slot": "2", "type": "t_address"}, {"astId": 493, "contract": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "label": "signature", "offset": 0, "slot": "3", "type": "t_bytes_storage"}, {"astId": 495, "contract": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "label": "data", "offset": 0, "slot": "4", "type": "t_bytes_storage"}, {"astId": 497, "contract": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "label": "uri", "offset": 0, "slot": "5", "type": "t_string_storage"}, {"astId": 499, "contract": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "label": "issuedAt", "offset": 0, "slot": "6", "type": "t_uint256"}, {"astId": 501, "contract": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "label": "expiresAt", "offset": 0, "slot": "7", "type": "t_uint256"}, {"astId": 503, "contract": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "label": "revoked", "offset": 0, "slot": "8", "type": "t_bool"}], "numberOfBytes": "288"}, "t_struct(ClaimType)475_storage": {"encoding": "inplace", "label": "struct SimpleClaimRegistry.ClaimType", "members": [{"astId": 464, "contract": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "label": "id", "offset": 0, "slot": "0", "type": "t_uint256"}, {"astId": 466, "contract": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "label": "name", "offset": 0, "slot": "1", "type": "t_string_storage"}, {"astId": 468, "contract": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "label": "description", "offset": 0, "slot": "2", "type": "t_string_storage"}, {"astId": 470, "contract": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "label": "creator", "offset": 0, "slot": "3", "type": "t_address"}, {"astId": 472, "contract": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "label": "createdAt", "offset": 0, "slot": "4", "type": "t_uint256"}, {"astId": 474, "contract": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "label": "active", "offset": 0, "slot": "5", "type": "t_bool"}], "numberOfBytes": "192"}, "t_struct(RoleData)21_storage": {"encoding": "inplace", "label": "struct AccessControl.RoleData", "members": [{"astId": 18, "contract": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "label": "hasRole", "offset": 0, "slot": "0", "type": "t_mapping(t_address,t_bool)"}, {"astId": 20, "contract": "contracts/SimpleClaimRegistry.sol:SimpleClaimRegistry", "label": "adminRole", "offset": 0, "slot": "1", "type": "t_bytes32"}], "numberOfBytes": "64"}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}}}}}}}}