const { ethers } = require("hardhat");

async function main() {
    console.log("🧪 Testing ERC-3643 System Deployment...");
    console.log("========================================");

    const [deployer] = await ethers.getSigners();
    console.log("Testing with account:", deployer.address);

    // Step 1: Deploy ClaimRegistry (if not exists)
    console.log("\n1️⃣ Deploying ClaimRegistry...");
    let claimRegistryAddress = process.env.CLAIM_REGISTRY_ADDRESS;
    
    if (!claimRegistryAddress) {
        const deployClaimsScript = require('./03-deploy-claims.js');
        const claimsResult = await deployClaimsScript();
        claimRegistryAddress = claimsResult.claimRegistry;
        console.log("✅ ClaimRegistry deployed:", claimRegistryAddress);
    } else {
        console.log("✅ Using existing ClaimRegistry:", claimRegistryAddress);
    }

    // Step 2: Deploy IdentityRegistry and Compliance
    console.log("\n2️⃣ Deploying IdentityRegistry and Compliance...");
    const deployIdentityScript = require('./04-deploy-identity-compliance.js');
    
    // Temporarily set claim registry address
    const originalClaimRegistry = process.env.CLAIM_REGISTRY_ADDRESS;
    process.env.CLAIM_REGISTRY_ADDRESS = claimRegistryAddress;
    
    const identityResult = await deployIdentityScript();
    
    // Restore original env
    if (originalClaimRegistry) {
        process.env.CLAIM_REGISTRY_ADDRESS = originalClaimRegistry;
    }

    console.log("✅ IdentityRegistry deployed:", identityResult.identityRegistry);
    console.log("✅ Compliance deployed:", identityResult.compliance);

    // Step 3: Deploy Test Token
    console.log("\n3️⃣ Deploying Test SecurityToken...");
    
    // Set environment variables for token deployment
    const originalIdentityRegistry = process.env.IDENTITY_REGISTRY_ADDRESS;
    const originalCompliance = process.env.COMPLIANCE_ADDRESS;
    
    process.env.IDENTITY_REGISTRY_ADDRESS = identityResult.identityRegistry;
    process.env.COMPLIANCE_ADDRESS = identityResult.compliance;
    process.env.TOKEN_NAME = "Test ERC-3643 Token";
    process.env.TOKEN_SYMBOL = "TEST3643";
    process.env.TOKEN_DECIMALS = "0";
    process.env.TOKEN_MAX_SUPPLY = "1000000";
    process.env.AUTO_REGISTER_DEPLOYER = "true";

    const deployTokenScript = require('./06-deploy-token-erc3643.js');
    const tokenResult = await deployTokenScript();

    // Restore original env
    if (originalIdentityRegistry) process.env.IDENTITY_REGISTRY_ADDRESS = originalIdentityRegistry;
    if (originalCompliance) process.env.COMPLIANCE_ADDRESS = originalCompliance;

    console.log("✅ SecurityToken deployed:", tokenResult.token);

    // Step 4: Test Basic Functionality
    console.log("\n4️⃣ Testing Basic Functionality...");
    
    const SecurityToken = await ethers.getContractFactory("SecurityToken");
    const token = SecurityToken.attach(tokenResult.token);
    
    const IdentityRegistry = await ethers.getContractFactory("IdentityRegistry");
    const identityRegistry = IdentityRegistry.attach(identityResult.identityRegistry);
    
    const ClaimRegistry = await ethers.getContractFactory("ClaimRegistry");
    const claimRegistry = ClaimRegistry.attach(claimRegistryAddress);

    try {
        // Test token info
        const name = await token.name();
        const symbol = await token.symbol();
        const version = await token.version();
        console.log(`✅ Token: ${name} (${symbol}) v${version}`);

        // Test deployer status
        const isVerified = await token.isVerified(deployer.address);
        const isWhitelisted = await token.isWhitelisted(deployer.address);
        console.log(`✅ Deployer status: Verified=${isVerified}, Whitelisted=${isWhitelisted}`);

        // Test minting
        if (isVerified && isWhitelisted) {
            const mintAmount = ethers.parseUnits("1000", 0);
            await token.mint(deployer.address, mintAmount);
            const balance = await token.balanceOf(deployer.address);
            console.log(`✅ Minted ${ethers.formatUnits(balance, 0)} tokens`);
        }

    } catch (error) {
        console.log("❌ Basic functionality test failed:", error.message);
    }

    // Step 5: Test Advanced Features
    console.log("\n5️⃣ Testing Advanced ERC-3643 Features...");
    
    try {
        // Test country tracking
        if (await identityRegistry.isVerified(deployer.address)) {
            const country = await identityRegistry.investorCountry(deployer.address);
            console.log(`✅ Deployer country: ${country} (USA=840)`);
        }

        // Test claims
        const totalClaims = await claimRegistry.getTotalClaims();
        console.log(`✅ Total claims in system: ${totalClaims}`);

        // Test compliance
        const Compliance = await ethers.getContractFactory("Compliance");
        const compliance = Compliance.attach(identityResult.compliance);
        
        const totalHolders = await compliance.getTotalHolders();
        console.log(`✅ Total token holders: ${totalHolders}`);

        // Test transfer compliance
        const canTransfer = await token.canTransfer(deployer.address, deployer.address, 100);
        console.log(`✅ Can transfer to self: ${canTransfer}`);

    } catch (error) {
        console.log("❌ Advanced features test failed:", error.message);
    }

    // Summary
    console.log("\n🎉 Test Deployment Summary");
    console.log("==========================");
    console.log("ClaimRegistry:", claimRegistryAddress);
    console.log("IdentityRegistry:", identityResult.identityRegistry);
    console.log("Compliance:", identityResult.compliance);
    console.log("SecurityToken:", tokenResult.token);

    console.log("\n📝 Test Environment Variables:");
    console.log("==============================");
    console.log(`export TEST_CLAIM_REGISTRY_ADDRESS=${claimRegistryAddress}`);
    console.log(`export TEST_IDENTITY_REGISTRY_ADDRESS=${identityResult.identityRegistry}`);
    console.log(`export TEST_COMPLIANCE_ADDRESS=${identityResult.compliance}`);
    console.log(`export TEST_TOKEN_ADDRESS=${tokenResult.token}`);

    return {
        claimRegistry: claimRegistryAddress,
        identityRegistry: identityResult.identityRegistry,
        compliance: identityResult.compliance,
        token: tokenResult.token
    };
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("❌ Test deployment failed:", error);
            process.exit(1);
        });
}

module.exports = main;
