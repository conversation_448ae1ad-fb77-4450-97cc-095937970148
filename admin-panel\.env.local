# Database Configuration
DATABASE_URL="postgresql://postgres:l1nruleZ@localhost:5432/tokendev_clients?schema=public"

# NextAuth Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# Admin wallet private key for signing transactions (required for API routes)

CONTRACT_ADMIN_PRIVATE_KEY=94692a030d151928530ddf7ae9f1ff07ad43d187b96135c8953fac16183619c1
PRIVATE_KEY=94692a030d151928530ddf7ae9f1ff07ad43d187b96135c8953fac16183619c1

# RPC URLs for different networks (customize as needed)
POLYGON_RPC_URL=https://polygon-rpc.com

# RPC URLs for different networks (customize as needed)
POLYGON_AMOY_RPC_URL=https://rpc-amoy.polygon.technology/
AMOY_RPC_URL=https://rpc-amoy.polygon.technology/
POLYGON_RPC_URL=https://polygon-rpc.com
NEXT_PUBLIC_AMOY_RPC_URL=https://rpc-amoy.polygon.technology/

# Factory Contract Address
FACTORY_ADDRESS=******************************************

# ERC-3643 Contract Addresses (Fixed Tokeny-Style System)
CLAIM_REGISTRY_ADDRESS=******************************************
IDENTITY_REGISTRY_ADDRESS=******************************************
COMPLIANCE_ADDRESS=******************************************
CLAIM_REGISTRY_ADMIN=0x56f3726C92B8B92a6ab71983886F91718540d888

# Public environment variables for client-side access
NEXT_PUBLIC_CLAIM_REGISTRY_ADDRESS=******************************************
NEXT_PUBLIC_IDENTITY_REGISTRY_ADDRESS=******************************************
NEXT_PUBLIC_COMPLIANCE_ADDRESS=******************************************

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB in bytes
UPLOAD_DIR=./uploads

# JWT Configuration
JWT_SECRET="your-jwt-secret-here"

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Application Configuration
NODE_ENV=development

