// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "./base/BaseIdentityRegistry.sol";
import "./base/BaseKYCRegistry.sol";
import "./interfaces/ICompleteWhitelist.sol";
import "./ClaimRegistry.sol";

/**
 * @title WhitelistWithClaims
 * @dev Enhanced whitelist that supports both traditional database-driven approval
 * and blockchain claim-based verification
 */
contract WhitelistWithClaims is 
    BaseIdentityRegistry,
    BaseKYCRegistry,
    ICompleteWhitelist
{
    ClaimRegistry public claimRegistry;
    
    // Flag to enable/disable claim-based verification
    bool public claimBasedVerificationEnabled;
    
    event ClaimRegistryUpdated(address indexed oldRegistry, address indexed newRegistry);
    event ClaimBasedVerificationToggled(bool enabled);

    /**
     * @dev Initialize the contract
     * @param admin The address to be granted DEFAULT_ADMIN_ROLE
     */
    function initialize(address admin) public initializer {
        __BaseIdentityRegistry_init(admin);
        __BaseKYCRegistry_init(admin);
        claimBasedVerificationEnabled = false; // Start with traditional mode
    }
    
    /**
     * @dev Special initialization function for factory deployment
     */
    function initializeWithAgent(address admin) external initializer {
        __BaseIdentityRegistry_init(admin);
        __BaseKYCRegistry_init(admin);
        _grantRole(AGENT_ROLE, admin);
        claimBasedVerificationEnabled = false;
    }

    /**
     * @dev Set the claim registry address
     */
    function setClaimRegistry(address _claimRegistry) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(_claimRegistry != address(0), "WhitelistWithClaims: invalid claim registry");
        address oldRegistry = address(claimRegistry);
        claimRegistry = ClaimRegistry(_claimRegistry);
        emit ClaimRegistryUpdated(oldRegistry, _claimRegistry);
    }

    /**
     * @dev Toggle claim-based verification
     */
    function toggleClaimBasedVerification(bool enabled) external onlyRole(DEFAULT_ADMIN_ROLE) {
        claimBasedVerificationEnabled = enabled;
        emit ClaimBasedVerificationToggled(enabled);
    }

    /**
     * @dev Enhanced whitelist check - supports both traditional and claim-based verification
     */
    function isWhitelisted(address account) public view override returns (bool) {
        // Traditional whitelist check (always available)
        bool traditionalWhitelisted = super.isWhitelisted(account);
        
        // If claim-based verification is disabled, use traditional only
        if (!claimBasedVerificationEnabled || address(claimRegistry) == address(0)) {
            return traditionalWhitelisted;
        }
        
        // Hybrid mode: whitelisted if EITHER traditional OR claim-based
        bool claimWhitelisted = claimRegistry.hasValidClaim(account, ClaimRegistry(address(0)).QUALIFICATION_CLAIM());
        
        return traditionalWhitelisted || claimWhitelisted;
    }

    /**
     * @dev Enhanced KYC check - supports both traditional and claim-based verification
     */
    function isKycApproved(address account) public view override returns (bool) {
        // Traditional KYC check
        bool traditionalKyc = super.isKycApproved(account);
        
        // If claim-based verification is disabled, use traditional only
        if (!claimBasedVerificationEnabled || address(claimRegistry) == address(0)) {
            return traditionalKyc;
        }
        
        // Hybrid mode: KYC approved if EITHER traditional OR claim-based
        bool claimKyc = claimRegistry.hasValidClaim(account, ClaimRegistry(address(0)).KYC_CLAIM());
        
        return traditionalKyc || claimKyc;
    }

    /**
     * @dev Issue a qualification claim when traditional approval happens
     */
    function approveKyc(address account) public override(BaseKYCRegistry, IKYCRegistry) {
        super.approveKyc(account);
        
        // Also whitelist the address if not already whitelisted
        if (!super.isWhitelisted(account)) {
            addToWhitelist(account);
        }
        
        // Issue blockchain claim if claim registry is available
        if (address(claimRegistry) != address(0) && hasRole(AGENT_ROLE, msg.sender)) {
            _issueKycClaim(account);
        }
    }

    /**
     * @dev Issue qualification claim when traditional whitelist approval happens
     */
    function addToWhitelist(address account) public override {
        super.addToWhitelist(account);
        
        // Issue blockchain claim if claim registry is available
        if (address(claimRegistry) != address(0) && hasRole(AGENT_ROLE, msg.sender)) {
            _issueQualificationClaim(account);
        }
    }

    /**
     * @dev Internal function to issue KYC claim
     */
    function _issueKycClaim(address account) internal {
        try claimRegistry.issueClaim(
            account,
            ClaimRegistry(address(0)).KYC_CLAIM(),
            "", // signature (can be empty for admin-issued claims)
            abi.encode("KYC_APPROVED", block.timestamp), // data
            "", // uri (optional)
            0 // expiresAt (0 = never expires)
        ) {
            // Claim issued successfully
        } catch {
            // Silently fail - traditional system still works
        }
    }

    /**
     * @dev Internal function to issue qualification claim
     */
    function _issueQualificationClaim(address account) internal {
        try claimRegistry.issueClaim(
            account,
            ClaimRegistry(address(0)).QUALIFICATION_CLAIM(),
            "", // signature
            abi.encode("QUALIFIED", block.timestamp), // data
            "", // uri
            0 // expiresAt
        ) {
            // Claim issued successfully
        } catch {
            // Silently fail - traditional system still works
        }
    }

    /**
     * @dev Check verification method for an address
     */
    function getVerificationMethod(address account) external view returns (string memory) {
        bool traditionalWhitelisted = super.isWhitelisted(account);
        bool traditionalKyc = super.isKycApproved(account);
        
        if (!claimBasedVerificationEnabled || address(claimRegistry) == address(0)) {
            if (traditionalWhitelisted && traditionalKyc) return "TRADITIONAL";
            return "NONE";
        }
        
        bool claimWhitelisted = claimRegistry.hasValidClaim(account, ClaimRegistry(address(0)).QUALIFICATION_CLAIM());
        bool claimKyc = claimRegistry.hasValidClaim(account, ClaimRegistry(address(0)).KYC_CLAIM());
        
        if ((traditionalWhitelisted || claimWhitelisted) && (traditionalKyc || claimKyc)) {
            if (traditionalWhitelisted && claimWhitelisted) return "HYBRID";
            if (traditionalWhitelisted) return "TRADITIONAL";
            if (claimWhitelisted) return "CLAIM_BASED";
        }
        
        return "NONE";
    }
}
