// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";

/**
 * @title ClaimRegistry
 * @dev Registry for storing and managing identity claims for ERC-3643 tokens
 * This works alongside existing whitelist contracts to provide claim-based verification
 */
contract ClaimRegistry is Initializable, AccessControlUpgradeable, UUPSUpgradeable {
    bytes32 public constant CLAIM_ISSUER_ROLE = keccak256("CLAIM_ISSUER_ROLE");
    bytes32 public constant CLAIM_VERIFIER_ROLE = keccak256("CLAIM_VERIFIER_ROLE");

    // Claim types
    uint256 public constant KYC_CLAIM = 1;
    uint256 public constant ACCREDITED_INVESTOR_CLAIM = 2;
    uint256 public constant JURISDICTION_CLAIM = 3;
    uint256 public constant QUALIFICATION_CLAIM = 4;

    struct Claim {
        uint256 claimType;
        address issuer;
        bytes signature;
        bytes data;
        string uri;
        uint256 issuedAt;
        uint256 expiresAt;
        bool revoked;
    }

    // Mapping: subject => claimType => claimId => Claim
    mapping(address => mapping(uint256 => mapping(bytes32 => Claim))) private claims;
    
    // Mapping: subject => claimType => claimIds[]
    mapping(address => mapping(uint256 => bytes32[])) private claimIds;
    
    // Mapping: subject => claimType => bool (for quick lookup)
    mapping(address => mapping(uint256 => bool)) private hasClaim;

    event ClaimAdded(
        address indexed subject,
        uint256 indexed claimType,
        bytes32 indexed claimId,
        address issuer
    );

    event ClaimRevoked(
        address indexed subject,
        uint256 indexed claimType,
        bytes32 indexed claimId,
        address issuer
    );

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    function initialize(address admin) public initializer {
        __AccessControl_init();
        __UUPSUpgradeable_init();
        
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(CLAIM_ISSUER_ROLE, admin);
        _grantRole(CLAIM_VERIFIER_ROLE, admin);
    }

    /**
     * @dev Issue a claim for a subject
     */
    function issueClaim(
        address subject,
        uint256 claimType,
        bytes calldata signature,
        bytes calldata data,
        string calldata uri,
        uint256 expiresAt
    ) external onlyRole(CLAIM_ISSUER_ROLE) returns (bytes32) {
        bytes32 claimId = keccak256(abi.encodePacked(subject, claimType, block.timestamp, msg.sender));
        
        claims[subject][claimType][claimId] = Claim({
            claimType: claimType,
            issuer: msg.sender,
            signature: signature,
            data: data,
            uri: uri,
            issuedAt: block.timestamp,
            expiresAt: expiresAt,
            revoked: false
        });

        claimIds[subject][claimType].push(claimId);
        hasClaim[subject][claimType] = true;

        emit ClaimAdded(subject, claimType, claimId, msg.sender);
        return claimId;
    }

    /**
     * @dev Revoke a claim
     */
    function revokeClaim(
        address subject,
        uint256 claimType,
        bytes32 claimId
    ) external {
        Claim storage claim = claims[subject][claimType][claimId];
        require(claim.issuer != address(0), "ClaimRegistry: claim does not exist");
        require(
            claim.issuer == msg.sender || hasRole(DEFAULT_ADMIN_ROLE, msg.sender),
            "ClaimRegistry: not authorized to revoke"
        );

        claim.revoked = true;
        emit ClaimRevoked(subject, claimType, claimId, claim.issuer);
    }

    /**
     * @dev Check if subject has a valid claim of specific type
     */
    function hasValidClaim(address subject, uint256 claimType) external view returns (bool) {
        if (!hasClaim[subject][claimType]) return false;

        bytes32[] memory ids = claimIds[subject][claimType];
        for (uint256 i = 0; i < ids.length; i++) {
            Claim memory claim = claims[subject][claimType][ids[i]];
            if (!claim.revoked && (claim.expiresAt == 0 || claim.expiresAt > block.timestamp)) {
                return true;
            }
        }
        return false;
    }

    /**
     * @dev Get claim details
     */
    function getClaim(
        address subject,
        uint256 claimType,
        bytes32 claimId
    ) external view returns (Claim memory) {
        return claims[subject][claimType][claimId];
    }

    /**
     * @dev Get all claim IDs for a subject and claim type
     */
    function getClaimIds(address subject, uint256 claimType) external view returns (bytes32[] memory) {
        return claimIds[subject][claimType];
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyRole(DEFAULT_ADMIN_ROLE) {}
}
