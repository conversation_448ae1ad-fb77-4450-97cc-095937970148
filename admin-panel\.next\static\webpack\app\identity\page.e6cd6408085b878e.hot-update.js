"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/identity/page",{

/***/ "(app-pages-browser)/./src/app/identity/page.tsx":
/*!***********************************!*\
  !*** ./src/app/identity/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IdentityPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_IdentityManagement__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/IdentityManagement */ \"(app-pages-browser)/./src/components/IdentityManagement.tsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction IdentityPage() {\n    _s();\n    const [contracts, setContracts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [batchAddresses, setBatchAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [batchLoading, setBatchLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('individual');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IdentityPage.useEffect\": ()=>{\n            fetchContractInfo();\n            fetchStats();\n        }\n    }[\"IdentityPage.useEffect\"], []);\n    const fetchContractInfo = async ()=>{\n        const contractAddresses = [\n            {\n                address: process.env.NEXT_PUBLIC_CLAIM_REGISTRY_ADDRESS || process.env.CLAIM_REGISTRY_ADDRESS || '',\n                name: 'ClaimRegistry'\n            },\n            {\n                address: process.env.NEXT_PUBLIC_IDENTITY_REGISTRY_ADDRESS || process.env.IDENTITY_REGISTRY_ADDRESS || '',\n                name: 'IdentityRegistry'\n            },\n            {\n                address: process.env.NEXT_PUBLIC_COMPLIANCE_ADDRESS || process.env.COMPLIANCE_ADDRESS || '',\n                name: 'Compliance'\n            }\n        ];\n        const contractInfo = contractAddresses.map((contract)=>({\n                ...contract,\n                status: contract.address ? 'connected' : 'error'\n            }));\n        setContracts(contractInfo);\n    };\n    const fetchStats = async ()=>{\n        try {\n            // This would typically fetch from your API\n            // For now, we'll use placeholder data\n            setStats({\n                totalVerified: 0,\n                totalWhitelisted: 0,\n                totalKycApproved: 0,\n                totalClaims: 0\n            });\n        } catch (error) {\n            console.error('Error fetching stats:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const performBatchAction = async (action)=>{\n        const addresses = batchAddresses.split('\\n').map((addr)=>addr.trim()).filter((addr)=>addr.length > 0);\n        if (addresses.length === 0) {\n            setMessage({\n                type: 'error',\n                text: 'Please enter at least one address'\n            });\n            return;\n        }\n        if (addresses.length > 50) {\n            setMessage({\n                type: 'error',\n                text: 'Maximum 50 addresses allowed per batch'\n            });\n            return;\n        }\n        setBatchLoading(action);\n        setMessage(null);\n        try {\n            const response = await fetch('/api/identity/batch', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action,\n                    addresses\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                setMessage({\n                    type: 'success',\n                    text: \"Batch \".concat(action, \" completed: \").concat(data.summary.successful, \"/\").concat(data.summary.total, \" successful\")\n                });\n                setBatchAddresses('');\n                fetchStats(); // Refresh stats\n            } else {\n                throw new Error(data.error || 'Batch action failed');\n            }\n        } catch (error) {\n            console.error(\"Error performing batch \".concat(action, \":\"), error);\n            setMessage({\n                type: 'error',\n                text: \"Failed to perform batch \".concat(action, \": \").concat(error.message)\n            });\n        } finally{\n            setBatchLoading(null);\n        }\n    };\n    const getStatusBadge = (status)=>{\n        switch(status){\n            case 'connected':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CheckCircle, {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 63\n                        }, this),\n                        \"Connected\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                    variant: \"destructive\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(XCircle, {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 45\n                        }, this),\n                        \"Not Configured\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                    variant: \"secondary\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertCircle, {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 43\n                        }, this),\n                        \"Unknown\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"ERC-3643 Identity Management\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                        variant: \"outline\",\n                        className: \"text-sm\",\n                        children: \"Compliance System v3.0\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                variant: message.type === 'error' ? 'destructive' : 'default',\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertCircle, {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertDescription, {\n                        children: message.text\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tabs, {\n                defaultValue: \"individual\",\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsList, {\n                        className: \"grid w-full grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsTrigger, {\n                                value: \"individual\",\n                                children: \"Individual Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsTrigger, {\n                                value: \"batch\",\n                                children: \"Batch Operations\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsTrigger, {\n                                value: \"stats\",\n                                children: \"Statistics\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsTrigger, {\n                                value: \"system\",\n                                children: \"System Status\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsContent, {\n                        value: \"individual\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_IdentityManagement__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            onStatusUpdate: fetchStats\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsContent, {\n                        value: \"batch\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Users, {\n                                                className: \"w-5 h-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Batch Operations\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"Wallet Addresses (one per line, max 50)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Textarea, {\n                                                    placeholder: \"0x1234... 0x5678... 0x9abc...\",\n                                                    value: batchAddresses,\n                                                    onChange: (e)=>setBatchAddresses(e.target.value),\n                                                    rows: 8,\n                                                    className: \"font-mono text-sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 mt-1\",\n                                                    children: [\n                                                        batchAddresses.split('\\n').filter((addr)=>addr.trim().length > 0).length,\n                                                        \" addresses\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-4 gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                                    onClick: ()=>performBatchAction('batch_whitelist'),\n                                                    disabled: !!batchLoading,\n                                                    variant: \"outline\",\n                                                    children: batchLoading === 'batch_whitelist' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Loader2, {\n                                                        className: \"w-4 h-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 57\n                                                    }, this) : 'Batch Whitelist'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                                    onClick: ()=>performBatchAction('batch_approve_kyc'),\n                                                    disabled: !!batchLoading,\n                                                    variant: \"outline\",\n                                                    children: batchLoading === 'batch_approve_kyc' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Loader2, {\n                                                        className: \"w-4 h-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 59\n                                                    }, this) : 'Batch Approve KYC'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                                    onClick: ()=>performBatchAction('batch_unwhitelist'),\n                                                    disabled: !!batchLoading,\n                                                    variant: \"outline\",\n                                                    children: batchLoading === 'batch_unwhitelist' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Loader2, {\n                                                        className: \"w-4 h-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 59\n                                                    }, this) : 'Batch Remove Whitelist'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                                    onClick: ()=>performBatchAction('batch_issue_qualification_claims'),\n                                                    disabled: !!batchLoading,\n                                                    variant: \"outline\",\n                                                    children: batchLoading === 'batch_issue_qualification_claims' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Loader2, {\n                                                        className: \"w-4 h-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 74\n                                                    }, this) : 'Issue Qualification Claims'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsContent, {\n                        value: \"stats\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                            className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Total Verified\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Shield, {\n                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: loading ? '...' : (stats === null || stats === void 0 ? void 0 : stats.totalVerified) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Registered identities\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                            className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Total Whitelisted\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Users, {\n                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: loading ? '...' : (stats === null || stats === void 0 ? void 0 : stats.totalWhitelisted) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Approved for trading\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                            className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"KYC Approved\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileCheck, {\n                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: loading ? '...' : (stats === null || stats === void 0 ? void 0 : stats.totalKycApproved) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Passed verification\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                            className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Total Claims\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Activity, {\n                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: loading ? '...' : (stats === null || stats === void 0 ? void 0 : stats.totalClaims) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Issued credentials\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsContent, {\n                        value: \"system\",\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Settings, {\n                                                    className: \"w-5 h-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Contract Status\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                        className: \"space-y-4\",\n                                        children: contracts.map((contract, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium\",\n                                                                children: contract.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500 font-mono\",\n                                                                children: contract.address || 'Not configured'\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    getStatusBadge(contract.status)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Database, {\n                                                    className: \"w-5 h-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Environment Configuration\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                        className: \"space-y-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Network:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" Polygon Amoy Testnet\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"RPC URL:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        process.env.NEXT_PUBLIC_AMOY_RPC_URL || 'Not configured'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Admin Address:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        process.env.NEXT_PUBLIC_CLAIM_REGISTRY_ADMIN || 'Not configured'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"System Version:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" ERC-3643 v3.0.0\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n        lineNumber: 139,\n        columnNumber: 5\n    }, this);\n}\n_s(IdentityPage, \"+XbPkwYwe0WZpIf1g4WW5sMnEK8=\");\n_c = IdentityPage;\nvar _c;\n$RefreshReg$(_c, \"IdentityPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/identity/page.tsx\n"));

/***/ })

});