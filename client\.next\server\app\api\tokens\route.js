/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/tokens/route";
exports.ids = ["app/api/tokens/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftokens%2Froute&page=%2Fapi%2Ftokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftokens%2Froute&page=%2Fapi%2Ftokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_client_src_app_api_tokens_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/tokens/route.ts */ \"(rsc)/./src/app/api/tokens/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/tokens/route\",\n        pathname: \"/api/tokens\",\n        filename: \"route\",\n        bundlePath: \"app/api/tokens/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\api\\\\tokens\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_client_src_app_api_tokens_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftokens%2Froute&page=%2Fapi%2Ftokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/tokens/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/tokens/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth0/nextjs-auth0 */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/index.js\");\n/* harmony import */ var _auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n\n\n\n// Helper function to check if user has required claims for a token\nasync function checkUserClaims(walletAddress, requiredClaims) {\n    const claimResults = {};\n    if (!walletAddress || !requiredClaims.length) {\n        return claimResults;\n    }\n    try {\n        const claimRegistryAddress = \"******************************************\";\n        if (!claimRegistryAddress) {\n            console.warn('Claim registry not configured');\n            return claimResults;\n        }\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.JsonRpcProvider(\"https://rpc-amoy.polygon.technology\");\n        const claimRegistryABI = [\n            \"function hasValidClaim(address subject, uint256 claimType) external view returns (bool)\"\n        ];\n        const claimRegistry = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(claimRegistryAddress, claimRegistryABI, provider);\n        // Check each required claim (now using custom claim type IDs)\n        for (const claimType of requiredClaims){\n            const claimTypeId = parseInt(claimType.trim());\n            if (!isNaN(claimTypeId) && claimTypeId > 0) {\n                try {\n                    const hasValidClaim = await claimRegistry.hasValidClaim(walletAddress, claimTypeId);\n                    claimResults[claimType] = hasValidClaim;\n                    console.log(`🔍 Claim check for ${walletAddress}: Claim Type ${claimTypeId} = ${hasValidClaim ? '✅ VALID' : '❌ INVALID'}`);\n                } catch (error) {\n                    console.warn(`Could not check claim type ${claimTypeId}:`, error);\n                    claimResults[claimType] = false;\n                }\n            } else {\n                console.warn(`Invalid claim type ID: ${claimType}`);\n                claimResults[claimType] = false;\n            }\n        }\n        return claimResults;\n    } catch (error) {\n        console.error('Error checking user claims:', error);\n        return claimResults;\n    }\n}\nasync function GET(request) {\n    try {\n        // Check for test wallet address in query params (for testing purposes)\n        const { searchParams } = new URL(request.url);\n        const testWalletAddress = searchParams.get('testWallet');\n        // Get user session to check wallet address\n        const session = await (0,_auth0_nextjs_auth0__WEBPACK_IMPORTED_MODULE_1__.getSession)(request, next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next());\n        let userWalletAddress = testWalletAddress; // Use test wallet if provided\n        // If no test wallet and user is logged in, try to get their wallet address\n        if (!userWalletAddress && session?.user?.email) {\n            try {\n                const adminPanelUrl = process.env.ADMIN_PANEL_URL;\n                const clientResponse = await fetch(`${adminPanelUrl}/api/clients?search=${encodeURIComponent(session.user.email)}&limit=1`);\n                if (clientResponse.ok) {\n                    const clientData = await clientResponse.json();\n                    const client = clientData.clients?.[0];\n                    userWalletAddress = client?.walletAddress || null;\n                }\n            } catch (error) {\n                console.warn('Could not fetch user wallet address:', error);\n            }\n        }\n        // Fetch tokens from the admin panel API\n        const adminPanelUrl = process.env.ADMIN_PANEL_URL;\n        const response = await fetch(`${adminPanelUrl}/api/tokens?source=database&t=${Date.now()}`, {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json',\n                'Cache-Control': 'no-cache, no-store, must-revalidate',\n                'Pragma': 'no-cache',\n                'Expires': '0'\n            },\n            // Add cache control to ensure fresh data\n            cache: 'no-store'\n        });\n        if (!response.ok) {\n            throw new Error(`Failed to fetch tokens from admin panel: ${response.status}`);\n        }\n        const tokens = await response.json();\n        // Get whitelist status for all tokens if user has a wallet\n        let whitelistStatuses = {};\n        if (userWalletAddress && tokens.length > 0) {\n            try {\n                const tokenAddresses = tokens.map((token)=>token.address);\n                const whitelistResponse = await fetch(`${adminPanelUrl}/api/whitelist/check?t=${Date.now()}`, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Cache-Control': 'no-cache, no-store, must-revalidate'\n                    },\n                    body: JSON.stringify({\n                        walletAddress: userWalletAddress,\n                        tokenAddresses: tokenAddresses\n                    })\n                });\n                if (whitelistResponse.ok) {\n                    const whitelistData = await whitelistResponse.json();\n                    whitelistData.tokens?.forEach((tokenStatus)=>{\n                        whitelistStatuses[tokenStatus.tokenAddress.toLowerCase()] = tokenStatus.isWhitelisted;\n                    });\n                }\n            } catch (error) {\n                console.warn('Could not fetch whitelist statuses:', error);\n            }\n        }\n        // Check user claims for all tokens if user has a wallet\n        let userClaimsMap = {};\n        let qualifiedTokens = [];\n        if (userWalletAddress && tokens.length > 0) {\n            console.log(`🔍 Checking qualification for wallet: ${userWalletAddress}`);\n            for (const token of tokens){\n                if (token.selectedClaims) {\n                    const requiredClaims = typeof token.selectedClaims === 'string' ? token.selectedClaims.split(',').map((c)=>c.trim()) : token.selectedClaims;\n                    const userClaims = await checkUserClaims(userWalletAddress, requiredClaims);\n                    userClaimsMap[token.address.toLowerCase()] = userClaims;\n                    // Check if user has ALL required claims\n                    const hasAllRequiredClaims = requiredClaims.every((claim)=>userClaims[claim] === true);\n                    if (hasAllRequiredClaims) {\n                        qualifiedTokens.push(token.address.toLowerCase());\n                        console.log(`✅ User qualified for ${token.symbol}`);\n                    } else {\n                        console.log(`❌ User NOT qualified for ${token.symbol}`);\n                    }\n                } else {\n                    // No claims required, user is qualified\n                    qualifiedTokens.push(token.address.toLowerCase());\n                    console.log(`✅ User qualified for ${token.symbol} - no requirements`);\n                }\n            }\n            console.log(`🎯 Showing ${qualifiedTokens.length} out of ${tokens.length} tokens to user`);\n        }\n        // Transform and filter tokens based on claims qualification\n        const transformedTokens = tokens.filter((token)=>{\n            // If no wallet address, show all tokens but mark as not qualified\n            if (!userWalletAddress) return true;\n            // If user has wallet, only show tokens they're qualified for\n            return qualifiedTokens.includes(token.address?.toLowerCase());\n        }).map((token)=>{\n            // Extract numeric price and currency from tokenPrice field (e.g., \"1.5 ETH\" -> price: \"1.5\", currency: \"ETH\")\n            let numericPrice = '0';\n            let extractedCurrency = token.currency || 'USD';\n            if (token.tokenPrice) {\n                // Try to extract price and currency from tokenPrice field\n                const priceWithCurrencyMatch = token.tokenPrice.match(/([\\d.]+)\\s*([A-Z]{3,4})/i);\n                if (priceWithCurrencyMatch) {\n                    numericPrice = priceWithCurrencyMatch[1];\n                    extractedCurrency = priceWithCurrencyMatch[2].toUpperCase();\n                } else {\n                    // Fallback to just extracting the number\n                    const priceMatch = token.tokenPrice.match(/[\\d.]+/);\n                    numericPrice = priceMatch ? priceMatch[0] : '0';\n                }\n            }\n            const tokenAddress = token.address?.toLowerCase();\n            return {\n                id: token.id,\n                name: token.name,\n                symbol: token.symbol,\n                address: token.address,\n                totalSupply: token.totalSupply || '0',\n                maxSupply: token.maxSupply || '0',\n                price: numericPrice,\n                currency: extractedCurrency,\n                category: token.tokenType || 'Unknown',\n                description: token.deploymentNotes || '',\n                imageUrl: token.tokenImageUrl || null,\n                network: token.network || 'amoy',\n                decimals: token.decimals || 0,\n                version: '1.0.0',\n                bonusTiers: token.bonusTiers || '',\n                whitelistAddress: token.whitelistAddress || '',\n                createdAt: token.createdAt || new Date().toISOString(),\n                isWhitelisted: whitelistStatuses[tokenAddress] || false\n            };\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(transformedTokens);\n    } catch (error) {\n        console.error('Error fetching tokens:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch tokens'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/tokens/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth0","vendor-chunks/debug","vendor-chunks/tslib","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-flag","vendor-chunks/joi","vendor-chunks/openid-client","vendor-chunks/@sideway","vendor-chunks/@hapi","vendor-chunks/lru-cache","vendor-chunks/object-hash","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/url-join","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftokens%2Froute&page=%2Fapi%2Ftokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();