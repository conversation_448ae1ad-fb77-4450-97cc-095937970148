import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../../lib/prisma';

// GET /api/tokens/[address]/clients/[clientId] - Get specific client approval for token
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ address: string; clientId: string }> }
) {
  try {
    const { address, clientId } = await params;
    
    // Find the token
    const token = await prisma.token.findUnique({
      where: { address },
      select: { id: true, name: true, symbol: true }
    });

    if (!token) {
      return NextResponse.json(
        { error: 'Token not found' },
        { status: 404 }
      );
    }

    // Get client with approval status for this token
    const client = await prisma.client.findUnique({
      where: { id: clientId },
      include: {
        tokenApprovals: {
          where: { tokenId: token.id },
          select: {
            id: true,
            approvalStatus: true,
            kycApproved: true,
            whitelistApproved: true,
            approvedBy: true,
            approvedAt: true,
            rejectedReason: true,
            notes: true,
            createdAt: true,
            updatedAt: true
          }
        }
      }
    });

    if (!client) {
      return NextResponse.json(
        { error: 'Client not found' },
        { status: 404 }
      );
    }

    const approval = client.tokenApprovals[0]; // Should be only one per token

    return NextResponse.json({
      client: {
        id: client.id,
        firstName: client.firstName,
        lastName: client.lastName,
        email: client.email,
        phoneNumber: client.phoneNumber,
        nationality: client.nationality,
        kycStatus: client.kycStatus,
        isWhitelisted: client.isWhitelisted,
        walletAddress: client.walletAddress,
        agreementAccepted: client.agreementAccepted,
        createdAt: client.createdAt,
        updatedAt: client.updatedAt
      },
      tokenApproval: approval || null,
      token: {
        id: token.id,
        name: token.name,
        symbol: token.symbol,
        address: address
      }
    });

  } catch (error) {
    console.error('Error fetching client approval:', error);
    return NextResponse.json(
      { error: 'Failed to fetch client approval' },
      { status: 500 }
    );
  }
}

// PUT /api/tokens/[address]/clients/[clientId] - Update client approval for token
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ address: string; clientId: string }> }
) {
  try {
    const { address, clientId } = await params;
    const body = await request.json();
    
    const { 
      approvalStatus, 
      kycApproved, 
      whitelistApproved, 
      notes, 
      approvedBy,
      rejectedReason 
    } = body;

    // Find the token
    const token = await prisma.token.findUnique({
      where: { address },
      select: { id: true }
    });

    if (!token) {
      return NextResponse.json(
        { error: 'Token not found' },
        { status: 404 }
      );
    }

    // Check if client exists
    const client = await prisma.client.findUnique({
      where: { id: clientId },
      select: { id: true }
    });

    if (!client) {
      return NextResponse.json(
        { error: 'Client not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {};
    
    if (approvalStatus !== undefined) updateData.approvalStatus = approvalStatus;
    if (kycApproved !== undefined) updateData.kycApproved = kycApproved;
    if (whitelistApproved !== undefined) updateData.whitelistApproved = whitelistApproved;
    if (notes !== undefined) updateData.notes = notes;
    if (approvedBy !== undefined) updateData.approvedBy = approvedBy;
    if (rejectedReason !== undefined) updateData.rejectedReason = rejectedReason;

    // Set approval timestamp
    if (approvalStatus === 'APPROVED') {
      updateData.approvedAt = new Date();
    } else if (approvalStatus === 'REJECTED') {
      updateData.approvedAt = null;
    }

    // Update or create the approval record
    const approval = await prisma.tokenClientApproval.upsert({
      where: {
        tokenId_clientId: {
          tokenId: token.id,
          clientId: clientId
        }
      },
      update: updateData,
      create: {
        tokenId: token.id,
        clientId: clientId,
        approvalStatus: approvalStatus || 'PENDING',
        kycApproved: kycApproved || false,
        whitelistApproved: whitelistApproved || false,
        notes: notes || null,
        approvedBy: approvedBy || null,
        rejectedReason: rejectedReason || null,
        approvedAt: (approvalStatus === 'APPROVED') ? new Date() : null
      },
      include: {
        client: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            walletAddress: true
          }
        },
        token: {
          select: {
            id: true,
            name: true,
            symbol: true,
            address: true
          }
        }
      }
    });

    return NextResponse.json(approval);

  } catch (error) {
    console.error('Error updating client approval:', error);
    return NextResponse.json(
      { error: 'Failed to update client approval' },
      { status: 500 }
    );
  }
}

// DELETE /api/tokens/[address]/clients/[clientId] - Remove client approval for token
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ address: string; clientId: string }> }
) {
  try {
    const { address, clientId } = await params;
    
    // Find the token
    const token = await prisma.token.findUnique({
      where: { address },
      select: { id: true }
    });

    if (!token) {
      return NextResponse.json(
        { error: 'Token not found' },
        { status: 404 }
      );
    }

    // Delete the approval record
    await prisma.tokenClientApproval.delete({
      where: {
        tokenId_clientId: {
          tokenId: token.id,
          clientId: clientId
        }
      }
    });

    return NextResponse.json({ message: 'Client approval removed successfully' });

  } catch (error) {
    console.error('Error removing client approval:', error);
    
    if (error.code === 'P2025') {
      return NextResponse.json(
        { error: 'Client approval not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to remove client approval' },
      { status: 500 }
    );
  }
}
