import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../../lib/prisma';
import { ethers } from 'ethers';

// Helper function to issue blockchain claims
async function issueBlockchainClaim(
  walletAddress: string,
  claimType: 'KYC_CLAIM' | 'QUALIFICATION_CLAIM',
  data: string = 'APPROVED'
): Promise<string | null> {
  try {
    const claimRegistryAddress = process.env.CLAIM_REGISTRY_ADDRESS;
    if (!claimRegistryAddress) {
      console.warn('Claim registry not configured, skipping claim issuance');
      return null;
    }

    const provider = new ethers.JsonRpcProvider(process.env.POLYGON_AMOY_RPC_URL);
    const adminWallet = new ethers.Wallet(process.env.CONTRACT_ADMIN_PRIVATE_KEY!, provider);

    const claimRegistryABI = [
      "function issueClaim(address subject, uint256 claimType, bytes calldata signature, bytes calldata data, string calldata uri, uint256 expiresAt) external returns (bytes32)"
    ];

    const claimRegistry = new ethers.Contract(claimRegistryAddress, claimRegistryABI, adminWallet);

    // Claim type mapping
    const claimTypeId = claimType === 'KYC_CLAIM' ? 1 : 4; // KYC_CLAIM = 1, QUALIFICATION_CLAIM = 4

    // Encode claim data
    const encodedData = ethers.AbiCoder.defaultAbiCoder().encode(
      ['string', 'uint256'],
      [data, Math.floor(Date.now() / 1000)]
    );

    console.log(`Issuing ${claimType} claim for ${walletAddress}...`);

    const tx = await claimRegistry.issueClaim(
      walletAddress,
      claimTypeId,
      '0x', // empty signature for admin-issued claims
      encodedData,
      '', // empty URI
      0 // never expires
    );

    const receipt = await tx.wait();
    console.log(`✅ ${claimType} claim issued successfully: ${receipt.transactionHash}`);

    return receipt.transactionHash;
  } catch (error) {
    console.error(`❌ Failed to issue ${claimType} claim:`, error);
    return null;
  }
}

// GET /api/tokens/[address]/clients/[clientId] - Get specific client approval for token
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ address: string; clientId: string }> }
) {
  try {
    const { address, clientId } = await params;
    
    // Find the token
    const token = await prisma.token.findUnique({
      where: { address },
      select: { id: true, name: true, symbol: true }
    });

    if (!token) {
      return NextResponse.json(
        { error: 'Token not found' },
        { status: 404 }
      );
    }

    // Get client with approval status for this token
    const client = await prisma.client.findUnique({
      where: { id: clientId },
      include: {
        tokenApprovals: {
          where: { tokenId: token.id },
          select: {
            id: true,
            approvalStatus: true,
            kycApproved: true,
            whitelistApproved: true,
            approvedBy: true,
            approvedAt: true,
            rejectedReason: true,
            notes: true,
            createdAt: true,
            updatedAt: true
          }
        }
      }
    });

    if (!client) {
      return NextResponse.json(
        { error: 'Client not found' },
        { status: 404 }
      );
    }

    const approval = client.tokenApprovals[0]; // Should be only one per token

    return NextResponse.json({
      client: {
        id: client.id,
        firstName: client.firstName,
        lastName: client.lastName,
        email: client.email,
        phoneNumber: client.phoneNumber,
        nationality: client.nationality,
        kycStatus: client.kycStatus,
        isWhitelisted: client.isWhitelisted,
        walletAddress: client.walletAddress,
        agreementAccepted: client.agreementAccepted,
        createdAt: client.createdAt,
        updatedAt: client.updatedAt
      },
      tokenApproval: approval || null,
      token: {
        id: token.id,
        name: token.name,
        symbol: token.symbol,
        address: address
      }
    });

  } catch (error) {
    console.error('Error fetching client approval:', error);
    return NextResponse.json(
      { error: 'Failed to fetch client approval' },
      { status: 500 }
    );
  }
}

// PUT /api/tokens/[address]/clients/[clientId] - Update client approval for token
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ address: string; clientId: string }> }
) {
  try {
    const { address, clientId } = await params;
    const body = await request.json();
    
    const { 
      approvalStatus, 
      kycApproved, 
      whitelistApproved, 
      notes, 
      approvedBy,
      rejectedReason 
    } = body;

    // Find the token
    const token = await prisma.token.findUnique({
      where: { address },
      select: { id: true }
    });

    if (!token) {
      return NextResponse.json(
        { error: 'Token not found' },
        { status: 404 }
      );
    }

    // Check if client exists
    const client = await prisma.client.findUnique({
      where: { id: clientId },
      select: { id: true }
    });

    if (!client) {
      return NextResponse.json(
        { error: 'Client not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {};
    
    if (approvalStatus !== undefined) updateData.approvalStatus = approvalStatus;
    if (kycApproved !== undefined) updateData.kycApproved = kycApproved;
    if (whitelistApproved !== undefined) updateData.whitelistApproved = whitelistApproved;
    if (notes !== undefined) updateData.notes = notes;
    if (approvedBy !== undefined) updateData.approvedBy = approvedBy;
    if (rejectedReason !== undefined) updateData.rejectedReason = rejectedReason;

    // Set approval timestamp
    if (approvalStatus === 'APPROVED') {
      updateData.approvedAt = new Date();
    } else if (approvalStatus === 'REJECTED') {
      updateData.approvedAt = null;
    }

    // Update or create the approval record
    const approval = await prisma.tokenClientApproval.upsert({
      where: {
        tokenId_clientId: {
          tokenId: token.id,
          clientId: clientId
        }
      },
      update: updateData,
      create: {
        tokenId: token.id,
        clientId: clientId,
        approvalStatus: approvalStatus || 'PENDING',
        kycApproved: kycApproved || false,
        whitelistApproved: whitelistApproved || false,
        notes: notes || null,
        approvedBy: approvedBy || null,
        rejectedReason: rejectedReason || null,
        approvedAt: (approvalStatus === 'APPROVED') ? new Date() : null
      },
      include: {
        client: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            walletAddress: true
          }
        },
        token: {
          select: {
            id: true,
            name: true,
            symbol: true,
            address: true
          }
        }
      }
    });

    // Issue blockchain claims when approvals are granted
    const claimTransactions: string[] = [];

    if (approval.client.walletAddress) {
      // Issue KYC claim if KYC was approved
      if (kycApproved === true) {
        console.log(`🔗 Issuing KYC claim for ${approval.client.walletAddress}...`);
        const kycTxHash = await issueBlockchainClaim(
          approval.client.walletAddress,
          'KYC_CLAIM',
          'KYC_APPROVED_VIA_ADMIN'
        );
        if (kycTxHash) {
          claimTransactions.push(kycTxHash);
        }
      }

      // Issue qualification claim if whitelist was approved
      if (whitelistApproved === true) {
        console.log(`🔗 Issuing qualification claim for ${approval.client.walletAddress}...`);
        const qualificationTxHash = await issueBlockchainClaim(
          approval.client.walletAddress,
          'QUALIFICATION_CLAIM',
          `QUALIFIED_FOR_${approval.token.symbol}`
        );
        if (qualificationTxHash) {
          claimTransactions.push(qualificationTxHash);
        }
      }
    } else {
      console.warn(`⚠️ Client ${approval.client.email} has no wallet address, skipping claim issuance`);
    }

    // Return approval with claim transaction information
    return NextResponse.json({
      ...approval,
      claimTransactions: claimTransactions.length > 0 ? claimTransactions : undefined,
      claimsIssued: claimTransactions.length
    });

  } catch (error) {
    console.error('Error updating client approval:', error);
    return NextResponse.json(
      { error: 'Failed to update client approval' },
      { status: 500 }
    );
  }
}

// DELETE /api/tokens/[address]/clients/[clientId] - Remove client approval for token
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ address: string; clientId: string }> }
) {
  try {
    const { address, clientId } = await params;
    
    // Find the token
    const token = await prisma.token.findUnique({
      where: { address },
      select: { id: true }
    });

    if (!token) {
      return NextResponse.json(
        { error: 'Token not found' },
        { status: 404 }
      );
    }

    // Delete the approval record
    await prisma.tokenClientApproval.delete({
      where: {
        tokenId_clientId: {
          tokenId: token.id,
          clientId: clientId
        }
      }
    });

    return NextResponse.json({ message: 'Client approval removed successfully' });

  } catch (error) {
    console.error('Error removing client approval:', error);
    
    if (error.code === 'P2025') {
      return NextResponse.json(
        { error: 'Client approval not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to remove client approval' },
      { status: 500 }
    );
  }
}
