"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-token/page",{

/***/ "(app-pages-browser)/./src/app/create-token/components/TokenForm.tsx":
/*!*******************************************************!*\
  !*** ./src/app/create-token/components/TokenForm.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../config */ \"(app-pages-browser)/./src/config.ts\");\n\n\n\n/**\r\n * TokenForm Component\r\n *\r\n * Form for creating a new security token with various configuration options\r\n */ const TokenForm = (param)=>{\n    let { formData, handleInputChange, handleNetworkChange, handleSubmit, isSubmitting, network, getNetworkLabel, deploymentStep, kycSupported } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white shadow-md rounded-lg p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-gray-700 text-sm font-bold mb-2\",\n                            htmlFor: \"network\",\n                            children: \"Network\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            id: \"network\",\n                            name: \"network\",\n                            className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                            value: network,\n                            onChange: handleNetworkChange,\n                            disabled: isSubmitting,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"amoy\",\n                                    children: \"Amoy Testnet\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"polygon\",\n                                    children: \"Polygon Mainnet\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, undefined),\n                formData.enableKYC && !kycSupported && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6 rounded-md\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-5 w-5 text-yellow-400\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    viewBox: \"0 0 20 20\",\n                                    fill: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-yellow-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"KYC Support Not Available:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \" The deployed factory contract doesn't support KYC functionality. Your token will be deployed without KYC support. To enable KYC, the contract factory needs to be updated.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"name\",\n                                    children: \"Token Name*\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"name\",\n                                    name: \"name\",\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    placeholder: \"e.g., Example Security Token\",\n                                    value: formData.name,\n                                    onChange: handleInputChange,\n                                    disabled: isSubmitting,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"symbol\",\n                                    children: \"Token Symbol*\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"symbol\",\n                                    name: \"symbol\",\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    placeholder: \"e.g., EXST\",\n                                    value: formData.symbol,\n                                    onChange: handleInputChange,\n                                    disabled: isSubmitting,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"decimals\",\n                                    children: \"Decimals*\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"decimals\",\n                                    name: \"decimals\",\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    value: formData.decimals,\n                                    onChange: handleInputChange,\n                                    disabled: isSubmitting,\n                                    required: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: 0,\n                                            children: \"0 (No decimals)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: 6,\n                                            children: \"6 decimals\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: 8,\n                                            children: \"8 decimals\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: 18,\n                                            children: \"18 decimals (Standard)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-green-600 mt-1\",\n                                    children: \"✅ Choose the number of decimal places for your token. 0 for whole numbers only, 18 for standard ERC-20 tokens.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"maxSupply\",\n                                    children: \"Maximum Supply*\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"maxSupply\",\n                                    name: \"maxSupply\",\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    placeholder: \"e.g., 1000000\",\n                                    value: formData.maxSupply,\n                                    onChange: handleInputChange,\n                                    disabled: isSubmitting,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"ownerAddress\",\n                                    children: \"Owner Address (Whitelisted)*\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"ownerAddress\",\n                                    name: \"ownerAddress\",\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    placeholder: \"e.g., 0x...\",\n                                    value: formData.ownerAddress,\n                                    onChange: handleInputChange,\n                                    disabled: isSubmitting,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"tokenPrice\",\n                                    children: \"Token Price\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"tokenPrice\",\n                                    name: \"tokenPrice\",\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    placeholder: \"e.g., 10\",\n                                    value: formData.tokenPrice,\n                                    onChange: handleInputChange,\n                                    disabled: isSubmitting\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"currency\",\n                                    children: \"Currency\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"currency\",\n                                    name: \"currency\",\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    value: formData.currency,\n                                    onChange: handleInputChange,\n                                    disabled: isSubmitting,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"USD\",\n                                            children: \"USD - US Dollar\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"EUR\",\n                                            children: \"EUR - Euro\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"GBP\",\n                                            children: \"GBP - British Pound\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"JPY\",\n                                            children: \"JPY - Japanese Yen\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"CAD\",\n                                            children: \"CAD - Canadian Dollar\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"AUD\",\n                                            children: \"AUD - Australian Dollar\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"CHF\",\n                                            children: \"CHF - Swiss Franc\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"CNY\",\n                                            children: \"CNY - Chinese Yuan\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"BTC\",\n                                            children: \"BTC - Bitcoin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"ETH\",\n                                            children: \"ETH - Ethereum\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"USDC\",\n                                            children: \"USDC - USD Coin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"USDT\",\n                                            children: \"USDT - Tether\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: \"Select the currency for token pricing and fees\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"tokenImageUrl\",\n                                    children: \"Token Logo/Image URL\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"url\",\n                                    id: \"tokenImageUrl\",\n                                    name: \"tokenImageUrl\",\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    placeholder: \"e.g., https://example.com/logo.png\",\n                                    value: formData.tokenImageUrl,\n                                    onChange: handleInputChange,\n                                    disabled: isSubmitting\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: \"Optional: URL to your token's logo or image (PNG, JPG, SVG recommended)\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"tokenType\",\n                                    children: \"Token Type\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"tokenType\",\n                                    name: \"tokenType\",\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    value: formData.tokenType,\n                                    onChange: handleInputChange,\n                                    disabled: isSubmitting,\n                                    children: _config__WEBPACK_IMPORTED_MODULE_2__.tokenTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: type.id,\n                                            children: type.name\n                                        }, type.id, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center h-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center text-gray-700 font-bold\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"enableKYC\",\n                                                name: \"enableKYC\",\n                                                className: \"mr-2 h-4 w-4 text-blue-600\",\n                                                checked: formData.enableKYC,\n                                                onChange: handleInputChange,\n                                                disabled: isSubmitting || !kycSupported\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Enable KYC\",\n                                            !kycSupported && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-1 text-xs bg-gray-200 text-gray-600 px-1 py-0.5 rounded\",\n                                                children: \"Not Available\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 ml-6\",\n                                        children: [\n                                            \"Requires KYC approval for token transfers\",\n                                            !kycSupported && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-red-500\",\n                                                children: \"KYC support requires an updated contract factory\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 p-4 border border-blue-200 rounded-lg bg-blue-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-bold text-gray-800 mb-3 flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 mr-2 text-blue-600\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"ERC-3643 Claims to Issue\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 mb-4\",\n                            children: [\n                                \"Select which Tokeny-style claims to automatically issue for the token owner during deployment. Claims will be generated in format: \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    className: \"bg-gray-200 px-1 rounded\",\n                                    children: \"YYMMDDHHMMSS + CountryCode + ClaimType\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 49\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"issuerCountry\",\n                                    children: \"Issuer Country (for claims)\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"issuerCountry\",\n                                    name: \"issuerCountry\",\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    value: formData.issuerCountry,\n                                    onChange: handleInputChange,\n                                    disabled: isSubmitting,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"US\",\n                                            children: \"\\uD83C\\uDDFA\\uD83C\\uDDF8 United States (840)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"CA\",\n                                            children: \"\\uD83C\\uDDE8\\uD83C\\uDDE6 Canada (124)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"GB\",\n                                            children: \"\\uD83C\\uDDEC\\uD83C\\uDDE7 United Kingdom (826)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"DE\",\n                                            children: \"\\uD83C\\uDDE9\\uD83C\\uDDEA Germany (276)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"FR\",\n                                            children: \"\\uD83C\\uDDEB\\uD83C\\uDDF7 France (250)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"IT\",\n                                            children: \"\\uD83C\\uDDEE\\uD83C\\uDDF9 Italy (380)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"ES\",\n                                            children: \"\\uD83C\\uDDEA\\uD83C\\uDDF8 Spain (724)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"NL\",\n                                            children: \"\\uD83C\\uDDF3\\uD83C\\uDDF1 Netherlands (528)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"CH\",\n                                            children: \"\\uD83C\\uDDE8\\uD83C\\uDDED Switzerland (756)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"AU\",\n                                            children: \"\\uD83C\\uDDE6\\uD83C\\uDDFA Australia (36)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"JP\",\n                                            children: \"\\uD83C\\uDDEF\\uD83C\\uDDF5 Japan (392)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"SG\",\n                                            children: \"\\uD83C\\uDDF8\\uD83C\\uDDEC Singapore (702)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                            children: [\n                                {\n                                    id: 'KYC',\n                                    name: 'KYC Verification',\n                                    description: 'Know Your Customer compliance claim',\n                                    code: '001'\n                                },\n                                {\n                                    id: 'AML',\n                                    name: 'AML Compliance',\n                                    description: 'Anti-Money Laundering verification',\n                                    code: '002'\n                                },\n                                {\n                                    id: 'IDENTITY',\n                                    name: 'Identity Verification',\n                                    description: 'Identity document verification',\n                                    code: '003'\n                                },\n                                {\n                                    id: 'QUALIFICATION',\n                                    name: 'Qualified Investor',\n                                    description: 'Accredited/qualified investor status',\n                                    code: '004'\n                                },\n                                {\n                                    id: 'ACCREDITATION',\n                                    name: 'Accreditation',\n                                    description: 'Professional accreditation claim',\n                                    code: '005'\n                                },\n                                {\n                                    id: 'RESIDENCE',\n                                    name: 'Residence Proof',\n                                    description: 'Proof of residence verification',\n                                    code: '006'\n                                },\n                                {\n                                    id: 'TOKEN_ISSUER',\n                                    name: 'Token Issuer',\n                                    description: 'Authorized token issuer claim',\n                                    code: '007'\n                                }\n                            ].map((claim)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3 p-3 border border-gray-200 rounded-lg bg-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"claim-\".concat(claim.id),\n                                            className: \"mt-1 h-4 w-4 text-blue-600 rounded\",\n                                            checked: formData.selectedClaims.includes(claim.id),\n                                            onChange: (e)=>{\n                                                const newClaims = e.target.checked ? [\n                                                    ...formData.selectedClaims,\n                                                    claim.id\n                                                ] : formData.selectedClaims.filter((c)=>c !== claim.id);\n                                                handleInputChange({\n                                                    target: {\n                                                        name: 'selectedClaims',\n                                                        value: newClaims\n                                                    }\n                                                });\n                                            },\n                                            disabled: isSubmitting\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"claim-\".concat(claim.id),\n                                                    className: \"text-sm font-medium text-gray-900 cursor-pointer\",\n                                                    children: [\n                                                        claim.name,\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"(Topic \",\n                                                                claim.code,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 34\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: claim.description\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, claim.id, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-yellow-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDCA1 Tip:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \" Claims will be automatically generated with current timestamp and selected country code. Example format: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        children: \"241218143045840001\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 31\n                                    }, undefined),\n                                    \" (Dec 18, 2024 14:30:45, USA, KYC)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-gray-700 text-sm font-bold mb-2\",\n                            htmlFor: \"bonusTiers\",\n                            children: \"Bonus Tiers\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            id: \"bonusTiers\",\n                            name: \"bonusTiers\",\n                            className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                            placeholder: \"e.g., Tier 1: 5%, Tier 2: 10%, Tier 3: 15%\",\n                            value: formData.bonusTiers,\n                            onChange: handleInputChange,\n                            disabled: isSubmitting,\n                            rows: 3\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center\",\n                        disabled: isSubmitting,\n                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 17\n                                }, undefined),\n                                deploymentStep === 'preparing' && 'Preparing...',\n                                deploymentStep === 'connecting' && 'Connecting...',\n                                deploymentStep === 'deploying' && 'Deploying Token...',\n                                deploymentStep === 'confirming' && 'Confirming Transaction...',\n                                deploymentStep === 'fetching' && 'Fetching Token Details...',\n                                deploymentStep === 'setting_up_compliance' && 'Setting up ERC-3643 Compliance...'\n                            ]\n                        }, void 0, true) : \"Create Token on \".concat(getNetworkLabel(network))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                    lineNumber: 398,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\n_c = TokenForm;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TokenForm);\nvar _c;\n$RefreshReg$(_c, \"TokenForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/create-token/components/TokenForm.tsx\n"));

/***/ })

});