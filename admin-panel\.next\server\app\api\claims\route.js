/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/claims/route";
exports.ids = ["app/api/claims/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclaims%2Froute&page=%2Fapi%2Fclaims%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclaims%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclaims%2Froute&page=%2Fapi%2Fclaims%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclaims%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_claims_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/claims/route.ts */ \"(rsc)/./src/app/api/claims/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/claims/route\",\n        pathname: \"/api/claims\",\n        filename: \"route\",\n        bundlePath: \"app/api/claims/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\claims\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_claims_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclaims%2Froute&page=%2Fapi%2Fclaims%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclaims%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/claims/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/claims/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/wallet/wallet.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/abi/abi-coder.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\nconst prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient();\n// GET /api/claims - Get claims for an address\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const walletAddress = searchParams.get('walletAddress');\n        const claimType = searchParams.get('claimType');\n        if (!walletAddress) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Wallet address is required'\n            }, {\n                status: 400\n            });\n        }\n        // Get claim registry address from environment or database\n        const claimRegistryAddress = process.env.CLAIM_REGISTRY_ADDRESS;\n        if (!claimRegistryAddress) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Claim registry not configured'\n            }, {\n                status: 500\n            });\n        }\n        // Connect to blockchain\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.JsonRpcProvider(process.env.POLYGON_AMOY_RPC_URL);\n        const claimRegistryABI = [\n            \"function hasValidClaim(address subject, uint256 claimType) external view returns (bool)\",\n            \"function getClaimIds(address subject, uint256 claimType) external view returns (bytes32[])\",\n            \"function getClaim(address subject, uint256 claimType, bytes32 claimId) external view returns (tuple(uint256 claimType, address issuer, bytes signature, bytes data, string uri, uint256 issuedAt, uint256 expiresAt, bool revoked))\"\n        ];\n        const claimRegistry = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(claimRegistryAddress, claimRegistryABI, provider);\n        // Claim types\n        const CLAIM_TYPES = {\n            KYC_CLAIM: 1,\n            ACCREDITED_INVESTOR_CLAIM: 2,\n            JURISDICTION_CLAIM: 3,\n            QUALIFICATION_CLAIM: 4\n        };\n        const claims = [];\n        // If specific claim type requested\n        if (claimType && CLAIM_TYPES[claimType]) {\n            const typeId = CLAIM_TYPES[claimType];\n            const hasValid = await claimRegistry.hasValidClaim(walletAddress, typeId);\n            const claimIds = await claimRegistry.getClaimIds(walletAddress, typeId);\n            for (const claimId of claimIds){\n                try {\n                    const claim = await claimRegistry.getClaim(walletAddress, typeId, claimId);\n                    claims.push({\n                        claimId,\n                        claimType: claimType,\n                        claimTypeId: typeId,\n                        issuer: claim.issuer,\n                        issuedAt: new Date(Number(claim.issuedAt) * 1000),\n                        expiresAt: claim.expiresAt > 0 ? new Date(Number(claim.expiresAt) * 1000) : null,\n                        revoked: claim.revoked,\n                        valid: !claim.revoked && (claim.expiresAt === 0n || claim.expiresAt > BigInt(Math.floor(Date.now() / 1000))),\n                        data: claim.data,\n                        uri: claim.uri\n                    });\n                } catch (error) {\n                    console.error('Error fetching claim details:', error);\n                }\n            }\n        } else {\n            // Get all claim types\n            for (const [typeName, typeId] of Object.entries(CLAIM_TYPES)){\n                try {\n                    const hasValid = await claimRegistry.hasValidClaim(walletAddress, typeId);\n                    const claimIds = await claimRegistry.getClaimIds(walletAddress, typeId);\n                    for (const claimId of claimIds){\n                        try {\n                            const claim = await claimRegistry.getClaim(walletAddress, typeId, claimId);\n                            claims.push({\n                                claimId,\n                                claimType: typeName,\n                                claimTypeId: typeId,\n                                issuer: claim.issuer,\n                                issuedAt: new Date(Number(claim.issuedAt) * 1000),\n                                expiresAt: claim.expiresAt > 0 ? new Date(Number(claim.expiresAt) * 1000) : null,\n                                revoked: claim.revoked,\n                                valid: !claim.revoked && (claim.expiresAt === 0n || claim.expiresAt > BigInt(Math.floor(Date.now() / 1000))),\n                                data: claim.data,\n                                uri: claim.uri\n                            });\n                        } catch (error) {\n                            console.error('Error fetching claim details:', error);\n                        }\n                    }\n                } catch (error) {\n                    console.error(`Error checking ${typeName} claims:`, error);\n                }\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            walletAddress,\n            claims,\n            totalClaims: claims.length,\n            validClaims: claims.filter((c)=>c.valid).length\n        });\n    } catch (error) {\n        console.error('Error fetching claims:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch claims'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/claims - Issue a new claim\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { walletAddress, claimType, data, uri, expiresAt } = body;\n        if (!walletAddress || !claimType) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Wallet address and claim type are required'\n            }, {\n                status: 400\n            });\n        }\n        // Get claim registry address\n        const claimRegistryAddress = process.env.CLAIM_REGISTRY_ADDRESS;\n        if (!claimRegistryAddress) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Claim registry not configured'\n            }, {\n                status: 500\n            });\n        }\n        // Connect to blockchain with admin wallet\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.JsonRpcProvider(process.env.POLYGON_AMOY_RPC_URL);\n        const adminWallet = new ethers__WEBPACK_IMPORTED_MODULE_4__.Wallet(process.env.CONTRACT_ADMIN_PRIVATE_KEY, provider);\n        const claimRegistryABI = [\n            \"function issueClaim(address subject, uint256 claimType, bytes calldata signature, bytes calldata data, string calldata uri, uint256 expiresAt) external returns (bytes32)\"\n        ];\n        const claimRegistry = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(claimRegistryAddress, claimRegistryABI, adminWallet);\n        // Claim types\n        const CLAIM_TYPES = {\n            KYC_CLAIM: 1,\n            ACCREDITED_INVESTOR_CLAIM: 2,\n            JURISDICTION_CLAIM: 3,\n            QUALIFICATION_CLAIM: 4\n        };\n        const claimTypeId = CLAIM_TYPES[claimType];\n        if (!claimTypeId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid claim type'\n            }, {\n                status: 400\n            });\n        }\n        // Issue the claim\n        const encodedData = ethers__WEBPACK_IMPORTED_MODULE_5__.AbiCoder.defaultAbiCoder().encode([\n            'string',\n            'uint256'\n        ], [\n            data || 'APPROVED',\n            Math.floor(Date.now() / 1000)\n        ]);\n        const expirationTimestamp = expiresAt ? Math.floor(new Date(expiresAt).getTime() / 1000) : 0;\n        const tx = await claimRegistry.issueClaim(walletAddress, claimTypeId, '0x', encodedData, uri || '', expirationTimestamp);\n        const receipt = await tx.wait();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            transactionHash: receipt.transactionHash,\n            walletAddress,\n            claimType,\n            claimTypeId,\n            message: 'Claim issued successfully'\n        });\n    } catch (error) {\n        console.error('Error issuing claim:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to issue claim'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/claims/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy","vendor-chunks/aes-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclaims%2Froute&page=%2Fapi%2Fclaims%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclaims%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();