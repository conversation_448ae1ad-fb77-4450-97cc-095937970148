import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';
import SecurityTokenABI from '../../../../contracts/SecurityToken.json';

// RPC URLs for different networks
const RPC_URLS = {
  amoy: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/',
  polygon: process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com',
  unknown: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/',
};

// GET /api/tokens/[address] - Get token details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ address: string }> }
) {
  try {
    const { address } = await params;
    const network = 'amoy'; // Default to amoy network
    const rpcUrl = RPC_URLS[network];

    // Validate address
    if (!ethers.isAddress(address)) {
      return NextResponse.json(
        { error: 'Invalid token address' },
        { status: 400 }
      );
    }

    // Create provider
    const provider = new ethers.JsonRpcProvider(rpcUrl);

    // Create token contract instance
    const tokenContract = new ethers.Contract(address, SecurityTokenABI.abi, provider);

    console.log(`Fetching details for token: ${address}`);

    // Get basic token info
    const [name, symbol, totalSupply, decimals] = await Promise.all([
      tokenContract.name(),
      tokenContract.symbol(),
      tokenContract.totalSupply(),
      tokenContract.decimals(),
    ]);

    // Try to get owner/admin address
    let owner = '';
    try {
      owner = await tokenContract.owner();
    } catch (error) {
      console.warn(`Could not fetch owner for ${address}:`, error);
      // Try alternative methods if owner() doesn't exist
      try {
        owner = await tokenContract.admin();
      } catch (adminError) {
        console.warn(`Could not fetch admin for ${address}:`, adminError);
      }
    }

    // Get additional token details
    let tokenType = 'EQUITY';
    let securityType = 'REGULATION_D';
    let description = '';

    try {
      const tokenDetails = await tokenContract.tokenDetails();
      if (tokenDetails) {
        try {
          const parsed = JSON.parse(tokenDetails);
          tokenType = parsed.tokenType || 'EQUITY';
          securityType = parsed.securityType || 'REGULATION_D';
          description = parsed.description || '';
        } catch {
          description = tokenDetails;
        }
      }
    } catch (error) {
      console.warn(`Could not fetch token details for ${address}:`, error);
    }

    // Get investor count (approximate by checking Transfer events)
    let investors = 0;
    try {
      const transferFilter = tokenContract.filters.Transfer(null, null);
      const transferEvents = await tokenContract.queryFilter(transferFilter, 0, 'latest');
      const uniqueRecipients = new Set();

      transferEvents.forEach(event => {
        if (event.args && event.args[1] !== ethers.ZeroAddress) {
          uniqueRecipients.add(event.args[1]);
        }
      });

      investors = uniqueRecipients.size;
    } catch (error) {
      console.warn(`Could not fetch investor count for ${address}:`, error);
    }

    // Get recent transactions
    const transactions = [];
    try {
      const transferFilter = tokenContract.filters.Transfer();
      const transferEvents = await tokenContract.queryFilter(transferFilter, -1000, 'latest'); // Last 1000 blocks

      for (const event of transferEvents.slice(-10)) { // Last 10 transactions
        try {
          const block = await provider.getBlock(event.blockNumber);
          const transaction = await provider.getTransaction(event.transactionHash);

          transactions.push({
            hash: event.transactionHash,
            type: event.args[0] === ethers.ZeroAddress ? 'MINT' : 'TRANSFER',
            amount: ethers.formatUnits(event.args[2], decimals),
            from: event.args[0],
            to: event.args[1],
            timestamp: new Date(block.timestamp * 1000).toISOString()
          });
        } catch (txError) {
          console.warn(`Could not fetch transaction details:`, txError);
        }
      }
    } catch (error) {
      console.warn(`Could not fetch transactions for ${address}:`, error);
    }

    // Get creation timestamp
    let createdAt = new Date().toISOString();
    try {
      const deployFilter = tokenContract.filters.Transfer(ethers.ZeroAddress);
      const deployEvents = await tokenContract.queryFilter(deployFilter, 0, 'latest');
      if (deployEvents.length > 0) {
        const block = await provider.getBlock(deployEvents[0].blockNumber);
        if (block) {
          createdAt = new Date(block.timestamp * 1000).toISOString();
        }
      }
    } catch (error) {
      console.warn(`Could not fetch creation time for ${address}:`, error);
    }

    const tokenDetails = {
      address,
      name,
      symbol,
      decimals: Number(decimals),
      totalSupply: totalSupply.toString(),
      owner,
      securityType,
      tokenType,
      description,
      investors,
      transactions,
      createdAt
    };

    console.log(`Successfully loaded details for token: ${address}`);
    return NextResponse.json(tokenDetails);

  } catch (error) {
    console.error('Error fetching token details:', error);
    return NextResponse.json(
      { error: 'Failed to fetch token details' },
      { status: 500 }
    );
  }
}
