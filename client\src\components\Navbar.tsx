'use client';

import { Fragment } from 'react';
import { Menu, Transition } from '@headlessui/react';
import { ChevronDownIcon, UserIcon, CogIcon, ArrowRightOnRectangleIcon, ClipboardDocumentCheckIcon, ShoppingBagIcon } from '@heroicons/react/24/outline';
import type { UserProfile } from '@auth0/nextjs-auth0/client';
import type { ClientProfile } from '@/lib/api-client';
import Link from 'next/link';

interface NavbarProps {
  user: UserProfile;
  clientProfile?: ClientProfile | null;
  onGetQualified: () => void;
  onLogout?: () => void;
  useMockAuth?: boolean;
}

export function Navbar({ user, clientProfile, onGetQualified, onLogout, useMockAuth }: NavbarProps) {
  if (!user) {
    return null
  }

  return (
    <nav className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <h1 className="text-xl font-bold text-gray-900">
                TokenDev
              </h1>
            </div>
          </div>

          {/* Right side */}
          <div className="flex items-center space-x-4">
            {/* Get Qualified Button */}
            <button
              onClick={onGetQualified}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                clientProfile?.kycStatus === 'APPROVED'
                  ? 'bg-green-100 text-green-800 cursor-default'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
              disabled={clientProfile?.kycStatus === 'APPROVED'}
            >
              {clientProfile?.kycStatus === 'APPROVED' ? (
                <>
                  <span className="inline-flex items-center">
                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Qualified
                  </span>
                </>
              ) : (
                'Get Qualified'
              )}
            </button>

            {/* KYC Status Indicator */}
            {clientProfile && (
              <div className="hidden sm:flex items-center">
                <div className={`w-2 h-2 rounded-full mr-2 ${
                  clientProfile.kycStatus === 'APPROVED' ? 'bg-green-500' :
                  clientProfile.kycStatus === 'IN_REVIEW' ? 'bg-yellow-500' :
                  clientProfile.kycStatus === 'REJECTED' ? 'bg-red-500' :
                  'bg-gray-400'
                }`}></div>
                <span className="text-sm text-gray-600">
                  KYC: {clientProfile.kycStatus.replace('_', ' ')}
                </span>
              </div>
            )}

            {/* User Menu */}
            <Menu as="div" className="relative">
              <div>
                <Menu.Button className="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                  <span className="sr-only">Open user menu</span>
                  <div className="flex items-center space-x-3">
                    {user.picture ? (
                      <img
                        className="h-8 w-8 rounded-full"
                        src={user.picture}
                        alt={user.name || 'User avatar'}
                      />
                    ) : (
                      <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                        <UserIcon className="h-5 w-5 text-gray-600" />
                      </div>
                    )}
                    <div className="hidden md:block text-left">
                      <div className="text-sm font-medium text-gray-900">
                        {user.name || 'User'}
                      </div>
                      <div className="text-xs text-gray-500">
                        {user.email}
                      </div>
                    </div>
                    <ChevronDownIcon className="h-4 w-4 text-gray-400" />
                  </div>
                </Menu.Button>
              </div>

              <Transition
                as={Fragment}
                enter="transition ease-out duration-100"
                enterFrom="transform opacity-0 scale-95"
                enterTo="transform opacity-100 scale-100"
                leave="transition ease-in duration-75"
                leaveFrom="transform opacity-100 scale-100"
                leaveTo="transform opacity-0 scale-95"
              >
                <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                  <Menu.Item>
                    {({ active }) => (
                      <Link
                        href="/qualification"
                        className={`${
                          active ? 'bg-gray-100' : ''
                        } flex items-center px-4 py-2 text-sm text-gray-700`}
                      >
                        <ClipboardDocumentCheckIcon className="mr-3 h-4 w-4" />
                        Qualification
                      </Link>
                    )}
                  </Menu.Item>

                  <Menu.Item>
                    {({ active }) => (
                      <Link
                        href="/orders"
                        className={`${
                          active ? 'bg-gray-100' : ''
                        } flex items-center px-4 py-2 text-sm text-gray-700`}
                      >
                        <ShoppingBagIcon className="mr-3 h-4 w-4" />
                        My Orders
                      </Link>
                    )}
                  </Menu.Item>

                  <Menu.Item>
                    {({ active }) => (
                      <button
                        onClick={onGetQualified}
                        className={`${
                          active ? 'bg-gray-100' : ''
                        } flex w-full items-center px-4 py-2 text-sm text-gray-700`}
                      >
                        <UserIcon className="mr-3 h-4 w-4" />
                        {clientProfile ? 'Update Profile' : 'Complete Profile'}
                      </button>
                    )}
                  </Menu.Item>

                  <Menu.Item>
                    {({ active }) => (
                      <a
                        href="#"
                        className={`${
                          active ? 'bg-gray-100' : ''
                        } flex items-center px-4 py-2 text-sm text-gray-700`}
                      >
                        <CogIcon className="mr-3 h-4 w-4" />
                        Settings
                      </a>
                    )}
                  </Menu.Item>

                  <div className="border-t border-gray-100"></div>

                  <Menu.Item>
                    {({ active }) => (
                      useMockAuth ? (
                        <button
                          onClick={onLogout}
                          className={`${
                            active ? 'bg-gray-100' : ''
                          } flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors`}
                        >
                          <ArrowRightOnRectangleIcon className="mr-3 h-4 w-4" />
                          Sign out
                        </button>
                      ) : (
                        <a
                          href={`/api/auth/logout?returnTo=${process.env.NEXT_PUBLIC_AUTH0_RETURN_TO_URL || process.env.AUTH0_BASE_URL}`}
                          className={`${
                            active ? 'bg-gray-100' : ''
                          } flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors`}
                        >
                          <ArrowRightOnRectangleIcon className="mr-3 h-4 w-4" />
                          Sign out
                        </a>
                      )
                    )}
                  </Menu.Item>
                </Menu.Items>
              </Transition>
            </Menu>
          </div>
        </div>
      </div>
    </nav>
  );
}
