/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/clients/[id]/whitelist/route";
exports.ids = ["app/api/clients/[id]/whitelist/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclients%2F%5Bid%5D%2Fwhitelist%2Froute&page=%2Fapi%2Fclients%2F%5Bid%5D%2Fwhitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclients%2F%5Bid%5D%2Fwhitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclients%2F%5Bid%5D%2Fwhitelist%2Froute&page=%2Fapi%2Fclients%2F%5Bid%5D%2Fwhitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclients%2F%5Bid%5D%2Fwhitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_clients_id_whitelist_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/clients/[id]/whitelist/route.ts */ \"(rsc)/./src/app/api/clients/[id]/whitelist/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/clients/[id]/whitelist/route\",\n        pathname: \"/api/clients/[id]/whitelist\",\n        filename: \"route\",\n        bundlePath: \"app/api/clients/[id]/whitelist/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\clients\\\\[id]\\\\whitelist\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_clients_id_whitelist_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclients%2F%5Bid%5D%2Fwhitelist%2Froute&page=%2Fapi%2Fclients%2F%5Bid%5D%2Fwhitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclients%2F%5Bid%5D%2Fwhitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/clients/[id]/whitelist/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/clients/[id]/whitelist/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _lib_validations_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/validations/client */ \"(rsc)/./src/lib/validations/client.ts\");\n\n\n\n// PUT /api/clients/[id]/whitelist - Update whitelist status\nasync function PUT(request, { params }) {\n    try {\n        const { id } = await params;\n        const body = await request.json();\n        const validatedData = _lib_validations_client__WEBPACK_IMPORTED_MODULE_2__.updateWhitelistSchema.parse({\n            ...body,\n            clientId: id\n        });\n        const updateData = {\n            walletAddress: validatedData.walletAddress,\n            isWhitelisted: validatedData.isWhitelisted,\n            updatedAt: new Date()\n        };\n        // Set whitelisted date if being whitelisted\n        if (validatedData.isWhitelisted) {\n            updateData.whitelistedAt = new Date();\n        } else {\n            updateData.whitelistedAt = null;\n        }\n        const client = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.update({\n            where: {\n                id\n            },\n            data: updateData,\n            select: {\n                id: true,\n                firstName: true,\n                lastName: true,\n                walletAddress: true,\n                isWhitelisted: true,\n                whitelistedAt: true,\n                updatedAt: true\n            }\n        });\n        // TODO: Integrate with blockchain whitelist contract\n        // This would call the actual smart contract to add/remove from whitelist\n        // For now, we're just updating the database\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(client);\n    } catch (error) {\n        console.error('Error updating whitelist status:', error);\n        if (error instanceof Error && error.message.includes('Record to update not found')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Client not found'\n            }, {\n                status: 404\n            });\n        }\n        if (error instanceof Error && error.message.includes('Unique constraint')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Wallet address already exists for another client'\n            }, {\n                status: 409\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to update whitelist status'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET /api/clients/[id]/whitelist - Get whitelist status\nasync function GET(request, { params }) {\n    try {\n        const { id } = await params;\n        const client = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.findUnique({\n            where: {\n                id\n            },\n            select: {\n                id: true,\n                firstName: true,\n                lastName: true,\n                walletAddress: true,\n                isWhitelisted: true,\n                whitelistedAt: true,\n                kycStatus: true\n            }\n        });\n        if (!client) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Client not found'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(client);\n    } catch (error) {\n        console.error('Error fetching whitelist status:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch whitelist status'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/clients/[id]/whitelist/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log:  true ? [\n        'query',\n        'error',\n        'warn'\n    ] : 0\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsQ0FBQztJQUMvREksS0FBS0MsS0FBc0MsR0FBRztRQUFDO1FBQVM7UUFBUztLQUFPLEdBQUcsQ0FBUztBQUN0RixHQUFHO0FBRUgsSUFBSUEsSUFBcUMsRUFBRUosZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcclxuXHJcbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XHJcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KHtcclxuICBsb2c6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnID8gWydxdWVyeScsICdlcnJvcicsICd3YXJuJ10gOiBbJ2Vycm9yJ10sXHJcbn0pO1xyXG5cclxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/validations/client.ts":
/*!***************************************!*\
  !*** ./src/lib/validations/client.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GenderEnum: () => (/* binding */ GenderEnum),\n/* harmony export */   IdentificationTypeEnum: () => (/* binding */ IdentificationTypeEnum),\n/* harmony export */   KYCStatusEnum: () => (/* binding */ KYCStatusEnum),\n/* harmony export */   PEPStatusEnum: () => (/* binding */ PEPStatusEnum),\n/* harmony export */   clientQuerySchema: () => (/* binding */ clientQuerySchema),\n/* harmony export */   createClientSchema: () => (/* binding */ createClientSchema),\n/* harmony export */   updateClientSchema: () => (/* binding */ updateClientSchema),\n/* harmony export */   updateKYCSchema: () => (/* binding */ updateKYCSchema),\n/* harmony export */   updateWhitelistSchema: () => (/* binding */ updateWhitelistSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n// Enums\nconst GenderEnum = zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n    'MALE',\n    'FEMALE',\n    'OTHER',\n    'PREFER_NOT_TO_SAY'\n]);\nconst IdentificationTypeEnum = zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n    'PASSPORT',\n    'ID_CARD',\n    'DRIVERS_LICENSE',\n    'OTHER'\n]);\nconst PEPStatusEnum = zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n    'NOT_PEP',\n    'DOMESTIC_PEP',\n    'FOREIGN_PEP',\n    'INTERNATIONAL_ORG_PEP',\n    'FAMILY_MEMBER',\n    'CLOSE_ASSOCIATE'\n]);\nconst KYCStatusEnum = zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n    'PENDING',\n    'IN_REVIEW',\n    'APPROVED',\n    'REJECTED',\n    'EXPIRED'\n]);\n// Client creation schema\nconst createClientSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    // Personal Information\n    firstName: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'First name is required').max(100),\n    lastName: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Last name is required').max(100),\n    gender: GenderEnum,\n    nationality: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Nationality is required').max(100),\n    birthday: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().refine((date)=>{\n        const parsedDate = new Date(date);\n        const today = new Date();\n        const age = today.getFullYear() - parsedDate.getFullYear();\n        return age >= 18 && age <= 120;\n    }, 'Must be between 18 and 120 years old'),\n    birthPlace: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Birth place is required').max(200),\n    // Identification\n    identificationType: IdentificationTypeEnum,\n    passportNumber: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    idCardNumber: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    documentExpiration: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().refine((date)=>{\n        const parsedDate = new Date(date);\n        const today = new Date();\n        return parsedDate > today;\n    }, 'Document expiration must be in the future'),\n    // Contact Information\n    phoneNumber: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Phone number is required').regex(/^\\+?[\\d\\s\\-\\(\\)]+$/, 'Invalid phone number format'),\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email('Invalid email format').optional(),\n    // Professional Information\n    occupation: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Occupation is required').max(200),\n    sectorOfActivity: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Sector of activity is required').max(200),\n    pepStatus: PEPStatusEnum,\n    pepDetails: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().max(1000).optional(),\n    // Address Information\n    street: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Street is required').max(200),\n    buildingNumber: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Building number is required').max(50),\n    city: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'City is required').max(100),\n    state: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().max(100).optional(),\n    country: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Country is required').max(100),\n    zipCode: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Zip code is required').max(20),\n    // Financial Information\n    sourceOfWealth: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Source of wealth is required').max(500),\n    bankAccountNumber: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Bank account number is required').max(50),\n    sourceOfFunds: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Source of funds is required').max(500),\n    taxIdentificationNumber: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Tax identification number is required').max(50)\n}).refine((data)=>{\n    // Ensure at least one identification document is provided\n    if (data.identificationType === 'PASSPORT' && !data.passportNumber) {\n        return false;\n    }\n    if ((data.identificationType === 'ID_CARD' || data.identificationType === 'DRIVERS_LICENSE') && !data.idCardNumber) {\n        return false;\n    }\n    return true;\n}, {\n    message: 'Identification document number is required based on the selected type',\n    path: [\n        'passportNumber',\n        'idCardNumber'\n    ]\n});\n// Client update schema (all fields optional except id)\nconst updateClientSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().cuid(),\n    // Personal Information (all optional for updates)\n    firstName: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(100).optional(),\n    lastName: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(100).optional(),\n    gender: GenderEnum.optional(),\n    nationality: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(100).optional(),\n    birthday: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    birthPlace: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(200).optional(),\n    // Identification (optional for updates)\n    identificationType: IdentificationTypeEnum.optional(),\n    passportNumber: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    idCardNumber: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    documentExpiration: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    // Contact Information (optional for updates)\n    phoneNumber: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\+?[\\d\\s\\-\\(\\)]+$/, 'Invalid phone number format').optional(),\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email('Invalid email format').optional(),\n    // Professional Information (optional for updates)\n    occupation: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(200).optional(),\n    sectorOfActivity: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(200).optional(),\n    pepStatus: PEPStatusEnum.optional(),\n    pepDetails: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().max(1000).optional(),\n    // Address Information (optional for updates)\n    street: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(200).optional(),\n    buildingNumber: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(50).optional(),\n    city: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(100).optional(),\n    state: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().max(100).optional(),\n    country: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(100).optional(),\n    zipCode: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(20).optional(),\n    // Financial Information (optional for updates)\n    sourceOfWealth: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(500).optional(),\n    bankAccountNumber: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(50).optional(),\n    sourceOfFunds: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(500).optional(),\n    taxIdentificationNumber: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(50).optional(),\n    // KYC and Blockchain fields\n    kycStatus: KYCStatusEnum.optional(),\n    kycNotes: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().max(1000).optional(),\n    walletAddress: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^0x[a-fA-F0-9]{40}$/, 'Invalid wallet address').optional(),\n    isWhitelisted: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().optional(),\n    // Agreement fields\n    agreementAccepted: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().optional(),\n    agreementAcceptedAt: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional()\n});\n// Client query schema\nconst clientQuerySchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    page: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform(Number).pipe(zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(1)).default('1'),\n    limit: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform(Number).pipe(zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(1).max(100)).default('10'),\n    search: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    kycStatus: KYCStatusEnum.optional(),\n    isWhitelisted: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().optional(),\n    sortBy: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'createdAt',\n        'firstName',\n        'lastName',\n        'kycStatus'\n    ]).default('createdAt'),\n    sortOrder: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'asc',\n        'desc'\n    ]).default('desc')\n});\n// KYC update schema\nconst updateKYCSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    clientId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().cuid(),\n    kycStatus: KYCStatusEnum,\n    kycNotes: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().max(1000).optional()\n});\n// Whitelist update schema\nconst updateWhitelistSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    clientId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().cuid(),\n    walletAddress: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^0x[a-fA-F0-9]{40}$/, 'Invalid wallet address'),\n    isWhitelisted: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/validations/client.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclients%2F%5Bid%5D%2Fwhitelist%2Froute&page=%2Fapi%2Fclients%2F%5Bid%5D%2Fwhitelist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclients%2F%5Bid%5D%2Fwhitelist%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();