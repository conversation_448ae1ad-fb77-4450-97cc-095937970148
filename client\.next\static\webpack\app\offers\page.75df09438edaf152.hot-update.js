"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/offers/page",{

/***/ "(app-pages-browser)/./src/app/offers/page.tsx":
/*!*********************************!*\
  !*** ./src/app/offers/page.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OffersPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @auth0/nextjs-auth0/client */ \"(app-pages-browser)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Navbar */ \"(app-pages-browser)/./src/components/Navbar.tsx\");\n/* harmony import */ var _components_KYCModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/KYCModal */ \"(app-pages-browser)/./src/components/KYCModal.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _components_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/providers/MockAuthProvider */ \"(app-pages-browser)/./src/components/providers/MockAuthProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction OffersPage() {\n    _s();\n    const useMockAuth = \"false\" === 'true';\n    // Use mock auth or real Auth0 based on environment\n    const auth0User = (0,_auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2__.useUser)();\n    const mockAuth = useMockAuth ? (0,_components_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_6__.useMockUser)() : {\n        user: undefined,\n        isLoading: false\n    };\n    const user = useMockAuth ? mockAuth.user : auth0User.user;\n    const userLoading = useMockAuth ? mockAuth.isLoading : auth0User.isLoading;\n    const [tokens, setTokens] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showKYCModal, setShowKYCModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showOrderModal, setShowOrderModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTokenForOrder, setSelectedTokenForOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [orderAmount, setOrderAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSubmittingOrder, setIsSubmittingOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [orderError, setOrderError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const apiClient = (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_5__.useApiClient)();\n    // Fetch client profile\n    const { data: clientProfile } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'client-profile'\n        ],\n        queryFn: {\n            \"OffersPage.useQuery\": ()=>apiClient.getClientProfile()\n        }[\"OffersPage.useQuery\"],\n        enabled: !!user\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OffersPage.useEffect\": ()=>{\n            fetchTokens();\n        }\n    }[\"OffersPage.useEffect\"], [\n        clientProfile === null || clientProfile === void 0 ? void 0 : clientProfile.walletAddress\n    ]); // Refetch when wallet address changes\n    const fetchTokens = async ()=>{\n        try {\n            setLoading(true);\n            // Construct URL with proper query parameters\n            const params = new URLSearchParams();\n            if (clientProfile === null || clientProfile === void 0 ? void 0 : clientProfile.walletAddress) {\n                params.append('testWallet', clientProfile.walletAddress);\n            }\n            params.append('_t', Date.now().toString());\n            const url = \"/api/tokens?\".concat(params.toString());\n            console.log('Fetching tokens from:', url);\n            const response = await fetch(url);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Token fetch error:', response.status, errorText);\n                throw new Error(\"Failed to fetch tokens: \".concat(response.status));\n            }\n            const data = await response.json();\n            console.log('Fetched tokens:', data);\n            setTokens(data);\n        } catch (err) {\n            console.error('Error in fetchTokens:', err);\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const formatPrice = (price, currency)=>{\n        const numPrice = parseFloat(price);\n        // Handle crypto currencies that don't have standard currency codes\n        const cryptoCurrencies = [\n            'ETH',\n            'BTC',\n            'USDC',\n            'USDT',\n            'DAI'\n        ];\n        if (cryptoCurrencies.includes(currency.toUpperCase())) {\n            return \"\".concat(numPrice, \" \").concat(currency.toUpperCase());\n        }\n        // Handle standard fiat currencies\n        const supportedCurrencies = [\n            'USD',\n            'EUR',\n            'GBP',\n            'JPY',\n            'CAD',\n            'AUD'\n        ];\n        const currencyCode = supportedCurrencies.includes(currency.toUpperCase()) ? currency.toUpperCase() : 'USD';\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: currencyCode,\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 2\n        }).format(numPrice);\n    };\n    const formatSupply = function(supply) {\n        let decimals = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        const numSupply = parseFloat(supply);\n        // Handle very large numbers (like 1000000000000000000000000)\n        if (decimals > 0 && numSupply > 1000000000000) {\n            // This is likely already in wei/smallest unit, convert to human readable\n            const humanReadable = numSupply / Math.pow(10, decimals);\n            return new Intl.NumberFormat('en-US', {\n                maximumFractionDigits: 0\n            }).format(humanReadable);\n        }\n        // For normal numbers or 0 decimals, display as-is\n        return new Intl.NumberFormat('en-US', {\n            maximumFractionDigits: 0\n        }).format(numSupply);\n    };\n    const getCategoryColor = (category)=>{\n        switch(category.toLowerCase()){\n            case 'commodity':\n            case 'commodities':\n                return 'bg-amber-100 text-amber-800';\n            case 'real estate':\n            case 'realestate':\n                return 'bg-green-100 text-green-800';\n            case 'equity':\n            case 'equities':\n                return 'bg-blue-100 text-blue-800';\n            case 'debt':\n            case 'bonds':\n                return 'bg-purple-100 text-purple-800';\n            case 'fund':\n            case 'funds':\n                return 'bg-indigo-100 text-indigo-800';\n            case 'security':\n            case 'securities':\n                return 'bg-teal-100 text-teal-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getDefaultImage = (category)=>{\n        switch(category.toLowerCase()){\n            case 'commodity':\n            case 'commodities':\n                return '🏗️';\n            case 'real estate':\n            case 'realestate':\n                return '🏢';\n            case 'equity':\n            case 'equities':\n                return '📈';\n            case 'debt':\n            case 'bonds':\n                return '💰';\n            case 'fund':\n            case 'funds':\n                return '🏦';\n            case 'security':\n            case 'securities':\n                return '🛡️';\n            default:\n                return '🪙';\n        }\n    };\n    const handleOrderSubmit = async ()=>{\n        if (!selectedTokenForOrder || !orderAmount || !(clientProfile === null || clientProfile === void 0 ? void 0 : clientProfile.id)) {\n            setOrderError('Missing token, amount, or client information.');\n            return;\n        }\n        // Debug logging\n        console.log('Order submission debug:', {\n            tokenPrice: selectedTokenForOrder.price,\n            tokenCurrency: selectedTokenForOrder.currency,\n            orderAmount: orderAmount,\n            clientWallet: clientProfile.walletAddress,\n            tokenWhitelisted: selectedTokenForOrder.isWhitelisted\n        });\n        // Validate orderAmount is a positive number\n        const amountNumber = parseFloat(orderAmount);\n        if (isNaN(amountNumber) || amountNumber <= 0) {\n            setOrderError('Please enter a valid positive amount.');\n            return;\n        }\n        // Check if amount exceeds max supply\n        if (amountNumber > Number(selectedTokenForOrder.maxSupply)) {\n            setOrderError(\"Cannot order more than \".concat(selectedTokenForOrder.maxSupply, \" tokens\"));\n            return;\n        }\n        // Check if user is whitelisted for this token\n        console.log('Whitelist check:', {\n            tokenAddress: selectedTokenForOrder.address,\n            isWhitelisted: selectedTokenForOrder.isWhitelisted,\n            clientWallet: clientProfile.walletAddress\n        });\n        if (!selectedTokenForOrder.isWhitelisted) {\n            setOrderError(\"You must be whitelisted for this token before placing an order. Please contact support to get whitelisted for \".concat(selectedTokenForOrder.name, \".\"));\n            return;\n        }\n        setIsSubmittingOrder(true);\n        setOrderError(null);\n        try {\n            const response = await fetch('/api/client-orders', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenId: selectedTokenForOrder.id,\n                    clientId: clientProfile.id,\n                    tokensOrdered: orderAmount\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to submit order');\n            }\n            // Order submitted successfully\n            setShowOrderModal(false);\n            setSelectedTokenForOrder(null);\n            setOrderAmount('');\n            // Show success message with order details\n            const totalAmount = formatPrice((amountNumber * Number(selectedTokenForOrder.price)).toString(), selectedTokenForOrder.currency);\n            alert(\"Order submitted successfully!\\n\\nToken: \".concat(selectedTokenForOrder.name, \"\\nAmount: \").concat(orderAmount, \" tokens\\nTotal: \").concat(totalAmount, \"\\n\\nYou will be notified once it is approved.\"));\n        } catch (err) {\n            console.error('Error submitting order:', err);\n            setOrderError(err.message || 'Failed to submit order. Please try again.');\n        } finally{\n            setIsSubmittingOrder(false);\n        }\n    };\n    if (userLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                lineNumber: 276,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n            lineNumber: 275,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                    children: \"TokenDev Offers\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Please log in to view available token offers\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: useMockAuth ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: mockAuth.login,\n                                disabled: mockAuth.isLoading,\n                                className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50\",\n                                children: mockAuth.isLoading ? 'Signing In...' : 'Sign In (Demo)'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/api/auth/login\",\n                                className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors inline-block text-center\",\n                                children: \"Sign In with Auth0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                lineNumber: 286,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n            lineNumber: 285,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_3__.Navbar, {\n                user: user,\n                clientProfile: clientProfile,\n                onGetQualified: ()=>setShowKYCModal(true),\n                onLogout: useMockAuth ? mockAuth.logout : undefined,\n                useMockAuth: useMockAuth\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                                children: \"Token Investment Opportunities\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Discover and invest in qualified security tokens based on your claims\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500 mb-1\",\n                                                    children: \"Wallet Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"w3m-button\", {\n                                                    size: \"sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, this),\n                            user && clientProfile && tokens.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-blue-900 mb-1\",\n                                                    children: \"Available Investment Opportunities\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-700 text-sm\",\n                                                    children: [\n                                                        \"You have access to \",\n                                                        tokens.length,\n                                                        \" investment \",\n                                                        tokens.length === 1 ? 'opportunity' : 'opportunities'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-blue-900\",\n                                                    children: tokens.length\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-blue-600\",\n                                                    children: \"Available Tokens\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, this),\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-5 w-5 text-red-400\",\n                                        viewBox: \"0 0 20 20\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-red-800\",\n                                            children: \"Error loading tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-700 mt-1\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 11\n                    }, this),\n                    !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: tokens.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: \"\\uD83E\\uDE99\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"No tokens available\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Check back later for new investment opportunities.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: tokens.map((token)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-48 bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n                                            children: token.imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: token.imageUrl,\n                                                alt: token.name,\n                                                className: \"w-24 h-24 object-cover rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 25\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-6xl\",\n                                                children: getDefaultImage(token.category)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                                                    children: token.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500 font-mono\",\n                                                                    children: token.symbol\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col gap-1 items-end\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(getCategoryColor(token.category)),\n                                                                    children: token.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 441,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                token.isQualified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\",\n                                                                    children: \"✅ QUALIFIED\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                token.isWhitelisted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\",\n                                                                    children: \"WHITELISTED\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 450,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: formatPrice(token.price, token.currency)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"per token\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4 mb-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"Total Supply\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 468,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: formatSupply(token.totalSupply, token.decimals)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 469,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"Max Supply\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 474,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: formatSupply(token.maxSupply, token.decimals)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 475,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 23\n                                                }, this),\n                                                token.requiredClaims && token.requiredClaims.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500 mb-2\",\n                                                            children: \"Required Qualifications\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-1\",\n                                                            children: token.requiredClaims.map((claim)=>{\n                                                                var _token_userClaims;\n                                                                const hasThisClaim = ((_token_userClaims = token.userClaims) === null || _token_userClaims === void 0 ? void 0 : _token_userClaims[claim]) === true;\n                                                                const claimDisplayName = claim.replace('_CLAIM', '').replace('_', ' ');\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center px-2 py-1 text-xs font-medium rounded-full \".concat(hasThisClaim ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                                    children: [\n                                                                        hasThisClaim ? '✅' : '❌',\n                                                                        \" \",\n                                                                        claimDisplayName\n                                                                    ]\n                                                                }, claim, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 33\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 25\n                                                }, this),\n                                                token.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mb-4 line-clamp-2\",\n                                                    children: token.description\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 25\n                                                }, this),\n                                                user && clientProfile ? token.isQualified ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        setSelectedTokenForOrder(token);\n                                                        setShowOrderModal(true);\n                                                    },\n                                                    className: \"w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors font-medium\",\n                                                    children: \"\\uD83D\\uDE80 Invest Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 27\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            disabled: true,\n                                                            className: \"w-full bg-gray-400 text-white py-2 px-4 rounded-lg cursor-not-allowed font-medium\",\n                                                            title: \"You need to complete the required qualifications\",\n                                                            children: \"❌ Qualification Required\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600 text-center\",\n                                                            children: \"Complete the required qualifications above to invest\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 27\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        if (!user) {\n                                                            // Redirect to login\n                                                            window.location.href = '/api/auth/login';\n                                                        } else {\n                                                            // Show KYC modal\n                                                            setShowKYCModal(true);\n                                                        }\n                                                    },\n                                                    className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium\",\n                                                    children: !user ? 'Sign In to View Offers' : 'Complete Qualification'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-6 py-3 bg-gray-50 border-t\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center text-xs text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Network: \",\n                                                            token.network\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Decimals: \",\n                                                            token.decimals\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, token.id, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                lineNumber: 331,\n                columnNumber: 7\n            }, this),\n            showKYCModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_KYCModal__WEBPACK_IMPORTED_MODULE_4__.KYCModal, {\n                onClose: ()=>setShowKYCModal(false),\n                existingProfile: clientProfile\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                lineNumber: 575,\n                columnNumber: 9\n            }, this),\n            showOrderModal && selectedTokenForOrder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg leading-6 font-medium text-gray-900\",\n                                        children: [\n                                            \"Order \",\n                                            selectedTokenForOrder.name,\n                                            \" (\",\n                                            selectedTokenForOrder.symbol,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 588,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowOrderModal(false);\n                                            setSelectedTokenForOrder(null);\n                                            setOrderAmount('');\n                                            setOrderError(null);\n                                        },\n                                        className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 591,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                lineNumber: 587,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Token Details\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 p-3 rounded-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Token:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            selectedTokenForOrder.name,\n                                                            \" (\",\n                                                            selectedTokenForOrder.symbol,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Price:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                lineNumber: 618,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            formatPrice(selectedTokenForOrder.price, selectedTokenForOrder.currency),\n                                                            \" per token\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Available:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            selectedTokenForOrder.maxSupply,\n                                                            \" tokens\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                        lineNumber: 620,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 609,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"order-amount\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Number of Tokens to Order\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"order-amount\",\n                                                type: \"number\",\n                                                min: \"1\",\n                                                step: \"1\",\n                                                value: orderAmount,\n                                                onChange: (e)=>{\n                                                    const value = e.target.value;\n                                                    setOrderAmount(value);\n                                                    setOrderError(null);\n                                                },\n                                                placeholder: \"Enter amount of tokens\",\n                                                className: \"block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 631,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 627,\n                                        columnNumber: 17\n                                    }, this),\n                                    orderAmount && !isNaN(Number(orderAmount)) && Number(orderAmount) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 p-3 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-blue-800\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Total Amount:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                        lineNumber: 651,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \" \",\n                                                    (()=>{\n                                                        const tokenPrice = Number(selectedTokenForOrder.price);\n                                                        const orderQty = Number(orderAmount);\n                                                        console.log('Price calculation debug:', {\n                                                            tokenPrice,\n                                                            orderQty,\n                                                            priceIsNaN: isNaN(tokenPrice),\n                                                            qtyIsNaN: isNaN(orderQty),\n                                                            rawPrice: selectedTokenForOrder.price\n                                                        });\n                                                        if (isNaN(tokenPrice) || isNaN(orderQty)) {\n                                                            return \"Error: Price=\".concat(selectedTokenForOrder.price, \", Qty=\").concat(orderAmount);\n                                                        }\n                                                        const total = orderQty * tokenPrice;\n                                                        return formatPrice(total.toString(), selectedTokenForOrder.currency);\n                                                    })()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-600 mt-1\",\n                                                children: \"This order will be submitted for admin approval\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 19\n                                    }, this),\n                                    orderError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 rounded-md p-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600\",\n                                            children: orderError\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                lineNumber: 607,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 flex flex-col space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleOrderSubmit,\n                                        disabled: isSubmittingOrder || !orderAmount,\n                                        className: \"w-full px-4 py-2 bg-green-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isSubmittingOrder ? 'Submitting...' : 'Submit Order'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 687,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowOrderModal(false);\n                                            setSelectedTokenForOrder(null);\n                                            setOrderAmount('');\n                                            setOrderError(null);\n                                        },\n                                        className: \"w-full px-4 py-2 bg-gray-200 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                lineNumber: 686,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                        lineNumber: 585,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                    lineNumber: 584,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                lineNumber: 583,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-4 right-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"w3m-button\", {}, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                    lineNumber: 713,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                lineNumber: 712,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n        lineNumber: 322,\n        columnNumber: 5\n    }, this);\n}\n_s(OffersPage, \"ApMYDulHuTrKf3ifJxh2CXa6xLY=\", false, function() {\n    return [\n        _auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2__.useUser,\n        _components_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_6__.useMockUser,\n        _lib_api_client__WEBPACK_IMPORTED_MODULE_5__.useApiClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery\n    ];\n});\n_c = OffersPage;\nvar _c;\n$RefreshReg$(_c, \"OffersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/offers/page.tsx\n"));

/***/ })

});