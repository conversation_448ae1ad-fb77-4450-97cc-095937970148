'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import IdentityManagement from '@/components/IdentityManagement';
import { 
  Loader2, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Users, 
  Shield, 
  FileCheck,
  Settings,
  Activity,
  Database
} from 'lucide-react';

interface ContractInfo {
  address: string;
  name: string;
  status: 'connected' | 'error' | 'unknown';
}

export default function IdentityPage() {
  const [contracts, setContracts] = useState<ContractInfo[]>([]);
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [batchAddresses, setBatchAddresses] = useState('');
  const [batchLoading, setBatchLoading] = useState<string | null>(null);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    fetchContractInfo();
    fetchStats();
  }, []);

  const fetchContractInfo = async () => {
    const contractAddresses = [
      { 
        address: process.env.NEXT_PUBLIC_CLAIM_REGISTRY_ADDRESS || process.env.CLAIM_REGISTRY_ADDRESS || '', 
        name: 'ClaimRegistry' 
      },
      { 
        address: process.env.NEXT_PUBLIC_IDENTITY_REGISTRY_ADDRESS || process.env.IDENTITY_REGISTRY_ADDRESS || '', 
        name: 'IdentityRegistry' 
      },
      { 
        address: process.env.NEXT_PUBLIC_COMPLIANCE_ADDRESS || process.env.COMPLIANCE_ADDRESS || '', 
        name: 'Compliance' 
      }
    ];

    const contractInfo = contractAddresses.map(contract => ({
      ...contract,
      status: contract.address ? 'connected' as const : 'error' as const
    }));

    setContracts(contractInfo);
  };

  const fetchStats = async () => {
    try {
      // This would typically fetch from your API
      // For now, we'll use placeholder data
      setStats({
        totalVerified: 0,
        totalWhitelisted: 0,
        totalKycApproved: 0,
        totalClaims: 0
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const performBatchAction = async (action: string) => {
    const addresses = batchAddresses
      .split('\n')
      .map(addr => addr.trim())
      .filter(addr => addr.length > 0);

    if (addresses.length === 0) {
      setMessage({ type: 'error', text: 'Please enter at least one address' });
      return;
    }

    if (addresses.length > 50) {
      setMessage({ type: 'error', text: 'Maximum 50 addresses allowed per batch' });
      return;
    }

    setBatchLoading(action);
    setMessage(null);

    try {
      const response = await fetch('/api/identity/batch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action,
          addresses
        })
      });

      const data = await response.json();

      if (response.ok) {
        setMessage({ 
          type: 'success', 
          text: `Batch ${action} completed: ${data.summary.successful}/${data.summary.total} successful` 
        });
        setBatchAddresses('');
        fetchStats(); // Refresh stats
      } else {
        throw new Error(data.error || 'Batch action failed');
      }
    } catch (error) {
      console.error(`Error performing batch ${action}:`, error);
      setMessage({ type: 'error', text: `Failed to perform batch ${action}: ${error.message}` });
    } finally {
      setBatchLoading(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'connected':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Connected</Badge>;
      case 'error':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Not Configured</Badge>;
      default:
        return <Badge variant="secondary"><AlertCircle className="w-3 h-3 mr-1" />Unknown</Badge>;
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">ERC-3643 Identity Management</h1>
        <Badge variant="outline" className="text-sm">
          Compliance System v3.0
        </Badge>
      </div>

      {message && (
        <Alert variant={message.type === 'error' ? 'destructive' : 'default'}>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{message.text}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="individual" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="individual">Individual Management</TabsTrigger>
          <TabsTrigger value="batch">Batch Operations</TabsTrigger>
          <TabsTrigger value="stats">Statistics</TabsTrigger>
          <TabsTrigger value="system">System Status</TabsTrigger>
        </TabsList>

        <TabsContent value="individual" className="space-y-6">
          <IdentityManagement onStatusUpdate={fetchStats} />
        </TabsContent>

        <TabsContent value="batch" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="w-5 h-5 mr-2" />
                Batch Operations
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Wallet Addresses (one per line, max 50)
                </label>
                <Textarea
                  placeholder="0x1234...&#10;0x5678...&#10;0x9abc..."
                  value={batchAddresses}
                  onChange={(e) => setBatchAddresses(e.target.value)}
                  rows={8}
                  className="font-mono text-sm"
                />
                <p className="text-sm text-gray-500 mt-1">
                  {batchAddresses.split('\n').filter(addr => addr.trim().length > 0).length} addresses
                </p>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <Button
                  onClick={() => performBatchAction('batch_whitelist')}
                  disabled={!!batchLoading}
                  variant="outline"
                >
                  {batchLoading === 'batch_whitelist' ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Batch Whitelist'}
                </Button>

                <Button
                  onClick={() => performBatchAction('batch_approve_kyc')}
                  disabled={!!batchLoading}
                  variant="outline"
                >
                  {batchLoading === 'batch_approve_kyc' ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Batch Approve KYC'}
                </Button>

                <Button
                  onClick={() => performBatchAction('batch_unwhitelist')}
                  disabled={!!batchLoading}
                  variant="outline"
                >
                  {batchLoading === 'batch_unwhitelist' ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Batch Remove Whitelist'}
                </Button>

                <Button
                  onClick={() => performBatchAction('batch_issue_qualification_claims')}
                  disabled={!!batchLoading}
                  variant="outline"
                >
                  {batchLoading === 'batch_issue_qualification_claims' ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Issue Qualification Claims'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stats" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Verified</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{loading ? '...' : stats?.totalVerified || 0}</div>
                <p className="text-xs text-muted-foreground">Registered identities</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Whitelisted</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{loading ? '...' : stats?.totalWhitelisted || 0}</div>
                <p className="text-xs text-muted-foreground">Approved for trading</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">KYC Approved</CardTitle>
                <FileCheck className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{loading ? '...' : stats?.totalKycApproved || 0}</div>
                <p className="text-xs text-muted-foreground">Passed verification</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Claims</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{loading ? '...' : stats?.totalClaims || 0}</div>
                <p className="text-xs text-muted-foreground">Issued credentials</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="system" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="w-5 h-5 mr-2" />
                Contract Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {contracts.map((contract, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <h3 className="font-medium">{contract.name}</h3>
                    <p className="text-sm text-gray-500 font-mono">{contract.address || 'Not configured'}</p>
                  </div>
                  {getStatusBadge(contract.status)}
                </div>
              ))}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database className="w-5 h-5 mr-2" />
                Environment Configuration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>Network:</strong> Polygon Amoy Testnet
                </div>
                <div>
                  <strong>RPC URL:</strong> {process.env.NEXT_PUBLIC_AMOY_RPC_URL || 'Not configured'}
                </div>
                <div>
                  <strong>Admin Address:</strong> {process.env.NEXT_PUBLIC_CLAIM_REGISTRY_ADMIN || 'Not configured'}
                </div>
                <div>
                  <strong>System Version:</strong> ERC-3643 v3.0.0
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
