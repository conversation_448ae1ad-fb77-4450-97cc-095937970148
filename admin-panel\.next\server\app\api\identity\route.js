/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/identity/route";
exports.ids = ["app/api/identity/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fidentity%2Froute&page=%2Fapi%2Fidentity%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fidentity%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fidentity%2Froute&page=%2Fapi%2Fidentity%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fidentity%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_identity_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/identity/route.ts */ \"(rsc)/./src/app/api/identity/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/identity/route\",\n        pathname: \"/api/identity\",\n        filename: \"route\",\n        bundlePath: \"app/api/identity/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\identity\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_identity_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fidentity%2Froute&page=%2Fapi%2Fidentity%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fidentity%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/identity/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/identity/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/wallet/wallet.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/abi/abi-coder.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\n// Contract ABIs (simplified for the functions we need)\nconst IdentityRegistryABI = [\n    \"function registerIdentity(address investor, uint16 country) external\",\n    \"function addToWhitelist(address investor) external\",\n    \"function removeFromWhitelist(address investor) external\",\n    \"function approveKyc(address investor) external\",\n    \"function revokeKyc(address investor) external\",\n    \"function isVerified(address investor) external view returns (bool)\",\n    \"function isWhitelisted(address investor) external view returns (bool)\",\n    \"function isKycApproved(address investor) external view returns (bool)\",\n    \"function investorCountry(address investor) external view returns (uint16)\",\n    \"function isFrozen(address investor) external view returns (bool)\",\n    \"function freezeAddress(address investor) external\",\n    \"function unfreezeAddress(address investor) external\",\n    \"function batchAddToWhitelist(address[] calldata investors) external\",\n    \"function batchApproveKyc(address[] calldata investors) external\",\n    \"function getVerifiedAddressCount() external view returns (uint256)\"\n];\nconst ClaimRegistryABI = [\n    \"function issueClaim(address subject, uint256 topic, bytes calldata signature, bytes calldata data, string calldata uri, uint256 validUntil) external\",\n    \"function revokeClaim(address subject, uint256 topic) external\",\n    \"function hasValidClaim(address subject, uint256 topic) external view returns (bool)\",\n    \"function getClaimsByAddress(address subject) external view returns (tuple(uint256 topic, address issuer, bytes signature, bytes data, string uri, uint256 validUntil, bool revoked)[])\",\n    \"function getTotalClaims() external view returns (uint256)\",\n    \"function getClaimCount(address subject) external view returns (uint256)\"\n];\n// Country code mapping (ISO-3166 numeric)\nconst COUNTRY_CODES = {\n    'US': 840,\n    'USA': 840,\n    'United States': 840,\n    'CA': 124,\n    'Canada': 124,\n    'GB': 826,\n    'UK': 826,\n    'United Kingdom': 826,\n    'DE': 276,\n    'Germany': 276,\n    'FR': 250,\n    'France': 250,\n    'IT': 380,\n    'Italy': 380,\n    'ES': 724,\n    'Spain': 724,\n    'NL': 528,\n    'Netherlands': 528,\n    'CH': 756,\n    'Switzerland': 756,\n    'AU': 36,\n    'Australia': 36,\n    'JP': 392,\n    'Japan': 392,\n    'SG': 702,\n    'Singapore': 702\n};\n// Tokeny Claim Topics (following Tokeny standard)\nconst CLAIM_TOPICS = {\n    KYC: 1,\n    AML: 2,\n    IDENTITY: 3,\n    QUALIFICATION: 4,\n    ACCREDITATION: 5,\n    RESIDENCE: 6,\n    TOKEN_ISSUER: 7\n};\n// Tokeny-style claim data format\nfunction generateTokenyClaim(country, claimType) {\n    const now = new Date();\n    const timestamp = now.getFullYear().toString().slice(-2) + // YY\n    (now.getMonth() + 1).toString().padStart(2, '0') + // MM\n    now.getDate().toString().padStart(2, '0') + // DD\n    now.getHours().toString().padStart(2, '0') + // HH\n    now.getMinutes().toString().padStart(2, '0') + // MM\n    now.getSeconds().toString().padStart(2, '0'); // SS\n    const countryCode = getCountryCode(country).toString().padStart(3, '0');\n    // Additional data based on claim type\n    let additionalData = '';\n    switch(claimType){\n        case 'KYC':\n            additionalData = '001'; // KYC level 1\n            break;\n        case 'QUALIFICATION':\n            additionalData = '002'; // Qualified investor\n            break;\n        case 'TOKEN_ISSUER':\n            additionalData = '003'; // Token issuer\n            break;\n        default:\n            additionalData = '000';\n    }\n    return timestamp + countryCode + additionalData;\n}\nfunction getProvider() {\n    return new ethers__WEBPACK_IMPORTED_MODULE_2__.JsonRpcProvider(process.env.AMOY_RPC_URL);\n}\nfunction getSigner() {\n    const provider = getProvider();\n    return new ethers__WEBPACK_IMPORTED_MODULE_3__.Wallet(process.env.CONTRACT_ADMIN_PRIVATE_KEY, provider);\n}\nfunction getIdentityRegistryContract() {\n    const signer = getSigner();\n    return new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(process.env.IDENTITY_REGISTRY_ADDRESS, IdentityRegistryABI, signer);\n}\nfunction getClaimRegistryContract() {\n    const signer = getSigner();\n    return new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(process.env.CLAIM_REGISTRY_ADDRESS, ClaimRegistryABI, signer);\n}\nfunction getCountryCode(country) {\n    return COUNTRY_CODES[country] || COUNTRY_CODES[country.toUpperCase()] || 840; // Default to USA\n}\n// GET /api/identity?address=0x... - Get identity status\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const address = searchParams.get('address');\n        if (!address) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'address parameter is required'\n            }, {\n                status: 400\n            });\n        }\n        const identityRegistry = getIdentityRegistryContract();\n        const claimRegistry = getClaimRegistryContract();\n        // Get identity status from blockchain\n        const [isVerified, isWhitelisted, isKycApproved, country, isFrozen] = await Promise.all([\n            identityRegistry.isVerified(address),\n            identityRegistry.isWhitelisted(address),\n            identityRegistry.isKycApproved(address),\n            identityRegistry.investorCountry(address),\n            identityRegistry.isFrozen(address)\n        ]);\n        // Get claims\n        let claims = [];\n        try {\n            // First try to get claim count to see if address has any claims\n            const claimCount = await claimRegistry.getClaimCount(address);\n            console.log(`Address ${address} has ${claimCount} claims`);\n            if (claimCount > 0) {\n                claims = await claimRegistry.getClaimsByAddress(address);\n            }\n        } catch (error) {\n            console.warn('Could not fetch claims:', error);\n            // Try individual claim checks for common topics\n            try {\n                const commonTopics = [\n                    1,\n                    4,\n                    7\n                ]; // KYC, Qualification, Token Issuer\n                for (const topic of commonTopics){\n                    const hasValidClaim = await claimRegistry.hasValidClaim(address, topic);\n                    if (hasValidClaim) {\n                        claims.push({\n                            topic,\n                            issuer: '0x0000000000000000000000000000000000000000',\n                            signature: '0x',\n                            data: '0x',\n                            uri: `Topic ${topic} claim`,\n                            validUntil: 0,\n                            revoked: false\n                        });\n                    }\n                }\n            } catch (fallbackError) {\n                console.warn('Fallback claim check also failed:', fallbackError);\n            }\n        }\n        // Get client data from database\n        const client = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.findFirst({\n            where: {\n                walletAddress: {\n                    equals: address,\n                    mode: 'insensitive'\n                }\n            },\n            select: {\n                id: true,\n                firstName: true,\n                lastName: true,\n                email: true,\n                nationality: true,\n                kycStatus: true,\n                isWhitelisted: true\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            address,\n            blockchain: {\n                isVerified,\n                isWhitelisted,\n                isKycApproved,\n                country: country.toString(),\n                isFrozen,\n                claims: claims.length\n            },\n            database: {\n                exists: !!client,\n                kycStatus: client?.kycStatus || 'PENDING',\n                isWhitelisted: client?.isWhitelisted || false,\n                nationality: client?.nationality\n            },\n            client: client || null\n        });\n    } catch (error) {\n        console.error('Error fetching identity status:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch identity status'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/identity - Register identity or update status\nasync function POST(request) {\n    try {\n        const body1 = await request.json();\n        const { action, address, country, clientId } = body1;\n        if (!action || !address) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'action and address are required'\n            }, {\n                status: 400\n            });\n        }\n        const identityRegistry = getIdentityRegistryContract();\n        const claimRegistry = getClaimRegistryContract();\n        let txHash = '';\n        switch(action){\n            case 'register':\n                {\n                    const countryCode = country ? getCountryCode(country) : 840; // Default to USA\n                    const tx = await identityRegistry.registerIdentity(address, countryCode);\n                    await tx.wait();\n                    txHash = tx.hash;\n                    console.log(`✅ Identity registered: ${address} (country: ${countryCode})`);\n                }\n                break;\n            case 'whitelist':\n                {\n                    const tx = await identityRegistry.addToWhitelist(address);\n                    await tx.wait();\n                    txHash = tx.hash;\n                    console.log(`✅ Added to whitelist: ${address}`);\n                    // Update database\n                    if (clientId) {\n                        await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.update({\n                            where: {\n                                id: clientId\n                            },\n                            data: {\n                                isWhitelisted: true\n                            }\n                        });\n                    }\n                }\n                break;\n            case 'unwhitelist':\n                {\n                    const tx = await identityRegistry.removeFromWhitelist(address);\n                    await tx.wait();\n                    txHash = tx.hash;\n                    console.log(`✅ Removed from whitelist: ${address}`);\n                    // Update database\n                    if (clientId) {\n                        await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.update({\n                            where: {\n                                id: clientId\n                            },\n                            data: {\n                                isWhitelisted: false\n                            }\n                        });\n                    }\n                }\n                break;\n            case 'approve_kyc':\n                {\n                    const tx = await identityRegistry.approveKyc(address);\n                    await tx.wait();\n                    txHash = tx.hash;\n                    console.log(`✅ KYC approved: ${address}`);\n                    // Issue Tokeny-style KYC claim\n                    try {\n                        const kycClaimValue = generateTokenyClaim('US', 'KYC'); // Default to US\n                        const claimData = ethers__WEBPACK_IMPORTED_MODULE_5__.AbiCoder.defaultAbiCoder().encode([\n                            \"string\",\n                            \"string\",\n                            \"uint256\"\n                        ], [\n                            kycClaimValue,\n                            \"KYC_APPROVED\",\n                            Math.floor(Date.now() / 1000)\n                        ]);\n                        const claimTx = await claimRegistry.issueClaim(address, CLAIM_TOPICS.KYC, \"0x\", claimData, `KYC:${kycClaimValue}`, 0 // never expires\n                        );\n                        await claimTx.wait();\n                        console.log(`✅ KYC claim issued: ${address} with value: ${kycClaimValue}`);\n                    } catch (error) {\n                        console.warn('Could not issue KYC claim:', error);\n                    }\n                    // Update database\n                    if (clientId) {\n                        await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.update({\n                            where: {\n                                id: clientId\n                            },\n                            data: {\n                                kycStatus: 'APPROVED'\n                            }\n                        });\n                    }\n                }\n                break;\n            case 'revoke_kyc':\n                {\n                    const tx = await identityRegistry.revokeKyc(address);\n                    await tx.wait();\n                    txHash = tx.hash;\n                    console.log(`✅ KYC revoked: ${address}`);\n                    // Revoke KYC claim\n                    try {\n                        const claimTx = await claimRegistry.revokeClaim(address, CLAIM_TOPICS.KYC);\n                        await claimTx.wait();\n                        console.log(`✅ KYC claim revoked: ${address}`);\n                    } catch (error) {\n                        console.warn('Could not revoke KYC claim:', error);\n                    }\n                    // Update database\n                    if (clientId) {\n                        await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.update({\n                            where: {\n                                id: clientId\n                            },\n                            data: {\n                                kycStatus: 'REJECTED'\n                            }\n                        });\n                    }\n                }\n                break;\n            case 'freeze':\n                {\n                    const tx = await identityRegistry.freezeAddress(address);\n                    await tx.wait();\n                    txHash = tx.hash;\n                    console.log(`✅ Address frozen: ${address}`);\n                }\n                break;\n            case 'unfreeze':\n                {\n                    const tx = await identityRegistry.unfreezeAddress(address);\n                    await tx.wait();\n                    txHash = tx.hash;\n                    console.log(`✅ Address unfrozen: ${address}`);\n                }\n                break;\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid action'\n                }, {\n                    status: 400\n                });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            action,\n            address,\n            txHash,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('Error processing identity action:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Failed to ${body.action}: ${error.message}`\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/identity/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log:  true ? [\n        'query',\n        'error',\n        'warn'\n    ] : 0\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsQ0FBQztJQUMvREksS0FBS0MsS0FBc0MsR0FBRztRQUFDO1FBQVM7UUFBUztLQUFPLEdBQUcsQ0FBUztBQUN0RixHQUFHO0FBRUgsSUFBSUEsSUFBcUMsRUFBRUosZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcclxuXHJcbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XHJcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KHtcclxuICBsb2c6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnID8gWydxdWVyeScsICdlcnJvcicsICd3YXJuJ10gOiBbJ2Vycm9yJ10sXHJcbn0pO1xyXG5cclxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy","vendor-chunks/aes-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fidentity%2Froute&page=%2Fapi%2Fidentity%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fidentity%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();