/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/identity/route";
exports.ids = ["app/api/identity/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fidentity%2Froute&page=%2Fapi%2Fidentity%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fidentity%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fidentity%2Froute&page=%2Fapi%2Fidentity%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fidentity%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_identity_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/identity/route.ts */ \"(rsc)/./src/app/api/identity/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/identity/route\",\n        pathname: \"/api/identity\",\n        filename: \"route\",\n        bundlePath: \"app/api/identity/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\identity\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_identity_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fidentity%2Froute&page=%2Fapi%2Fidentity%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fidentity%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/identity/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/identity/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/wallet/wallet.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/abi/abi-coder.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\n// Contract ABIs (simplified for the functions we need)\nconst IdentityRegistryABI = [\n    \"function registerIdentity(address investor, uint16 country) external\",\n    \"function addToWhitelist(address investor) external\",\n    \"function removeFromWhitelist(address investor) external\",\n    \"function approveKyc(address investor) external\",\n    \"function revokeKyc(address investor) external\",\n    \"function isVerified(address investor) external view returns (bool)\",\n    \"function isWhitelisted(address investor) external view returns (bool)\",\n    \"function isKycApproved(address investor) external view returns (bool)\",\n    \"function investorCountry(address investor) external view returns (uint16)\",\n    \"function isFrozen(address investor) external view returns (bool)\",\n    \"function freezeAddress(address investor) external\",\n    \"function unfreezeAddress(address investor) external\",\n    \"function batchAddToWhitelist(address[] calldata investors) external\",\n    \"function batchApproveKyc(address[] calldata investors) external\",\n    \"function getVerifiedAddressCount() external view returns (uint256)\"\n];\nconst ClaimRegistryABI = [\n    \"function issueClaim(address subject, uint256 topic, bytes calldata signature, bytes calldata data, string calldata uri, uint256 validUntil) external\",\n    \"function revokeClaim(address subject, uint256 topic) external\",\n    \"function hasValidClaim(address subject, uint256 topic) external view returns (bool)\",\n    \"function getClaimsByAddress(address subject) external view returns (tuple(uint256 topic, address issuer, bytes signature, bytes data, string uri, uint256 validUntil, bool revoked)[])\"\n];\n// Country code mapping (ISO-3166 numeric)\nconst COUNTRY_CODES = {\n    'US': 840,\n    'USA': 840,\n    'United States': 840,\n    'CA': 124,\n    'Canada': 124,\n    'GB': 826,\n    'UK': 826,\n    'United Kingdom': 826,\n    'DE': 276,\n    'Germany': 276,\n    'FR': 250,\n    'France': 250,\n    'IT': 380,\n    'Italy': 380,\n    'ES': 724,\n    'Spain': 724,\n    'NL': 528,\n    'Netherlands': 528,\n    'CH': 756,\n    'Switzerland': 756,\n    'AU': 36,\n    'Australia': 36,\n    'JP': 392,\n    'Japan': 392,\n    'SG': 702,\n    'Singapore': 702\n};\n// Claim topics\nconst CLAIM_TOPICS = {\n    KYC: 1,\n    QUALIFICATION: 4\n};\nfunction getProvider() {\n    return new ethers__WEBPACK_IMPORTED_MODULE_2__.JsonRpcProvider(process.env.AMOY_RPC_URL);\n}\nfunction getSigner() {\n    const provider = getProvider();\n    return new ethers__WEBPACK_IMPORTED_MODULE_3__.Wallet(process.env.CONTRACT_ADMIN_PRIVATE_KEY, provider);\n}\nfunction getIdentityRegistryContract() {\n    const signer = getSigner();\n    return new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(process.env.IDENTITY_REGISTRY_ADDRESS, IdentityRegistryABI, signer);\n}\nfunction getClaimRegistryContract() {\n    const signer = getSigner();\n    return new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(process.env.CLAIM_REGISTRY_ADDRESS, ClaimRegistryABI, signer);\n}\nfunction getCountryCode(country) {\n    return COUNTRY_CODES[country] || COUNTRY_CODES[country.toUpperCase()] || 840; // Default to USA\n}\n// GET /api/identity?address=0x... - Get identity status\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const address = searchParams.get('address');\n        if (!address) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'address parameter is required'\n            }, {\n                status: 400\n            });\n        }\n        const identityRegistry = getIdentityRegistryContract();\n        const claimRegistry = getClaimRegistryContract();\n        // Get identity status from blockchain\n        const [isVerified, isWhitelisted, isKycApproved, country, isFrozen] = await Promise.all([\n            identityRegistry.isVerified(address),\n            identityRegistry.isWhitelisted(address),\n            identityRegistry.isKycApproved(address),\n            identityRegistry.investorCountry(address),\n            identityRegistry.isFrozen(address)\n        ]);\n        // Get claims\n        let claims = [];\n        try {\n            claims = await claimRegistry.getClaimsByAddress(address);\n        } catch (error) {\n            console.warn('Could not fetch claims:', error);\n        }\n        // Get client data from database\n        const client = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.findFirst({\n            where: {\n                walletAddress: {\n                    equals: address,\n                    mode: 'insensitive'\n                }\n            },\n            select: {\n                id: true,\n                firstName: true,\n                lastName: true,\n                email: true,\n                nationality: true,\n                kycStatus: true,\n                isWhitelisted: true\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            address,\n            blockchain: {\n                isVerified,\n                isWhitelisted,\n                isKycApproved,\n                country: country.toString(),\n                isFrozen,\n                claims: claims.length\n            },\n            database: {\n                exists: !!client,\n                kycStatus: client?.kycStatus || 'PENDING',\n                isWhitelisted: client?.isWhitelisted || false,\n                nationality: client?.nationality\n            },\n            client: client || null\n        });\n    } catch (error) {\n        console.error('Error fetching identity status:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch identity status'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/identity - Register identity or update status\nasync function POST(request) {\n    try {\n        const body1 = await request.json();\n        const { action, address, country, clientId } = body1;\n        if (!action || !address) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'action and address are required'\n            }, {\n                status: 400\n            });\n        }\n        const identityRegistry = getIdentityRegistryContract();\n        const claimRegistry = getClaimRegistryContract();\n        let txHash = '';\n        switch(action){\n            case 'register':\n                {\n                    const countryCode = country ? getCountryCode(country) : 840; // Default to USA\n                    const tx = await identityRegistry.registerIdentity(address, countryCode);\n                    await tx.wait();\n                    txHash = tx.hash;\n                    console.log(`✅ Identity registered: ${address} (country: ${countryCode})`);\n                }\n                break;\n            case 'whitelist':\n                {\n                    const tx = await identityRegistry.addToWhitelist(address);\n                    await tx.wait();\n                    txHash = tx.hash;\n                    console.log(`✅ Added to whitelist: ${address}`);\n                    // Update database\n                    if (clientId) {\n                        await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.update({\n                            where: {\n                                id: clientId\n                            },\n                            data: {\n                                isWhitelisted: true\n                            }\n                        });\n                    }\n                }\n                break;\n            case 'unwhitelist':\n                {\n                    const tx = await identityRegistry.removeFromWhitelist(address);\n                    await tx.wait();\n                    txHash = tx.hash;\n                    console.log(`✅ Removed from whitelist: ${address}`);\n                    // Update database\n                    if (clientId) {\n                        await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.update({\n                            where: {\n                                id: clientId\n                            },\n                            data: {\n                                isWhitelisted: false\n                            }\n                        });\n                    }\n                }\n                break;\n            case 'approve_kyc':\n                {\n                    const tx = await identityRegistry.approveKyc(address);\n                    await tx.wait();\n                    txHash = tx.hash;\n                    console.log(`✅ KYC approved: ${address}`);\n                    // Issue KYC claim\n                    try {\n                        const claimData = ethers__WEBPACK_IMPORTED_MODULE_5__.AbiCoder.defaultAbiCoder().encode([\n                            \"string\",\n                            \"uint256\"\n                        ], [\n                            \"KYC_APPROVED\",\n                            Math.floor(Date.now() / 1000)\n                        ]);\n                        const claimTx = await claimRegistry.issueClaim(address, CLAIM_TOPICS.KYC, \"0x\", claimData, \"\", 0 // never expires\n                        );\n                        await claimTx.wait();\n                        console.log(`✅ KYC claim issued: ${address}`);\n                    } catch (error) {\n                        console.warn('Could not issue KYC claim:', error);\n                    }\n                    // Update database\n                    if (clientId) {\n                        await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.update({\n                            where: {\n                                id: clientId\n                            },\n                            data: {\n                                kycStatus: 'APPROVED'\n                            }\n                        });\n                    }\n                }\n                break;\n            case 'revoke_kyc':\n                {\n                    const tx = await identityRegistry.revokeKyc(address);\n                    await tx.wait();\n                    txHash = tx.hash;\n                    console.log(`✅ KYC revoked: ${address}`);\n                    // Revoke KYC claim\n                    try {\n                        const claimTx = await claimRegistry.revokeClaim(address, CLAIM_TOPICS.KYC);\n                        await claimTx.wait();\n                        console.log(`✅ KYC claim revoked: ${address}`);\n                    } catch (error) {\n                        console.warn('Could not revoke KYC claim:', error);\n                    }\n                    // Update database\n                    if (clientId) {\n                        await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.update({\n                            where: {\n                                id: clientId\n                            },\n                            data: {\n                                kycStatus: 'REJECTED'\n                            }\n                        });\n                    }\n                }\n                break;\n            case 'freeze':\n                {\n                    const tx = await identityRegistry.freezeAddress(address);\n                    await tx.wait();\n                    txHash = tx.hash;\n                    console.log(`✅ Address frozen: ${address}`);\n                }\n                break;\n            case 'unfreeze':\n                {\n                    const tx = await identityRegistry.unfreezeAddress(address);\n                    await tx.wait();\n                    txHash = tx.hash;\n                    console.log(`✅ Address unfrozen: ${address}`);\n                }\n                break;\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid action'\n                }, {\n                    status: 400\n                });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            action,\n            address,\n            txHash,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('Error processing identity action:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Failed to ${body.action}: ${error.message}`\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9pZGVudGl0eS9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUF3RDtBQUN4QjtBQUNNO0FBRXRDLHVEQUF1RDtBQUN2RCxNQUFNRyxzQkFBc0I7SUFDMUI7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0NBQ0Q7QUFFRCxNQUFNQyxtQkFBbUI7SUFDdkI7SUFDQTtJQUNBO0lBQ0E7Q0FDRDtBQUVELDBDQUEwQztBQUMxQyxNQUFNQyxnQkFBMkM7SUFDL0MsTUFBTTtJQUFLLE9BQU87SUFBSyxpQkFBaUI7SUFDeEMsTUFBTTtJQUFLLFVBQVU7SUFDckIsTUFBTTtJQUFLLE1BQU07SUFBSyxrQkFBa0I7SUFDeEMsTUFBTTtJQUFLLFdBQVc7SUFDdEIsTUFBTTtJQUFLLFVBQVU7SUFDckIsTUFBTTtJQUFLLFNBQVM7SUFDcEIsTUFBTTtJQUFLLFNBQVM7SUFDcEIsTUFBTTtJQUFLLGVBQWU7SUFDMUIsTUFBTTtJQUFLLGVBQWU7SUFDMUIsTUFBTTtJQUFJLGFBQWE7SUFDdkIsTUFBTTtJQUFLLFNBQVM7SUFDcEIsTUFBTTtJQUFLLGFBQWE7QUFDMUI7QUFFQSxlQUFlO0FBQ2YsTUFBTUMsZUFBZTtJQUNuQkMsS0FBSztJQUNMQyxlQUFlO0FBQ2pCO0FBRUEsU0FBU0M7SUFDUCxPQUFPLElBQUlSLG1EQUFzQixDQUFDVSxRQUFRQyxHQUFHLENBQUNDLFlBQVk7QUFDNUQ7QUFFQSxTQUFTQztJQUNQLE1BQU1DLFdBQVdOO0lBQ2pCLE9BQU8sSUFBSVIsMENBQWEsQ0FBQ1UsUUFBUUMsR0FBRyxDQUFDSywwQkFBMEIsRUFBR0Y7QUFDcEU7QUFFQSxTQUFTRztJQUNQLE1BQU1DLFNBQVNMO0lBQ2YsT0FBTyxJQUFJYiw0Q0FBZSxDQUN4QlUsUUFBUUMsR0FBRyxDQUFDUyx5QkFBeUIsRUFDckNsQixxQkFDQWdCO0FBRUo7QUFFQSxTQUFTRztJQUNQLE1BQU1ILFNBQVNMO0lBQ2YsT0FBTyxJQUFJYiw0Q0FBZSxDQUN4QlUsUUFBUUMsR0FBRyxDQUFDVyxzQkFBc0IsRUFDbENuQixrQkFDQWU7QUFFSjtBQUVBLFNBQVNLLGVBQWVDLE9BQWU7SUFDckMsT0FBT3BCLGFBQWEsQ0FBQ29CLFFBQVEsSUFBSXBCLGFBQWEsQ0FBQ29CLFFBQVFDLFdBQVcsR0FBRyxJQUFJLEtBQUssaUJBQWlCO0FBQ2pHO0FBRUEsd0RBQXdEO0FBQ2pELGVBQWVDLElBQUlDLE9BQW9CO0lBQzVDLElBQUk7UUFDRixNQUFNLEVBQUVDLFlBQVksRUFBRSxHQUFHLElBQUlDLElBQUlGLFFBQVFHLEdBQUc7UUFDNUMsTUFBTUMsVUFBVUgsYUFBYUksR0FBRyxDQUFDO1FBRWpDLElBQUksQ0FBQ0QsU0FBUztZQUNaLE9BQU9oQyxxREFBWUEsQ0FBQ2tDLElBQUksQ0FDdEI7Z0JBQUVDLE9BQU87WUFBZ0MsR0FDekM7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLE1BQU1DLG1CQUFtQm5CO1FBQ3pCLE1BQU1vQixnQkFBZ0JoQjtRQUV0QixzQ0FBc0M7UUFDdEMsTUFBTSxDQUFDaUIsWUFBWUMsZUFBZUMsZUFBZWhCLFNBQVNpQixTQUFTLEdBQUcsTUFBTUMsUUFBUUMsR0FBRyxDQUFDO1lBQ3RGUCxpQkFBaUJFLFVBQVUsQ0FBQ1A7WUFDNUJLLGlCQUFpQkcsYUFBYSxDQUFDUjtZQUMvQkssaUJBQWlCSSxhQUFhLENBQUNUO1lBQy9CSyxpQkFBaUJRLGVBQWUsQ0FBQ2I7WUFDakNLLGlCQUFpQkssUUFBUSxDQUFDVjtTQUMzQjtRQUVELGFBQWE7UUFDYixJQUFJYyxTQUFTLEVBQUU7UUFDZixJQUFJO1lBQ0ZBLFNBQVMsTUFBTVIsY0FBY1Msa0JBQWtCLENBQUNmO1FBQ2xELEVBQUUsT0FBT0csT0FBTztZQUNkYSxRQUFRQyxJQUFJLENBQUMsMkJBQTJCZDtRQUMxQztRQUVBLGdDQUFnQztRQUNoQyxNQUFNZSxTQUFTLE1BQU1oRCwrQ0FBTUEsQ0FBQ2dELE1BQU0sQ0FBQ0MsU0FBUyxDQUFDO1lBQzNDQyxPQUFPO2dCQUNMQyxlQUFlO29CQUNiQyxRQUFRdEI7b0JBQ1J1QixNQUFNO2dCQUNSO1lBQ0Y7WUFDQUMsUUFBUTtnQkFDTkMsSUFBSTtnQkFDSkMsV0FBVztnQkFDWEMsVUFBVTtnQkFDVkMsT0FBTztnQkFDUEMsYUFBYTtnQkFDYkMsV0FBVztnQkFDWHRCLGVBQWU7WUFDakI7UUFDRjtRQUVBLE9BQU94QyxxREFBWUEsQ0FBQ2tDLElBQUksQ0FBQztZQUN2QkY7WUFDQStCLFlBQVk7Z0JBQ1Z4QjtnQkFDQUM7Z0JBQ0FDO2dCQUNBaEIsU0FBU0EsUUFBUXVDLFFBQVE7Z0JBQ3pCdEI7Z0JBQ0FJLFFBQVFBLE9BQU9tQixNQUFNO1lBQ3ZCO1lBQ0FDLFVBQVU7Z0JBQ1JDLFFBQVEsQ0FBQyxDQUFDakI7Z0JBQ1ZZLFdBQVdaLFFBQVFZLGFBQWE7Z0JBQ2hDdEIsZUFBZVUsUUFBUVYsaUJBQWlCO2dCQUN4Q3FCLGFBQWFYLFFBQVFXO1lBQ3ZCO1lBQ0FYLFFBQVFBLFVBQVU7UUFDcEI7SUFFRixFQUFFLE9BQU9mLE9BQU87UUFDZGEsUUFBUWIsS0FBSyxDQUFDLG1DQUFtQ0E7UUFDakQsT0FBT25DLHFEQUFZQSxDQUFDa0MsSUFBSSxDQUN0QjtZQUFFQyxPQUFPO1FBQWtDLEdBQzNDO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRUEsMERBQTBEO0FBQ25ELGVBQWVnQyxLQUFLeEMsT0FBb0I7SUFDN0MsSUFBSTtRQUNGLE1BQU15QyxRQUFPLE1BQU16QyxRQUFRTSxJQUFJO1FBQy9CLE1BQU0sRUFBRW9DLE1BQU0sRUFBRXRDLE9BQU8sRUFBRVAsT0FBTyxFQUFFOEMsUUFBUSxFQUFFLEdBQUdGO1FBRS9DLElBQUksQ0FBQ0MsVUFBVSxDQUFDdEMsU0FBUztZQUN2QixPQUFPaEMscURBQVlBLENBQUNrQyxJQUFJLENBQ3RCO2dCQUFFQyxPQUFPO1lBQWtDLEdBQzNDO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxNQUFNQyxtQkFBbUJuQjtRQUN6QixNQUFNb0IsZ0JBQWdCaEI7UUFDdEIsSUFBSWtELFNBQVM7UUFFYixPQUFRRjtZQUNOLEtBQUs7Z0JBQ0g7b0JBQ0UsTUFBTUcsY0FBY2hELFVBQVVELGVBQWVDLFdBQVcsS0FBSyxpQkFBaUI7b0JBQzlFLE1BQU1pRCxLQUFLLE1BQU1yQyxpQkFBaUJzQyxnQkFBZ0IsQ0FBQzNDLFNBQVN5QztvQkFDNUQsTUFBTUMsR0FBR0UsSUFBSTtvQkFDYkosU0FBU0UsR0FBR0csSUFBSTtvQkFDaEI3QixRQUFROEIsR0FBRyxDQUFDLENBQUMsdUJBQXVCLEVBQUU5QyxRQUFRLFdBQVcsRUFBRXlDLFlBQVksQ0FBQyxDQUFDO2dCQUMzRTtnQkFDQTtZQUVGLEtBQUs7Z0JBQ0g7b0JBQ0UsTUFBTUMsS0FBSyxNQUFNckMsaUJBQWlCMEMsY0FBYyxDQUFDL0M7b0JBQ2pELE1BQU0wQyxHQUFHRSxJQUFJO29CQUNiSixTQUFTRSxHQUFHRyxJQUFJO29CQUNoQjdCLFFBQVE4QixHQUFHLENBQUMsQ0FBQyxzQkFBc0IsRUFBRTlDLFNBQVM7b0JBRTlDLGtCQUFrQjtvQkFDbEIsSUFBSXVDLFVBQVU7d0JBQ1osTUFBTXJFLCtDQUFNQSxDQUFDZ0QsTUFBTSxDQUFDOEIsTUFBTSxDQUFDOzRCQUN6QjVCLE9BQU87Z0NBQUVLLElBQUljOzRCQUFTOzRCQUN0QlUsTUFBTTtnQ0FBRXpDLGVBQWU7NEJBQUs7d0JBQzlCO29CQUNGO2dCQUNGO2dCQUNBO1lBRUYsS0FBSztnQkFDSDtvQkFDRSxNQUFNa0MsS0FBSyxNQUFNckMsaUJBQWlCNkMsbUJBQW1CLENBQUNsRDtvQkFDdEQsTUFBTTBDLEdBQUdFLElBQUk7b0JBQ2JKLFNBQVNFLEdBQUdHLElBQUk7b0JBQ2hCN0IsUUFBUThCLEdBQUcsQ0FBQyxDQUFDLDBCQUEwQixFQUFFOUMsU0FBUztvQkFFbEQsa0JBQWtCO29CQUNsQixJQUFJdUMsVUFBVTt3QkFDWixNQUFNckUsK0NBQU1BLENBQUNnRCxNQUFNLENBQUM4QixNQUFNLENBQUM7NEJBQ3pCNUIsT0FBTztnQ0FBRUssSUFBSWM7NEJBQVM7NEJBQ3RCVSxNQUFNO2dDQUFFekMsZUFBZTs0QkFBTTt3QkFDL0I7b0JBQ0Y7Z0JBQ0Y7Z0JBQ0E7WUFFRixLQUFLO2dCQUNIO29CQUNFLE1BQU1rQyxLQUFLLE1BQU1yQyxpQkFBaUI4QyxVQUFVLENBQUNuRDtvQkFDN0MsTUFBTTBDLEdBQUdFLElBQUk7b0JBQ2JKLFNBQVNFLEdBQUdHLElBQUk7b0JBQ2hCN0IsUUFBUThCLEdBQUcsQ0FBQyxDQUFDLGdCQUFnQixFQUFFOUMsU0FBUztvQkFFeEMsa0JBQWtCO29CQUNsQixJQUFJO3dCQUNGLE1BQU1vRCxZQUFZbkYsNENBQWUsQ0FBQ3FGLGVBQWUsR0FBR0MsTUFBTSxDQUN4RDs0QkFBQzs0QkFBVTt5QkFBVSxFQUNyQjs0QkFBQzs0QkFBZ0JDLEtBQUtDLEtBQUssQ0FBQ0MsS0FBS0MsR0FBRyxLQUFLO3lCQUFNO3dCQUVqRCxNQUFNQyxVQUFVLE1BQU10RCxjQUFjdUQsVUFBVSxDQUM1QzdELFNBQ0ExQixhQUFhQyxHQUFHLEVBQ2hCLE1BQ0E2RSxXQUNBLElBQ0EsRUFBRSxnQkFBZ0I7O3dCQUVwQixNQUFNUSxRQUFRaEIsSUFBSTt3QkFDbEI1QixRQUFROEIsR0FBRyxDQUFDLENBQUMsb0JBQW9CLEVBQUU5QyxTQUFTO29CQUM5QyxFQUFFLE9BQU9HLE9BQU87d0JBQ2RhLFFBQVFDLElBQUksQ0FBQyw4QkFBOEJkO29CQUM3QztvQkFFQSxrQkFBa0I7b0JBQ2xCLElBQUlvQyxVQUFVO3dCQUNaLE1BQU1yRSwrQ0FBTUEsQ0FBQ2dELE1BQU0sQ0FBQzhCLE1BQU0sQ0FBQzs0QkFDekI1QixPQUFPO2dDQUFFSyxJQUFJYzs0QkFBUzs0QkFDdEJVLE1BQU07Z0NBQUVuQixXQUFXOzRCQUFXO3dCQUNoQztvQkFDRjtnQkFDRjtnQkFDQTtZQUVGLEtBQUs7Z0JBQ0g7b0JBQ0UsTUFBTVksS0FBSyxNQUFNckMsaUJBQWlCeUQsU0FBUyxDQUFDOUQ7b0JBQzVDLE1BQU0wQyxHQUFHRSxJQUFJO29CQUNiSixTQUFTRSxHQUFHRyxJQUFJO29CQUNoQjdCLFFBQVE4QixHQUFHLENBQUMsQ0FBQyxlQUFlLEVBQUU5QyxTQUFTO29CQUV2QyxtQkFBbUI7b0JBQ25CLElBQUk7d0JBQ0YsTUFBTTRELFVBQVUsTUFBTXRELGNBQWN5RCxXQUFXLENBQUMvRCxTQUFTMUIsYUFBYUMsR0FBRzt3QkFDekUsTUFBTXFGLFFBQVFoQixJQUFJO3dCQUNsQjVCLFFBQVE4QixHQUFHLENBQUMsQ0FBQyxxQkFBcUIsRUFBRTlDLFNBQVM7b0JBQy9DLEVBQUUsT0FBT0csT0FBTzt3QkFDZGEsUUFBUUMsSUFBSSxDQUFDLCtCQUErQmQ7b0JBQzlDO29CQUVBLGtCQUFrQjtvQkFDbEIsSUFBSW9DLFVBQVU7d0JBQ1osTUFBTXJFLCtDQUFNQSxDQUFDZ0QsTUFBTSxDQUFDOEIsTUFBTSxDQUFDOzRCQUN6QjVCLE9BQU87Z0NBQUVLLElBQUljOzRCQUFTOzRCQUN0QlUsTUFBTTtnQ0FBRW5CLFdBQVc7NEJBQVc7d0JBQ2hDO29CQUNGO2dCQUNGO2dCQUNBO1lBRUYsS0FBSztnQkFDSDtvQkFDRSxNQUFNWSxLQUFLLE1BQU1yQyxpQkFBaUIyRCxhQUFhLENBQUNoRTtvQkFDaEQsTUFBTTBDLEdBQUdFLElBQUk7b0JBQ2JKLFNBQVNFLEdBQUdHLElBQUk7b0JBQ2hCN0IsUUFBUThCLEdBQUcsQ0FBQyxDQUFDLGtCQUFrQixFQUFFOUMsU0FBUztnQkFDNUM7Z0JBQ0E7WUFFRixLQUFLO2dCQUNIO29CQUNFLE1BQU0wQyxLQUFLLE1BQU1yQyxpQkFBaUI0RCxlQUFlLENBQUNqRTtvQkFDbEQsTUFBTTBDLEdBQUdFLElBQUk7b0JBQ2JKLFNBQVNFLEdBQUdHLElBQUk7b0JBQ2hCN0IsUUFBUThCLEdBQUcsQ0FBQyxDQUFDLG9CQUFvQixFQUFFOUMsU0FBUztnQkFDOUM7Z0JBQ0E7WUFFRjtnQkFDRSxPQUFPaEMscURBQVlBLENBQUNrQyxJQUFJLENBQ3RCO29CQUFFQyxPQUFPO2dCQUFpQixHQUMxQjtvQkFBRUMsUUFBUTtnQkFBSTtRQUVwQjtRQUVBLE9BQU9wQyxxREFBWUEsQ0FBQ2tDLElBQUksQ0FBQztZQUN2QmdFLFNBQVM7WUFDVDVCO1lBQ0F0QztZQUNBd0M7WUFDQTJCLFdBQVcsSUFBSVQsT0FBT1UsV0FBVztRQUNuQztJQUVGLEVBQUUsT0FBT2pFLE9BQU87UUFDZGEsUUFBUWIsS0FBSyxDQUFDLHFDQUFxQ0E7UUFDbkQsT0FBT25DLHFEQUFZQSxDQUFDa0MsSUFBSSxDQUN0QjtZQUFFQyxPQUFPLENBQUMsVUFBVSxFQUFFa0MsS0FBS0MsTUFBTSxDQUFDLEVBQUUsRUFBRW5DLE1BQU1rRSxPQUFPLEVBQUU7UUFBQyxHQUN0RDtZQUFFakUsUUFBUTtRQUFJO0lBRWxCO0FBQ0YiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcYWRtaW4tcGFuZWxcXHNyY1xcYXBwXFxhcGlcXGlkZW50aXR5XFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgZXRoZXJzIH0gZnJvbSAnZXRoZXJzJztcbmltcG9ydCB7IHByaXNtYSB9IGZyb20gJ0AvbGliL3ByaXNtYSc7XG5cbi8vIENvbnRyYWN0IEFCSXMgKHNpbXBsaWZpZWQgZm9yIHRoZSBmdW5jdGlvbnMgd2UgbmVlZClcbmNvbnN0IElkZW50aXR5UmVnaXN0cnlBQkkgPSBbXG4gIFwiZnVuY3Rpb24gcmVnaXN0ZXJJZGVudGl0eShhZGRyZXNzIGludmVzdG9yLCB1aW50MTYgY291bnRyeSkgZXh0ZXJuYWxcIixcbiAgXCJmdW5jdGlvbiBhZGRUb1doaXRlbGlzdChhZGRyZXNzIGludmVzdG9yKSBleHRlcm5hbFwiLFxuICBcImZ1bmN0aW9uIHJlbW92ZUZyb21XaGl0ZWxpc3QoYWRkcmVzcyBpbnZlc3RvcikgZXh0ZXJuYWxcIixcbiAgXCJmdW5jdGlvbiBhcHByb3ZlS3ljKGFkZHJlc3MgaW52ZXN0b3IpIGV4dGVybmFsXCIsXG4gIFwiZnVuY3Rpb24gcmV2b2tlS3ljKGFkZHJlc3MgaW52ZXN0b3IpIGV4dGVybmFsXCIsXG4gIFwiZnVuY3Rpb24gaXNWZXJpZmllZChhZGRyZXNzIGludmVzdG9yKSBleHRlcm5hbCB2aWV3IHJldHVybnMgKGJvb2wpXCIsXG4gIFwiZnVuY3Rpb24gaXNXaGl0ZWxpc3RlZChhZGRyZXNzIGludmVzdG9yKSBleHRlcm5hbCB2aWV3IHJldHVybnMgKGJvb2wpXCIsXG4gIFwiZnVuY3Rpb24gaXNLeWNBcHByb3ZlZChhZGRyZXNzIGludmVzdG9yKSBleHRlcm5hbCB2aWV3IHJldHVybnMgKGJvb2wpXCIsXG4gIFwiZnVuY3Rpb24gaW52ZXN0b3JDb3VudHJ5KGFkZHJlc3MgaW52ZXN0b3IpIGV4dGVybmFsIHZpZXcgcmV0dXJucyAodWludDE2KVwiLFxuICBcImZ1bmN0aW9uIGlzRnJvemVuKGFkZHJlc3MgaW52ZXN0b3IpIGV4dGVybmFsIHZpZXcgcmV0dXJucyAoYm9vbClcIixcbiAgXCJmdW5jdGlvbiBmcmVlemVBZGRyZXNzKGFkZHJlc3MgaW52ZXN0b3IpIGV4dGVybmFsXCIsXG4gIFwiZnVuY3Rpb24gdW5mcmVlemVBZGRyZXNzKGFkZHJlc3MgaW52ZXN0b3IpIGV4dGVybmFsXCIsXG4gIFwiZnVuY3Rpb24gYmF0Y2hBZGRUb1doaXRlbGlzdChhZGRyZXNzW10gY2FsbGRhdGEgaW52ZXN0b3JzKSBleHRlcm5hbFwiLFxuICBcImZ1bmN0aW9uIGJhdGNoQXBwcm92ZUt5YyhhZGRyZXNzW10gY2FsbGRhdGEgaW52ZXN0b3JzKSBleHRlcm5hbFwiLFxuICBcImZ1bmN0aW9uIGdldFZlcmlmaWVkQWRkcmVzc0NvdW50KCkgZXh0ZXJuYWwgdmlldyByZXR1cm5zICh1aW50MjU2KVwiXG5dO1xuXG5jb25zdCBDbGFpbVJlZ2lzdHJ5QUJJID0gW1xuICBcImZ1bmN0aW9uIGlzc3VlQ2xhaW0oYWRkcmVzcyBzdWJqZWN0LCB1aW50MjU2IHRvcGljLCBieXRlcyBjYWxsZGF0YSBzaWduYXR1cmUsIGJ5dGVzIGNhbGxkYXRhIGRhdGEsIHN0cmluZyBjYWxsZGF0YSB1cmksIHVpbnQyNTYgdmFsaWRVbnRpbCkgZXh0ZXJuYWxcIixcbiAgXCJmdW5jdGlvbiByZXZva2VDbGFpbShhZGRyZXNzIHN1YmplY3QsIHVpbnQyNTYgdG9waWMpIGV4dGVybmFsXCIsXG4gIFwiZnVuY3Rpb24gaGFzVmFsaWRDbGFpbShhZGRyZXNzIHN1YmplY3QsIHVpbnQyNTYgdG9waWMpIGV4dGVybmFsIHZpZXcgcmV0dXJucyAoYm9vbClcIixcbiAgXCJmdW5jdGlvbiBnZXRDbGFpbXNCeUFkZHJlc3MoYWRkcmVzcyBzdWJqZWN0KSBleHRlcm5hbCB2aWV3IHJldHVybnMgKHR1cGxlKHVpbnQyNTYgdG9waWMsIGFkZHJlc3MgaXNzdWVyLCBieXRlcyBzaWduYXR1cmUsIGJ5dGVzIGRhdGEsIHN0cmluZyB1cmksIHVpbnQyNTYgdmFsaWRVbnRpbCwgYm9vbCByZXZva2VkKVtdKVwiXG5dO1xuXG4vLyBDb3VudHJ5IGNvZGUgbWFwcGluZyAoSVNPLTMxNjYgbnVtZXJpYylcbmNvbnN0IENPVU5UUllfQ09ERVM6IHsgW2tleTogc3RyaW5nXTogbnVtYmVyIH0gPSB7XG4gICdVUyc6IDg0MCwgJ1VTQSc6IDg0MCwgJ1VuaXRlZCBTdGF0ZXMnOiA4NDAsXG4gICdDQSc6IDEyNCwgJ0NhbmFkYSc6IDEyNCxcbiAgJ0dCJzogODI2LCAnVUsnOiA4MjYsICdVbml0ZWQgS2luZ2RvbSc6IDgyNixcbiAgJ0RFJzogMjc2LCAnR2VybWFueSc6IDI3NixcbiAgJ0ZSJzogMjUwLCAnRnJhbmNlJzogMjUwLFxuICAnSVQnOiAzODAsICdJdGFseSc6IDM4MCxcbiAgJ0VTJzogNzI0LCAnU3BhaW4nOiA3MjQsXG4gICdOTCc6IDUyOCwgJ05ldGhlcmxhbmRzJzogNTI4LFxuICAnQ0gnOiA3NTYsICdTd2l0emVybGFuZCc6IDc1NixcbiAgJ0FVJzogMzYsICdBdXN0cmFsaWEnOiAzNixcbiAgJ0pQJzogMzkyLCAnSmFwYW4nOiAzOTIsXG4gICdTRyc6IDcwMiwgJ1NpbmdhcG9yZSc6IDcwMixcbn07XG5cbi8vIENsYWltIHRvcGljc1xuY29uc3QgQ0xBSU1fVE9QSUNTID0ge1xuICBLWUM6IDEsXG4gIFFVQUxJRklDQVRJT046IDRcbn07XG5cbmZ1bmN0aW9uIGdldFByb3ZpZGVyKCkge1xuICByZXR1cm4gbmV3IGV0aGVycy5Kc29uUnBjUHJvdmlkZXIocHJvY2Vzcy5lbnYuQU1PWV9SUENfVVJMKTtcbn1cblxuZnVuY3Rpb24gZ2V0U2lnbmVyKCkge1xuICBjb25zdCBwcm92aWRlciA9IGdldFByb3ZpZGVyKCk7XG4gIHJldHVybiBuZXcgZXRoZXJzLldhbGxldChwcm9jZXNzLmVudi5DT05UUkFDVF9BRE1JTl9QUklWQVRFX0tFWSEsIHByb3ZpZGVyKTtcbn1cblxuZnVuY3Rpb24gZ2V0SWRlbnRpdHlSZWdpc3RyeUNvbnRyYWN0KCkge1xuICBjb25zdCBzaWduZXIgPSBnZXRTaWduZXIoKTtcbiAgcmV0dXJuIG5ldyBldGhlcnMuQ29udHJhY3QoXG4gICAgcHJvY2Vzcy5lbnYuSURFTlRJVFlfUkVHSVNUUllfQUREUkVTUyEsXG4gICAgSWRlbnRpdHlSZWdpc3RyeUFCSSxcbiAgICBzaWduZXJcbiAgKTtcbn1cblxuZnVuY3Rpb24gZ2V0Q2xhaW1SZWdpc3RyeUNvbnRyYWN0KCkge1xuICBjb25zdCBzaWduZXIgPSBnZXRTaWduZXIoKTtcbiAgcmV0dXJuIG5ldyBldGhlcnMuQ29udHJhY3QoXG4gICAgcHJvY2Vzcy5lbnYuQ0xBSU1fUkVHSVNUUllfQUREUkVTUyEsXG4gICAgQ2xhaW1SZWdpc3RyeUFCSSxcbiAgICBzaWduZXJcbiAgKTtcbn1cblxuZnVuY3Rpb24gZ2V0Q291bnRyeUNvZGUoY291bnRyeTogc3RyaW5nKTogbnVtYmVyIHtcbiAgcmV0dXJuIENPVU5UUllfQ09ERVNbY291bnRyeV0gfHwgQ09VTlRSWV9DT0RFU1tjb3VudHJ5LnRvVXBwZXJDYXNlKCldIHx8IDg0MDsgLy8gRGVmYXVsdCB0byBVU0Fcbn1cblxuLy8gR0VUIC9hcGkvaWRlbnRpdHk/YWRkcmVzcz0weC4uLiAtIEdldCBpZGVudGl0eSBzdGF0dXNcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IHNlYXJjaFBhcmFtcyB9ID0gbmV3IFVSTChyZXF1ZXN0LnVybCk7XG4gICAgY29uc3QgYWRkcmVzcyA9IHNlYXJjaFBhcmFtcy5nZXQoJ2FkZHJlc3MnKTtcblxuICAgIGlmICghYWRkcmVzcykge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnYWRkcmVzcyBwYXJhbWV0ZXIgaXMgcmVxdWlyZWQnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICBjb25zdCBpZGVudGl0eVJlZ2lzdHJ5ID0gZ2V0SWRlbnRpdHlSZWdpc3RyeUNvbnRyYWN0KCk7XG4gICAgY29uc3QgY2xhaW1SZWdpc3RyeSA9IGdldENsYWltUmVnaXN0cnlDb250cmFjdCgpO1xuXG4gICAgLy8gR2V0IGlkZW50aXR5IHN0YXR1cyBmcm9tIGJsb2NrY2hhaW5cbiAgICBjb25zdCBbaXNWZXJpZmllZCwgaXNXaGl0ZWxpc3RlZCwgaXNLeWNBcHByb3ZlZCwgY291bnRyeSwgaXNGcm96ZW5dID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xuICAgICAgaWRlbnRpdHlSZWdpc3RyeS5pc1ZlcmlmaWVkKGFkZHJlc3MpLFxuICAgICAgaWRlbnRpdHlSZWdpc3RyeS5pc1doaXRlbGlzdGVkKGFkZHJlc3MpLFxuICAgICAgaWRlbnRpdHlSZWdpc3RyeS5pc0t5Y0FwcHJvdmVkKGFkZHJlc3MpLFxuICAgICAgaWRlbnRpdHlSZWdpc3RyeS5pbnZlc3RvckNvdW50cnkoYWRkcmVzcyksXG4gICAgICBpZGVudGl0eVJlZ2lzdHJ5LmlzRnJvemVuKGFkZHJlc3MpXG4gICAgXSk7XG5cbiAgICAvLyBHZXQgY2xhaW1zXG4gICAgbGV0IGNsYWltcyA9IFtdO1xuICAgIHRyeSB7XG4gICAgICBjbGFpbXMgPSBhd2FpdCBjbGFpbVJlZ2lzdHJ5LmdldENsYWltc0J5QWRkcmVzcyhhZGRyZXNzKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS53YXJuKCdDb3VsZCBub3QgZmV0Y2ggY2xhaW1zOicsIGVycm9yKTtcbiAgICB9XG5cbiAgICAvLyBHZXQgY2xpZW50IGRhdGEgZnJvbSBkYXRhYmFzZVxuICAgIGNvbnN0IGNsaWVudCA9IGF3YWl0IHByaXNtYS5jbGllbnQuZmluZEZpcnN0KHtcbiAgICAgIHdoZXJlOiB7XG4gICAgICAgIHdhbGxldEFkZHJlc3M6IHtcbiAgICAgICAgICBlcXVhbHM6IGFkZHJlc3MsXG4gICAgICAgICAgbW9kZTogJ2luc2Vuc2l0aXZlJ1xuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgc2VsZWN0OiB7XG4gICAgICAgIGlkOiB0cnVlLFxuICAgICAgICBmaXJzdE5hbWU6IHRydWUsXG4gICAgICAgIGxhc3ROYW1lOiB0cnVlLFxuICAgICAgICBlbWFpbDogdHJ1ZSxcbiAgICAgICAgbmF0aW9uYWxpdHk6IHRydWUsXG4gICAgICAgIGt5Y1N0YXR1czogdHJ1ZSxcbiAgICAgICAgaXNXaGl0ZWxpc3RlZDogdHJ1ZVxuICAgICAgfVxuICAgIH0pO1xuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIGFkZHJlc3MsXG4gICAgICBibG9ja2NoYWluOiB7XG4gICAgICAgIGlzVmVyaWZpZWQsXG4gICAgICAgIGlzV2hpdGVsaXN0ZWQsXG4gICAgICAgIGlzS3ljQXBwcm92ZWQsXG4gICAgICAgIGNvdW50cnk6IGNvdW50cnkudG9TdHJpbmcoKSxcbiAgICAgICAgaXNGcm96ZW4sXG4gICAgICAgIGNsYWltczogY2xhaW1zLmxlbmd0aFxuICAgICAgfSxcbiAgICAgIGRhdGFiYXNlOiB7XG4gICAgICAgIGV4aXN0czogISFjbGllbnQsXG4gICAgICAgIGt5Y1N0YXR1czogY2xpZW50Py5reWNTdGF0dXMgfHwgJ1BFTkRJTkcnLFxuICAgICAgICBpc1doaXRlbGlzdGVkOiBjbGllbnQ/LmlzV2hpdGVsaXN0ZWQgfHwgZmFsc2UsXG4gICAgICAgIG5hdGlvbmFsaXR5OiBjbGllbnQ/Lm5hdGlvbmFsaXR5XG4gICAgICB9LFxuICAgICAgY2xpZW50OiBjbGllbnQgfHwgbnVsbFxuICAgIH0pO1xuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgaWRlbnRpdHkgc3RhdHVzOicsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IGVycm9yOiAnRmFpbGVkIHRvIGZldGNoIGlkZW50aXR5IHN0YXR1cycgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn1cblxuLy8gUE9TVCAvYXBpL2lkZW50aXR5IC0gUmVnaXN0ZXIgaWRlbnRpdHkgb3IgdXBkYXRlIHN0YXR1c1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBib2R5ID0gYXdhaXQgcmVxdWVzdC5qc29uKCk7XG4gICAgY29uc3QgeyBhY3Rpb24sIGFkZHJlc3MsIGNvdW50cnksIGNsaWVudElkIH0gPSBib2R5O1xuXG4gICAgaWYgKCFhY3Rpb24gfHwgIWFkZHJlc3MpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogJ2FjdGlvbiBhbmQgYWRkcmVzcyBhcmUgcmVxdWlyZWQnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICBjb25zdCBpZGVudGl0eVJlZ2lzdHJ5ID0gZ2V0SWRlbnRpdHlSZWdpc3RyeUNvbnRyYWN0KCk7XG4gICAgY29uc3QgY2xhaW1SZWdpc3RyeSA9IGdldENsYWltUmVnaXN0cnlDb250cmFjdCgpO1xuICAgIGxldCB0eEhhc2ggPSAnJztcblxuICAgIHN3aXRjaCAoYWN0aW9uKSB7XG4gICAgICBjYXNlICdyZWdpc3Rlcic6XG4gICAgICAgIHtcbiAgICAgICAgICBjb25zdCBjb3VudHJ5Q29kZSA9IGNvdW50cnkgPyBnZXRDb3VudHJ5Q29kZShjb3VudHJ5KSA6IDg0MDsgLy8gRGVmYXVsdCB0byBVU0FcbiAgICAgICAgICBjb25zdCB0eCA9IGF3YWl0IGlkZW50aXR5UmVnaXN0cnkucmVnaXN0ZXJJZGVudGl0eShhZGRyZXNzLCBjb3VudHJ5Q29kZSk7XG4gICAgICAgICAgYXdhaXQgdHgud2FpdCgpO1xuICAgICAgICAgIHR4SGFzaCA9IHR4Lmhhc2g7XG4gICAgICAgICAgY29uc29sZS5sb2coYOKchSBJZGVudGl0eSByZWdpc3RlcmVkOiAke2FkZHJlc3N9IChjb3VudHJ5OiAke2NvdW50cnlDb2RlfSlgKTtcbiAgICAgICAgfVxuICAgICAgICBicmVhaztcblxuICAgICAgY2FzZSAnd2hpdGVsaXN0JzpcbiAgICAgICAge1xuICAgICAgICAgIGNvbnN0IHR4ID0gYXdhaXQgaWRlbnRpdHlSZWdpc3RyeS5hZGRUb1doaXRlbGlzdChhZGRyZXNzKTtcbiAgICAgICAgICBhd2FpdCB0eC53YWl0KCk7XG4gICAgICAgICAgdHhIYXNoID0gdHguaGFzaDtcbiAgICAgICAgICBjb25zb2xlLmxvZyhg4pyFIEFkZGVkIHRvIHdoaXRlbGlzdDogJHthZGRyZXNzfWApO1xuXG4gICAgICAgICAgLy8gVXBkYXRlIGRhdGFiYXNlXG4gICAgICAgICAgaWYgKGNsaWVudElkKSB7XG4gICAgICAgICAgICBhd2FpdCBwcmlzbWEuY2xpZW50LnVwZGF0ZSh7XG4gICAgICAgICAgICAgIHdoZXJlOiB7IGlkOiBjbGllbnRJZCB9LFxuICAgICAgICAgICAgICBkYXRhOiB7IGlzV2hpdGVsaXN0ZWQ6IHRydWUgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGJyZWFrO1xuXG4gICAgICBjYXNlICd1bndoaXRlbGlzdCc6XG4gICAgICAgIHtcbiAgICAgICAgICBjb25zdCB0eCA9IGF3YWl0IGlkZW50aXR5UmVnaXN0cnkucmVtb3ZlRnJvbVdoaXRlbGlzdChhZGRyZXNzKTtcbiAgICAgICAgICBhd2FpdCB0eC53YWl0KCk7XG4gICAgICAgICAgdHhIYXNoID0gdHguaGFzaDtcbiAgICAgICAgICBjb25zb2xlLmxvZyhg4pyFIFJlbW92ZWQgZnJvbSB3aGl0ZWxpc3Q6ICR7YWRkcmVzc31gKTtcblxuICAgICAgICAgIC8vIFVwZGF0ZSBkYXRhYmFzZVxuICAgICAgICAgIGlmIChjbGllbnRJZCkge1xuICAgICAgICAgICAgYXdhaXQgcHJpc21hLmNsaWVudC51cGRhdGUoe1xuICAgICAgICAgICAgICB3aGVyZTogeyBpZDogY2xpZW50SWQgfSxcbiAgICAgICAgICAgICAgZGF0YTogeyBpc1doaXRlbGlzdGVkOiBmYWxzZSB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgYnJlYWs7XG5cbiAgICAgIGNhc2UgJ2FwcHJvdmVfa3ljJzpcbiAgICAgICAge1xuICAgICAgICAgIGNvbnN0IHR4ID0gYXdhaXQgaWRlbnRpdHlSZWdpc3RyeS5hcHByb3ZlS3ljKGFkZHJlc3MpO1xuICAgICAgICAgIGF3YWl0IHR4LndhaXQoKTtcbiAgICAgICAgICB0eEhhc2ggPSB0eC5oYXNoO1xuICAgICAgICAgIGNvbnNvbGUubG9nKGDinIUgS1lDIGFwcHJvdmVkOiAke2FkZHJlc3N9YCk7XG5cbiAgICAgICAgICAvLyBJc3N1ZSBLWUMgY2xhaW1cbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3QgY2xhaW1EYXRhID0gZXRoZXJzLkFiaUNvZGVyLmRlZmF1bHRBYmlDb2RlcigpLmVuY29kZShcbiAgICAgICAgICAgICAgW1wic3RyaW5nXCIsIFwidWludDI1NlwiXSxcbiAgICAgICAgICAgICAgW1wiS1lDX0FQUFJPVkVEXCIsIE1hdGguZmxvb3IoRGF0ZS5ub3coKSAvIDEwMDApXVxuICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIGNvbnN0IGNsYWltVHggPSBhd2FpdCBjbGFpbVJlZ2lzdHJ5Lmlzc3VlQ2xhaW0oXG4gICAgICAgICAgICAgIGFkZHJlc3MsXG4gICAgICAgICAgICAgIENMQUlNX1RPUElDUy5LWUMsXG4gICAgICAgICAgICAgIFwiMHhcIiwgLy8gZW1wdHkgc2lnbmF0dXJlXG4gICAgICAgICAgICAgIGNsYWltRGF0YSxcbiAgICAgICAgICAgICAgXCJcIiwgLy8gZW1wdHkgVVJJXG4gICAgICAgICAgICAgIDAgLy8gbmV2ZXIgZXhwaXJlc1xuICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIGF3YWl0IGNsYWltVHgud2FpdCgpO1xuICAgICAgICAgICAgY29uc29sZS5sb2coYOKchSBLWUMgY2xhaW0gaXNzdWVkOiAke2FkZHJlc3N9YCk7XG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUud2FybignQ291bGQgbm90IGlzc3VlIEtZQyBjbGFpbTonLCBlcnJvcik7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gVXBkYXRlIGRhdGFiYXNlXG4gICAgICAgICAgaWYgKGNsaWVudElkKSB7XG4gICAgICAgICAgICBhd2FpdCBwcmlzbWEuY2xpZW50LnVwZGF0ZSh7XG4gICAgICAgICAgICAgIHdoZXJlOiB7IGlkOiBjbGllbnRJZCB9LFxuICAgICAgICAgICAgICBkYXRhOiB7IGt5Y1N0YXR1czogJ0FQUFJPVkVEJyB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgYnJlYWs7XG5cbiAgICAgIGNhc2UgJ3Jldm9rZV9reWMnOlxuICAgICAgICB7XG4gICAgICAgICAgY29uc3QgdHggPSBhd2FpdCBpZGVudGl0eVJlZ2lzdHJ5LnJldm9rZUt5YyhhZGRyZXNzKTtcbiAgICAgICAgICBhd2FpdCB0eC53YWl0KCk7XG4gICAgICAgICAgdHhIYXNoID0gdHguaGFzaDtcbiAgICAgICAgICBjb25zb2xlLmxvZyhg4pyFIEtZQyByZXZva2VkOiAke2FkZHJlc3N9YCk7XG5cbiAgICAgICAgICAvLyBSZXZva2UgS1lDIGNsYWltXG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IGNsYWltVHggPSBhd2FpdCBjbGFpbVJlZ2lzdHJ5LnJldm9rZUNsYWltKGFkZHJlc3MsIENMQUlNX1RPUElDUy5LWUMpO1xuICAgICAgICAgICAgYXdhaXQgY2xhaW1UeC53YWl0KCk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhg4pyFIEtZQyBjbGFpbSByZXZva2VkOiAke2FkZHJlc3N9YCk7XG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUud2FybignQ291bGQgbm90IHJldm9rZSBLWUMgY2xhaW06JywgZXJyb3IpO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIC8vIFVwZGF0ZSBkYXRhYmFzZVxuICAgICAgICAgIGlmIChjbGllbnRJZCkge1xuICAgICAgICAgICAgYXdhaXQgcHJpc21hLmNsaWVudC51cGRhdGUoe1xuICAgICAgICAgICAgICB3aGVyZTogeyBpZDogY2xpZW50SWQgfSxcbiAgICAgICAgICAgICAgZGF0YTogeyBreWNTdGF0dXM6ICdSRUpFQ1RFRCcgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGJyZWFrO1xuXG4gICAgICBjYXNlICdmcmVlemUnOlxuICAgICAgICB7XG4gICAgICAgICAgY29uc3QgdHggPSBhd2FpdCBpZGVudGl0eVJlZ2lzdHJ5LmZyZWV6ZUFkZHJlc3MoYWRkcmVzcyk7XG4gICAgICAgICAgYXdhaXQgdHgud2FpdCgpO1xuICAgICAgICAgIHR4SGFzaCA9IHR4Lmhhc2g7XG4gICAgICAgICAgY29uc29sZS5sb2coYOKchSBBZGRyZXNzIGZyb3plbjogJHthZGRyZXNzfWApO1xuICAgICAgICB9XG4gICAgICAgIGJyZWFrO1xuXG4gICAgICBjYXNlICd1bmZyZWV6ZSc6XG4gICAgICAgIHtcbiAgICAgICAgICBjb25zdCB0eCA9IGF3YWl0IGlkZW50aXR5UmVnaXN0cnkudW5mcmVlemVBZGRyZXNzKGFkZHJlc3MpO1xuICAgICAgICAgIGF3YWl0IHR4LndhaXQoKTtcbiAgICAgICAgICB0eEhhc2ggPSB0eC5oYXNoO1xuICAgICAgICAgIGNvbnNvbGUubG9nKGDinIUgQWRkcmVzcyB1bmZyb3plbjogJHthZGRyZXNzfWApO1xuICAgICAgICB9XG4gICAgICAgIGJyZWFrO1xuXG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgICAgeyBlcnJvcjogJ0ludmFsaWQgYWN0aW9uJyB9LFxuICAgICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgICApO1xuICAgIH1cblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgYWN0aW9uLFxuICAgICAgYWRkcmVzcyxcbiAgICAgIHR4SGFzaCxcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgfSk7XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBwcm9jZXNzaW5nIGlkZW50aXR5IGFjdGlvbjonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBlcnJvcjogYEZhaWxlZCB0byAke2JvZHkuYWN0aW9ufTogJHtlcnJvci5tZXNzYWdlfWAgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJldGhlcnMiLCJwcmlzbWEiLCJJZGVudGl0eVJlZ2lzdHJ5QUJJIiwiQ2xhaW1SZWdpc3RyeUFCSSIsIkNPVU5UUllfQ09ERVMiLCJDTEFJTV9UT1BJQ1MiLCJLWUMiLCJRVUFMSUZJQ0FUSU9OIiwiZ2V0UHJvdmlkZXIiLCJKc29uUnBjUHJvdmlkZXIiLCJwcm9jZXNzIiwiZW52IiwiQU1PWV9SUENfVVJMIiwiZ2V0U2lnbmVyIiwicHJvdmlkZXIiLCJXYWxsZXQiLCJDT05UUkFDVF9BRE1JTl9QUklWQVRFX0tFWSIsImdldElkZW50aXR5UmVnaXN0cnlDb250cmFjdCIsInNpZ25lciIsIkNvbnRyYWN0IiwiSURFTlRJVFlfUkVHSVNUUllfQUREUkVTUyIsImdldENsYWltUmVnaXN0cnlDb250cmFjdCIsIkNMQUlNX1JFR0lTVFJZX0FERFJFU1MiLCJnZXRDb3VudHJ5Q29kZSIsImNvdW50cnkiLCJ0b1VwcGVyQ2FzZSIsIkdFVCIsInJlcXVlc3QiLCJzZWFyY2hQYXJhbXMiLCJVUkwiLCJ1cmwiLCJhZGRyZXNzIiwiZ2V0IiwianNvbiIsImVycm9yIiwic3RhdHVzIiwiaWRlbnRpdHlSZWdpc3RyeSIsImNsYWltUmVnaXN0cnkiLCJpc1ZlcmlmaWVkIiwiaXNXaGl0ZWxpc3RlZCIsImlzS3ljQXBwcm92ZWQiLCJpc0Zyb3plbiIsIlByb21pc2UiLCJhbGwiLCJpbnZlc3RvckNvdW50cnkiLCJjbGFpbXMiLCJnZXRDbGFpbXNCeUFkZHJlc3MiLCJjb25zb2xlIiwid2FybiIsImNsaWVudCIsImZpbmRGaXJzdCIsIndoZXJlIiwid2FsbGV0QWRkcmVzcyIsImVxdWFscyIsIm1vZGUiLCJzZWxlY3QiLCJpZCIsImZpcnN0TmFtZSIsImxhc3ROYW1lIiwiZW1haWwiLCJuYXRpb25hbGl0eSIsImt5Y1N0YXR1cyIsImJsb2NrY2hhaW4iLCJ0b1N0cmluZyIsImxlbmd0aCIsImRhdGFiYXNlIiwiZXhpc3RzIiwiUE9TVCIsImJvZHkiLCJhY3Rpb24iLCJjbGllbnRJZCIsInR4SGFzaCIsImNvdW50cnlDb2RlIiwidHgiLCJyZWdpc3RlcklkZW50aXR5Iiwid2FpdCIsImhhc2giLCJsb2ciLCJhZGRUb1doaXRlbGlzdCIsInVwZGF0ZSIsImRhdGEiLCJyZW1vdmVGcm9tV2hpdGVsaXN0IiwiYXBwcm92ZUt5YyIsImNsYWltRGF0YSIsIkFiaUNvZGVyIiwiZGVmYXVsdEFiaUNvZGVyIiwiZW5jb2RlIiwiTWF0aCIsImZsb29yIiwiRGF0ZSIsIm5vdyIsImNsYWltVHgiLCJpc3N1ZUNsYWltIiwicmV2b2tlS3ljIiwicmV2b2tlQ2xhaW0iLCJmcmVlemVBZGRyZXNzIiwidW5mcmVlemVBZGRyZXNzIiwic3VjY2VzcyIsInRpbWVzdGFtcCIsInRvSVNPU3RyaW5nIiwibWVzc2FnZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/identity/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log:  true ? [\n        'query',\n        'error',\n        'warn'\n    ] : 0\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsQ0FBQztJQUMvREksS0FBS0MsS0FBc0MsR0FBRztRQUFDO1FBQVM7UUFBUztLQUFPLEdBQUcsQ0FBUztBQUN0RixHQUFHO0FBRUgsSUFBSUEsSUFBcUMsRUFBRUosZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcclxuXHJcbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XHJcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KHtcclxuICBsb2c6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnID8gWydxdWVyeScsICdlcnJvcicsICd3YXJuJ10gOiBbJ2Vycm9yJ10sXHJcbn0pO1xyXG5cclxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy","vendor-chunks/aes-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fidentity%2Froute&page=%2Fapi%2Fidentity%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fidentity%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();