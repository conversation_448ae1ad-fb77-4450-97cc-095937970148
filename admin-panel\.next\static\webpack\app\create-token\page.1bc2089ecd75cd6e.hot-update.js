"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-token/page",{

/***/ "(app-pages-browser)/./src/app/create-token/hooks/useERC3643Integration.ts":
/*!*************************************************************!*\
  !*** ./src/app/create-token/hooks/useERC3643Integration.ts ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useERC3643Integration: () => (/* binding */ useERC3643Integration)\n/* harmony export */ });\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/abi/abi-coder.js\");\n\n// Contract ABIs for ERC-3643 integration\nconst IdentityRegistryABI = [\n    \"function registerIdentity(address investor, uint16 country) external\",\n    \"function addToWhitelist(address investor) external\",\n    \"function approveKyc(address investor) external\",\n    \"function isVerified(address investor) external view returns (bool)\",\n    \"function isWhitelisted(address investor) external view returns (bool)\",\n    \"function isKycApproved(address investor) external view returns (bool)\"\n];\nconst ClaimRegistryABI = [\n    \"function issueClaim(address subject, uint256 topic, bytes calldata signature, bytes calldata data, string calldata uri, uint256 validUntil) external\",\n    \"function hasValidClaim(address subject, uint256 topic) external view returns (bool)\"\n];\nconst ComplianceABI = [\n    \"function created(address to, uint256 value) external\",\n    \"function canTransfer(address from, address to, uint256 value) external view returns (bool)\"\n];\n// Tokeny Claim Topics (following Tokeny standard)\nconst CLAIM_TOPICS = {\n    KYC: 1,\n    AML: 2,\n    IDENTITY: 3,\n    QUALIFICATION: 4,\n    ACCREDITATION: 5,\n    RESIDENCE: 6,\n    TOKEN_ISSUER: 7 // Custom claim for token issuers\n};\n// Tokeny-style claim data format\n// Format: YYYYMMDDHHMMSS (timestamp) + country code + additional data\n// Example: 10101010000648 = timestamp + country + verification level\nfunction generateTokenyClaim(country, claimType) {\n    const now = new Date();\n    const timestamp = now.getFullYear().toString().slice(-2) + // YY\n    (now.getMonth() + 1).toString().padStart(2, '0') + // MM\n    now.getDate().toString().padStart(2, '0') + // DD\n    now.getHours().toString().padStart(2, '0') + // HH\n    now.getMinutes().toString().padStart(2, '0') + // MM\n    now.getSeconds().toString().padStart(2, '0'); // SS\n    const countryCode = getCountryCode(country).toString().padStart(3, '0');\n    // Additional data based on claim type\n    let additionalData = '';\n    switch(claimType){\n        case 'KYC':\n            additionalData = '001'; // KYC level 1\n            break;\n        case 'QUALIFICATION':\n            additionalData = '002'; // Qualified investor\n            break;\n        case 'TOKEN_ISSUER':\n            additionalData = '003'; // Token issuer\n            break;\n        default:\n            additionalData = '000';\n    }\n    return timestamp + countryCode + additionalData;\n}\n// Country code mapping (ISO-3166 numeric)\nconst COUNTRY_CODES = {\n    'US': 840,\n    'USA': 840,\n    'United States': 840,\n    'CA': 124,\n    'Canada': 124,\n    'GB': 826,\n    'UK': 826,\n    'United Kingdom': 826,\n    'DE': 276,\n    'Germany': 276,\n    'FR': 250,\n    'France': 250,\n    'IT': 380,\n    'Italy': 380,\n    'ES': 724,\n    'Spain': 724,\n    'NL': 528,\n    'Netherlands': 528,\n    'CH': 756,\n    'Switzerland': 756,\n    'AU': 36,\n    'Australia': 36,\n    'JP': 392,\n    'Japan': 392,\n    'SG': 702,\n    'Singapore': 702\n};\nfunction getCountryCode(country) {\n    return COUNTRY_CODES[country] || COUNTRY_CODES[country.toUpperCase()] || 840; // Default to USA\n}\n// Global cache to prevent duplicate operations\nconst operationCache = new Map();\n/**\n * Hook for ERC-3643 integration during token deployment\n */ function useERC3643Integration() {\n    /**\n   * Setup ERC-3643 compliance for a newly deployed token\n   */ const setupERC3643Compliance = async (tokenAddress, ownerAddress, signer, tokenData)=>{\n        // Create a unique operation key to prevent duplicates\n        const operationKey = \"\".concat(tokenAddress, \"-\").concat(ownerAddress, \"-\").concat(JSON.stringify(tokenData.selectedClaims));\n        // Check if this operation is already in progress\n        if (operationCache.has(operationKey)) {\n            console.log(\"🔄 ERC-3643 compliance setup already in progress, returning cached promise\");\n            return operationCache.get(operationKey);\n        }\n        console.log(\"🏛️ Setting up ERC-3643 compliance for token:\", tokenAddress);\n        const results = {\n            identityRegistered: false,\n            whitelisted: false,\n            kycApproved: false,\n            claimsIssued: [],\n            complianceNotified: false,\n            errors: []\n        };\n        // Create the operation promise\n        const operationPromise = (async ()=>{\n            try {\n                // Get contract addresses from environment\n                const identityRegistryAddress = \"0x129E04323E4c9bFBD097489473d8523E4015Bfc3\";\n                const claimRegistryAddress = \"******************************************\";\n                const complianceAddress = \"******************************************\";\n                if (!identityRegistryAddress || !claimRegistryAddress || !complianceAddress) {\n                    console.warn(\"⚠️ ERC-3643 contract addresses not configured, skipping compliance setup\");\n                    return results;\n                }\n                // Connect to contracts\n                const identityRegistry = new ethers__WEBPACK_IMPORTED_MODULE_0__.Contract(identityRegistryAddress, IdentityRegistryABI, signer);\n                const claimRegistry = new ethers__WEBPACK_IMPORTED_MODULE_0__.Contract(claimRegistryAddress, ClaimRegistryABI, signer);\n                const compliance = new ethers__WEBPACK_IMPORTED_MODULE_0__.Contract(complianceAddress, ComplianceABI, signer);\n                // Pre-check: Get current status to avoid unnecessary transactions\n                console.log(\"🔍 Checking current compliance status...\");\n                const [isVerified, isWhitelisted, isKycApproved] = await Promise.all([\n                    identityRegistry.isVerified(ownerAddress).catch(()=>false),\n                    identityRegistry.isWhitelisted(ownerAddress).catch(()=>false),\n                    identityRegistry.isKycApproved(ownerAddress).catch(()=>false)\n                ]);\n                console.log(\"📊 Current status:\", {\n                    isVerified,\n                    isWhitelisted,\n                    isKycApproved\n                });\n                // Step 1: Register identity if not already registered\n                try {\n                    if (!isVerified) {\n                        console.log(\"📝 Registering identity for token owner...\");\n                        const countryCode = getCountryCode(tokenData.country || 'US');\n                        const tx1 = await identityRegistry.registerIdentity(ownerAddress, countryCode);\n                        await tx1.wait();\n                        results.identityRegistered = true;\n                        console.log(\"✅ Identity registered successfully\");\n                    } else {\n                        console.log(\"✅ Identity already registered\");\n                        results.identityRegistered = true;\n                    }\n                } catch (error) {\n                    console.error(\"❌ Failed to register identity:\", error);\n                    results.errors.push(\"Identity registration failed: \".concat(error.message));\n                }\n                // Step 2: Add to whitelist if not already whitelisted\n                try {\n                    if (!isWhitelisted) {\n                        console.log(\"📋 Adding to whitelist...\");\n                        const tx2 = await identityRegistry.addToWhitelist(ownerAddress);\n                        await tx2.wait();\n                        results.whitelisted = true;\n                        console.log(\"✅ Added to whitelist successfully\");\n                    } else {\n                        console.log(\"✅ Already whitelisted\");\n                        results.whitelisted = true;\n                    }\n                } catch (error) {\n                    console.error(\"❌ Failed to add to whitelist:\", error);\n                    results.errors.push(\"Whitelist addition failed: \".concat(error.message));\n                }\n                // Step 3: Approve KYC if not already approved\n                try {\n                    if (!isKycApproved) {\n                        console.log(\"🔍 Approving KYC...\");\n                        const tx3 = await identityRegistry.approveKyc(ownerAddress);\n                        await tx3.wait();\n                        results.kycApproved = true;\n                        console.log(\"✅ KYC approved successfully\");\n                    } else {\n                        console.log(\"✅ KYC already approved\");\n                        results.kycApproved = true;\n                    }\n                } catch (error) {\n                    console.error(\"❌ Failed to approve KYC:\", error);\n                    results.errors.push(\"KYC approval failed: \".concat(error.message));\n                }\n                // Step 4: Issue selected Tokeny-style claims for token issuer\n                try {\n                    const selectedClaims = tokenData.selectedClaims || [\n                        'KYC',\n                        'QUALIFICATION',\n                        'TOKEN_ISSUER'\n                    ];\n                    console.log(\"📜 Issuing selected Tokeny-style claims:\", selectedClaims);\n                    // Pre-check existing claims to avoid duplicates\n                    const existingClaims = new Map();\n                    for (const claimType of selectedClaims){\n                        const claimTopic = CLAIM_TOPICS[claimType];\n                        if (claimTopic) {\n                            try {\n                                const hasExistingClaim = await claimRegistry.hasValidClaim(ownerAddress, claimTopic);\n                                existingClaims.set(claimType, hasExistingClaim);\n                            } catch (error) {\n                                existingClaims.set(claimType, false);\n                            }\n                        }\n                    }\n                    console.log(\"📊 Existing claims status:\", Object.fromEntries(existingClaims));\n                    // Issue claims based on user selection\n                    for (const claimType of selectedClaims){\n                        try {\n                            const claimValue = generateTokenyClaim(tokenData.country || 'US', claimType);\n                            const claimTopic = CLAIM_TOPICS[claimType];\n                            if (!claimTopic) {\n                                console.warn(\"⚠️ Unknown claim type: \".concat(claimType));\n                                continue;\n                            }\n                            console.log(\"\\uD83D\\uDD22 Generated \".concat(claimType, \" claim: \").concat(claimValue));\n                            // Check if claim already exists (using pre-checked values)\n                            const hasExistingClaim = existingClaims.get(claimType) || false;\n                            if (hasExistingClaim) {\n                                console.log(\"✅ \".concat(claimType, \" claim already exists\"));\n                                continue;\n                            }\n                            // Prepare claim data based on type\n                            let claimData;\n                            let claimUri;\n                            switch(claimType){\n                                case 'KYC':\n                                    claimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                                        \"string\",\n                                        \"string\",\n                                        \"uint256\"\n                                    ], [\n                                        claimValue,\n                                        \"KYC_APPROVED\",\n                                        Math.floor(Date.now() / 1000)\n                                    ]);\n                                    claimUri = \"KYC:\".concat(claimValue);\n                                    break;\n                                case 'AML':\n                                    claimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                                        \"string\",\n                                        \"string\",\n                                        \"uint256\"\n                                    ], [\n                                        claimValue,\n                                        \"AML_VERIFIED\",\n                                        Math.floor(Date.now() / 1000)\n                                    ]);\n                                    claimUri = \"AML:\".concat(claimValue);\n                                    break;\n                                case 'IDENTITY':\n                                    claimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                                        \"string\",\n                                        \"string\",\n                                        \"uint256\"\n                                    ], [\n                                        claimValue,\n                                        \"IDENTITY_VERIFIED\",\n                                        Math.floor(Date.now() / 1000)\n                                    ]);\n                                    claimUri = \"IDENTITY:\".concat(claimValue);\n                                    break;\n                                case 'QUALIFICATION':\n                                    claimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                                        \"string\",\n                                        \"string\",\n                                        \"uint256\"\n                                    ], [\n                                        claimValue,\n                                        \"QUALIFIED_INVESTOR\",\n                                        Math.floor(Date.now() / 1000)\n                                    ]);\n                                    claimUri = \"QUALIFICATION:\".concat(claimValue);\n                                    break;\n                                case 'ACCREDITATION':\n                                    claimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                                        \"string\",\n                                        \"string\",\n                                        \"uint256\"\n                                    ], [\n                                        claimValue,\n                                        \"ACCREDITED_INVESTOR\",\n                                        Math.floor(Date.now() / 1000)\n                                    ]);\n                                    claimUri = \"ACCREDITATION:\".concat(claimValue);\n                                    break;\n                                case 'RESIDENCE':\n                                    claimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                                        \"string\",\n                                        \"string\",\n                                        \"uint256\"\n                                    ], [\n                                        claimValue,\n                                        \"RESIDENCE_VERIFIED\",\n                                        Math.floor(Date.now() / 1000)\n                                    ]);\n                                    claimUri = \"RESIDENCE:\".concat(claimValue);\n                                    break;\n                                case 'TOKEN_ISSUER':\n                                    claimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                                        \"string\",\n                                        \"string\",\n                                        \"string\",\n                                        \"string\",\n                                        \"uint256\"\n                                    ], [\n                                        claimValue,\n                                        tokenData.name,\n                                        tokenData.symbol,\n                                        tokenData.tokenType,\n                                        Math.floor(Date.now() / 1000)\n                                    ]);\n                                    claimUri = \"TOKEN_ISSUER:\".concat(claimValue, \":\").concat(tokenData.symbol);\n                                    break;\n                                default:\n                                    console.warn(\"⚠️ Unsupported claim type: \".concat(claimType));\n                                    continue;\n                            }\n                            // Issue the claim\n                            const tx = await claimRegistry.issueClaim(ownerAddress, claimTopic, \"0x\", claimData, claimUri, 0 // never expires\n                            );\n                            await tx.wait();\n                            results.claimsIssued.push(\"\".concat(claimType, \":\").concat(claimValue));\n                            console.log(\"✅ \".concat(claimType, \" claim issued: \").concat(claimValue));\n                        } catch (claimError) {\n                            console.error(\"❌ Failed to issue \".concat(claimType, \" claim:\"), claimError);\n                            results.errors.push(\"\".concat(claimType, \" claim issuance failed: \").concat(claimError.message));\n                        }\n                    }\n                    console.log(\"\\uD83C\\uDF89 Claims issuance completed! Issued \".concat(results.claimsIssued.length, \" claims\"));\n                } catch (error) {\n                    console.error(\"❌ Failed to issue claims:\", error);\n                    results.errors.push(\"Claims issuance failed: \".concat(error.message));\n                }\n                // Step 5: Notify compliance contract (if needed)\n                try {\n                    console.log(\"⚖️ Notifying compliance contract...\");\n                    // This would typically be called when tokens are minted, but we can prepare it\n                    const canTransfer = await compliance.canTransfer(ownerAddress, ownerAddress, 1);\n                    console.log(\"✅ Compliance check passed:\", canTransfer);\n                    results.complianceNotified = true;\n                } catch (error) {\n                    console.error(\"❌ Compliance notification failed:\", error);\n                    results.errors.push(\"Compliance notification failed: \".concat(error.message));\n                }\n                console.log(\"🎉 ERC-3643 compliance setup completed!\");\n                console.log(\"Results:\", {\n                    identityRegistered: results.identityRegistered,\n                    whitelisted: results.whitelisted,\n                    kycApproved: results.kycApproved,\n                    claimsIssued: results.claimsIssued,\n                    complianceNotified: results.complianceNotified,\n                    errorCount: results.errors.length\n                });\n            } catch (error) {\n                console.error(\"❌ ERC-3643 setup failed:\", error);\n                results.errors.push(\"General setup failed: \".concat(error.message));\n            } finally{\n                // Clean up cache entry\n                operationCache.delete(operationKey);\n            }\n            return results;\n        })();\n        // Cache the operation promise\n        operationCache.set(operationKey, operationPromise);\n        return operationPromise;\n    };\n    /**\n   * Check if ERC-3643 contracts are available\n   */ const isERC3643Available = ()=>{\n        return !!( true && \"******************************************\");\n    };\n    /**\n   * Get ERC-3643 contract addresses\n   */ const getERC3643Addresses = ()=>{\n        return {\n            identityRegistry: \"0x129E04323E4c9bFBD097489473d8523E4015Bfc3\",\n            claimRegistry: \"******************************************\",\n            compliance: \"******************************************\"\n        };\n    };\n    return {\n        setupERC3643Compliance,\n        isERC3643Available,\n        getERC3643Addresses\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/create-token/hooks/useERC3643Integration.ts\n"));

/***/ })

});