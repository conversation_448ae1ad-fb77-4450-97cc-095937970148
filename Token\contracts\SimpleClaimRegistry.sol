// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/access/AccessControl.sol";

/**
 * @title SimpleClaimRegistry
 * @dev A simplified claim registry with custom claim type management
 */
contract SimpleClaimRegistry is AccessControl {
    bytes32 public constant CLAIM_ISSUER_ROLE = keccak256("CLAIM_ISSUER_ROLE");
    bytes32 public constant CLAIM_VERIFIER_ROLE = keccak256("CLAIM_VERIFIER_ROLE");

    // Custom claim management
    uint256 private _nextClaimTypeId;
    
    struct ClaimType {
        uint256 id;
        string name;
        string description;
        address creator;
        uint256 createdAt;
        bool active;
    }
    
    // Mapping: claimTypeId => ClaimType
    mapping(uint256 => ClaimType) public claimTypes;
    
    // Mapping: creator => claimTypeIds[]
    mapping(address => uint256[]) public creatorClaimTypes;

    // Claim storage
    struct Claim {
        uint256 claimType;
        address issuer;
        address subject;
        bytes signature;
        bytes data;
        string uri;
        uint256 issuedAt;
        uint256 expiresAt;
        bool revoked;
    }

    // Mapping: claimId => Claim
    mapping(bytes32 => Claim) public claims;
    
    // Mapping: subject => claimType => claimIds[]
    mapping(address => mapping(uint256 => bytes32[])) public claimIds;

    // Events
    event ClaimIssued(
        address indexed subject,
        uint256 indexed claimType,
        bytes32 indexed claimId,
        address issuer,
        string uri
    );

    event ClaimRevoked(
        address indexed subject,
        uint256 indexed claimType,
        bytes32 indexed claimId,
        address issuer
    );

    event ClaimTypeCreated(
        uint256 indexed claimTypeId,
        string name,
        string description,
        address indexed creator
    );

    event ClaimTypeUpdated(
        uint256 indexed claimTypeId,
        string name,
        string description,
        bool active
    );

    constructor(address admin) {
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(CLAIM_ISSUER_ROLE, admin);
        _grantRole(CLAIM_VERIFIER_ROLE, admin);
        
        // Initialize claim type counter
        _nextClaimTypeId = 1;
        
        // Create default claim types for backward compatibility
        _createClaimType("KYC Verification", "Basic identity verification through KYC process");
        _createClaimType("Accredited Investor", "Qualified as an accredited investor");
        _createClaimType("Jurisdiction Compliance", "Meets specific jurisdiction requirements");
        _createClaimType("General Qualification", "General investment qualification");
    }

    /**
     * @dev Create a new claim type
     */
    function createClaimType(
        string calldata name,
        string calldata description
    ) external onlyRole(CLAIM_ISSUER_ROLE) returns (uint256) {
        return _createClaimType(name, description);
    }

    /**
     * @dev Internal function to create claim type
     */
    function _createClaimType(
        string memory name,
        string memory description
    ) internal returns (uint256) {
        uint256 claimTypeId = _nextClaimTypeId++;
        
        claimTypes[claimTypeId] = ClaimType({
            id: claimTypeId,
            name: name,
            description: description,
            creator: msg.sender,
            createdAt: block.timestamp,
            active: true
        });
        
        creatorClaimTypes[msg.sender].push(claimTypeId);
        
        emit ClaimTypeCreated(claimTypeId, name, description, msg.sender);
        return claimTypeId;
    }

    /**
     * @dev Update claim type details
     */
    function updateClaimType(
        uint256 claimTypeId,
        string calldata name,
        string calldata description,
        bool active
    ) external {
        ClaimType storage claimType = claimTypes[claimTypeId];
        require(claimType.id != 0, "ClaimRegistry: claim type does not exist");
        require(
            claimType.creator == msg.sender || hasRole(DEFAULT_ADMIN_ROLE, msg.sender),
            "ClaimRegistry: not authorized to update"
        );

        claimType.name = name;
        claimType.description = description;
        claimType.active = active;

        emit ClaimTypeUpdated(claimTypeId, name, description, active);
    }

    /**
     * @dev Issue a claim for a subject
     */
    function issueClaim(
        address subject,
        uint256 claimType,
        bytes calldata signature,
        bytes calldata data,
        string calldata uri,
        uint256 expiresAt
    ) external onlyRole(CLAIM_ISSUER_ROLE) returns (bytes32) {
        require(claimTypes[claimType].id != 0, "ClaimRegistry: invalid claim type");
        require(claimTypes[claimType].active, "ClaimRegistry: claim type is inactive");

        bytes32 claimId = keccak256(
            abi.encodePacked(subject, claimType, msg.sender, block.timestamp, data)
        );

        claims[claimId] = Claim({
            claimType: claimType,
            issuer: msg.sender,
            subject: subject,
            signature: signature,
            data: data,
            uri: uri,
            issuedAt: block.timestamp,
            expiresAt: expiresAt,
            revoked: false
        });

        claimIds[subject][claimType].push(claimId);

        emit ClaimIssued(subject, claimType, claimId, msg.sender, uri);
        return claimId;
    }

    /**
     * @dev Check if a subject has a valid claim of a specific type
     */
    function hasValidClaim(address subject, uint256 claimType) external view returns (bool) {
        bytes32[] memory subjectClaimIds = claimIds[subject][claimType];
        
        for (uint256 i = 0; i < subjectClaimIds.length; i++) {
            Claim memory claim = claims[subjectClaimIds[i]];
            if (!claim.revoked && (claim.expiresAt == 0 || claim.expiresAt > block.timestamp)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * @dev Get all active claim types (paginated)
     */
    function getActiveClaimTypes(uint256 offset, uint256 limit) external view returns (ClaimType[] memory) {
        require(limit > 0 && limit <= 100, "ClaimRegistry: invalid limit");
        
        // Count active claim types
        uint256 activeCount = 0;
        for (uint256 i = 1; i < _nextClaimTypeId; i++) {
            if (claimTypes[i].active) {
                activeCount++;
            }
        }
        
        if (offset >= activeCount) {
            return new ClaimType[](0);
        }
        
        uint256 resultLength = activeCount - offset;
        if (resultLength > limit) {
            resultLength = limit;
        }
        
        ClaimType[] memory result = new ClaimType[](resultLength);
        uint256 resultIndex = 0;
        uint256 currentOffset = 0;
        
        for (uint256 i = 1; i < _nextClaimTypeId && resultIndex < resultLength; i++) {
            if (claimTypes[i].active) {
                if (currentOffset >= offset) {
                    result[resultIndex] = claimTypes[i];
                    resultIndex++;
                }
                currentOffset++;
            }
        }
        
        return result;
    }

    /**
     * @dev Get total number of claim types
     */
    function getTotalClaimTypes() external view returns (uint256) {
        return _nextClaimTypeId - 1;
    }

    /**
     * @dev Check if claim type exists and is active
     */
    function isValidClaimType(uint256 claimTypeId) external view returns (bool) {
        return claimTypes[claimTypeId].id != 0 && claimTypes[claimTypeId].active;
    }

    /**
     * @dev Get all claim types created by an address
     */
    function getClaimTypesByCreator(address creator) external view returns (uint256[] memory) {
        return creatorClaimTypes[creator];
    }

    /**
     * @dev Get all claim IDs for a subject and claim type
     */
    function getClaimIds(address subject, uint256 claimType) external view returns (bytes32[] memory) {
        return claimIds[subject][claimType];
    }
}
