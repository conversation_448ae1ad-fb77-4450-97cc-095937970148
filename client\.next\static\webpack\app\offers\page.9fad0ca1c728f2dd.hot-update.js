"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/offers/page",{

/***/ "(app-pages-browser)/./src/app/offers/page.tsx":
/*!*********************************!*\
  !*** ./src/app/offers/page.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OffersPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @auth0/nextjs-auth0/client */ \"(app-pages-browser)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Navbar */ \"(app-pages-browser)/./src/components/Navbar.tsx\");\n/* harmony import */ var _components_KYCModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/KYCModal */ \"(app-pages-browser)/./src/components/KYCModal.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _components_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/providers/MockAuthProvider */ \"(app-pages-browser)/./src/components/providers/MockAuthProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction OffersPage() {\n    _s();\n    const useMockAuth = \"false\" === 'true';\n    // Use mock auth or real Auth0 based on environment\n    const auth0User = (0,_auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2__.useUser)();\n    const mockAuth = useMockAuth ? (0,_components_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_6__.useMockUser)() : {\n        user: undefined,\n        isLoading: false\n    };\n    const user = useMockAuth ? mockAuth.user : auth0User.user;\n    const userLoading = useMockAuth ? mockAuth.isLoading : auth0User.isLoading;\n    const [tokens, setTokens] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showKYCModal, setShowKYCModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showOrderModal, setShowOrderModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTokenForOrder, setSelectedTokenForOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [orderAmount, setOrderAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSubmittingOrder, setIsSubmittingOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [orderError, setOrderError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const apiClient = (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_5__.useApiClient)();\n    // Fetch client profile\n    const { data: clientProfile } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'client-profile'\n        ],\n        queryFn: {\n            \"OffersPage.useQuery\": ()=>apiClient.getClientProfile()\n        }[\"OffersPage.useQuery\"],\n        enabled: !!user\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OffersPage.useEffect\": ()=>{\n            fetchTokens();\n        }\n    }[\"OffersPage.useEffect\"], [\n        clientProfile === null || clientProfile === void 0 ? void 0 : clientProfile.walletAddress\n    ]); // Refetch when wallet address changes\n    const fetchTokens = async ()=>{\n        try {\n            setLoading(true);\n            // Construct URL with proper query parameters\n            const params = new URLSearchParams();\n            if (clientProfile === null || clientProfile === void 0 ? void 0 : clientProfile.walletAddress) {\n                params.append('testWallet', clientProfile.walletAddress);\n            }\n            params.append('_t', Date.now().toString());\n            const url = \"/api/tokens?\".concat(params.toString());\n            console.log('Fetching tokens from:', url);\n            const response = await fetch(url);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Token fetch error:', response.status, errorText);\n                throw new Error(\"Failed to fetch tokens: \".concat(response.status));\n            }\n            const data = await response.json();\n            console.log('Fetched tokens:', data);\n            setTokens(data);\n        } catch (err) {\n            console.error('Error in fetchTokens:', err);\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const formatPrice = (price, currency)=>{\n        const numPrice = parseFloat(price);\n        // Handle crypto currencies that don't have standard currency codes\n        const cryptoCurrencies = [\n            'ETH',\n            'BTC',\n            'USDC',\n            'USDT',\n            'DAI'\n        ];\n        if (cryptoCurrencies.includes(currency.toUpperCase())) {\n            return \"\".concat(numPrice, \" \").concat(currency.toUpperCase());\n        }\n        // Handle standard fiat currencies\n        const supportedCurrencies = [\n            'USD',\n            'EUR',\n            'GBP',\n            'JPY',\n            'CAD',\n            'AUD'\n        ];\n        const currencyCode = supportedCurrencies.includes(currency.toUpperCase()) ? currency.toUpperCase() : 'USD';\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: currencyCode,\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 2\n        }).format(numPrice);\n    };\n    const formatSupply = function(supply) {\n        let decimals = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        const numSupply = parseFloat(supply);\n        // Handle very large numbers (like 1000000000000000000000000)\n        if (decimals > 0 && numSupply > 1000000000000) {\n            // This is likely already in wei/smallest unit, convert to human readable\n            const humanReadable = numSupply / Math.pow(10, decimals);\n            return new Intl.NumberFormat('en-US', {\n                maximumFractionDigits: 0\n            }).format(humanReadable);\n        }\n        // For normal numbers or 0 decimals, display as-is\n        return new Intl.NumberFormat('en-US', {\n            maximumFractionDigits: 0\n        }).format(numSupply);\n    };\n    const getCategoryColor = (category)=>{\n        switch(category.toLowerCase()){\n            case 'commodity':\n            case 'commodities':\n                return 'bg-amber-100 text-amber-800';\n            case 'real estate':\n            case 'realestate':\n                return 'bg-green-100 text-green-800';\n            case 'equity':\n            case 'equities':\n                return 'bg-blue-100 text-blue-800';\n            case 'debt':\n            case 'bonds':\n                return 'bg-purple-100 text-purple-800';\n            case 'fund':\n            case 'funds':\n                return 'bg-indigo-100 text-indigo-800';\n            case 'security':\n            case 'securities':\n                return 'bg-teal-100 text-teal-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getDefaultImage = (category)=>{\n        switch(category.toLowerCase()){\n            case 'commodity':\n            case 'commodities':\n                return '🏗️';\n            case 'real estate':\n            case 'realestate':\n                return '🏢';\n            case 'equity':\n            case 'equities':\n                return '📈';\n            case 'debt':\n            case 'bonds':\n                return '💰';\n            case 'fund':\n            case 'funds':\n                return '🏦';\n            case 'security':\n            case 'securities':\n                return '🛡️';\n            default:\n                return '🪙';\n        }\n    };\n    const handleOrderSubmit = async ()=>{\n        if (!selectedTokenForOrder || !orderAmount || !(clientProfile === null || clientProfile === void 0 ? void 0 : clientProfile.id)) {\n            setOrderError('Missing token, amount, or client information.');\n            return;\n        }\n        // Debug logging\n        console.log('Order submission debug:', {\n            tokenPrice: selectedTokenForOrder.price,\n            tokenCurrency: selectedTokenForOrder.currency,\n            orderAmount: orderAmount,\n            clientWallet: clientProfile.walletAddress,\n            tokenWhitelisted: selectedTokenForOrder.isWhitelisted\n        });\n        // Validate orderAmount is a positive number\n        const amountNumber = parseFloat(orderAmount);\n        if (isNaN(amountNumber) || amountNumber <= 0) {\n            setOrderError('Please enter a valid positive amount.');\n            return;\n        }\n        // Check if amount exceeds max supply\n        if (amountNumber > Number(selectedTokenForOrder.maxSupply)) {\n            setOrderError(\"Cannot order more than \".concat(selectedTokenForOrder.maxSupply, \" tokens\"));\n            return;\n        }\n        // Check if user is whitelisted for this token\n        console.log('Whitelist check:', {\n            tokenAddress: selectedTokenForOrder.address,\n            isWhitelisted: selectedTokenForOrder.isWhitelisted,\n            clientWallet: clientProfile.walletAddress\n        });\n        if (!selectedTokenForOrder.isWhitelisted) {\n            setOrderError(\"You must be whitelisted for this token before placing an order. Please contact support to get whitelisted for \".concat(selectedTokenForOrder.name, \".\"));\n            return;\n        }\n        setIsSubmittingOrder(true);\n        setOrderError(null);\n        try {\n            const response = await fetch('/api/client-orders', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenId: selectedTokenForOrder.id,\n                    clientId: clientProfile.id,\n                    tokensOrdered: orderAmount\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to submit order');\n            }\n            // Order submitted successfully\n            setShowOrderModal(false);\n            setSelectedTokenForOrder(null);\n            setOrderAmount('');\n            // Show success message with order details\n            const totalAmount = formatPrice((amountNumber * Number(selectedTokenForOrder.price)).toString(), selectedTokenForOrder.currency);\n            alert(\"Order submitted successfully!\\n\\nToken: \".concat(selectedTokenForOrder.name, \"\\nAmount: \").concat(orderAmount, \" tokens\\nTotal: \").concat(totalAmount, \"\\n\\nYou will be notified once it is approved.\"));\n        } catch (err) {\n            console.error('Error submitting order:', err);\n            setOrderError(err.message || 'Failed to submit order. Please try again.');\n        } finally{\n            setIsSubmittingOrder(false);\n        }\n    };\n    if (userLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                lineNumber: 276,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n            lineNumber: 275,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                    children: \"TokenDev Offers\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Please log in to view available token offers\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: useMockAuth ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: mockAuth.login,\n                                disabled: mockAuth.isLoading,\n                                className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50\",\n                                children: mockAuth.isLoading ? 'Signing In...' : 'Sign In (Demo)'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/api/auth/login\",\n                                className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors inline-block text-center\",\n                                children: \"Sign In with Auth0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                lineNumber: 286,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n            lineNumber: 285,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_3__.Navbar, {\n                user: user,\n                clientProfile: clientProfile,\n                onGetQualified: ()=>setShowKYCModal(true),\n                onLogout: useMockAuth ? mockAuth.logout : undefined,\n                useMockAuth: useMockAuth\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                                children: \"Token Investment Opportunities\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Discover and invest in qualified security tokens based on your claims\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500 mb-1\",\n                                                    children: \"Wallet Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"w3m-button\", {\n                                                    size: \"sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, this),\n                            user && clientProfile && tokens.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-blue-900 mb-1\",\n                                                    children: \"Available Investment Opportunities\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-700 text-sm\",\n                                                    children: [\n                                                        \"You have access to \",\n                                                        tokens.length,\n                                                        \" investment \",\n                                                        tokens.length === 1 ? 'opportunity' : 'opportunities'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-blue-900\",\n                                                    children: tokens.length\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-blue-600\",\n                                                    children: \"Available Tokens\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, this),\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-5 w-5 text-red-400\",\n                                        viewBox: \"0 0 20 20\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-red-800\",\n                                            children: \"Error loading tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-700 mt-1\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 11\n                    }, this),\n                    !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: tokens.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: \"\\uD83E\\uDE99\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"No tokens available\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Check back later for new investment opportunities.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: tokens.map((token)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-48 bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n                                            children: token.imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: token.imageUrl,\n                                                alt: token.name,\n                                                className: \"w-24 h-24 object-cover rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 25\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-6xl\",\n                                                children: getDefaultImage(token.category)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                                                    children: token.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500 font-mono\",\n                                                                    children: token.symbol\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col gap-1 items-end\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(getCategoryColor(token.category)),\n                                                                    children: token.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 441,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                token.isQualified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\",\n                                                                    children: \"✅ QUALIFIED\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                token.isWhitelisted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\",\n                                                                    children: \"WHITELISTED\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 450,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: formatPrice(token.price, token.currency)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"per token\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4 mb-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"Total Supply\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 468,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: formatSupply(token.totalSupply, token.decimals)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 469,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"Max Supply\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 474,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: formatSupply(token.maxSupply, token.decimals)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 475,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 23\n                                                }, this),\n                                                token.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mb-4 line-clamp-2\",\n                                                    children: token.description\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 25\n                                                }, this),\n                                                user && clientProfile ? token.isQualified ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        setSelectedTokenForOrder(token);\n                                                        setShowOrderModal(true);\n                                                    },\n                                                    className: \"w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors font-medium\",\n                                                    children: \"\\uD83D\\uDE80 Invest Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 27\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            disabled: true,\n                                                            className: \"w-full bg-gray-400 text-white py-2 px-4 rounded-lg cursor-not-allowed font-medium\",\n                                                            title: \"You need to complete the required qualifications\",\n                                                            children: \"❌ Qualification Required\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600 text-center\",\n                                                            children: \"Complete the required qualifications above to invest\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 27\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        if (!user) {\n                                                            // Redirect to login\n                                                            window.location.href = '/api/auth/login';\n                                                        } else {\n                                                            // Show KYC modal\n                                                            setShowKYCModal(true);\n                                                        }\n                                                    },\n                                                    className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium\",\n                                                    children: !user ? 'Sign In to View Offers' : 'Complete Qualification'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-6 py-3 bg-gray-50 border-t\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center text-xs text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Network: \",\n                                                            token.network\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Decimals: \",\n                                                            token.decimals\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, token.id, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                lineNumber: 331,\n                columnNumber: 7\n            }, this),\n            showKYCModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_KYCModal__WEBPACK_IMPORTED_MODULE_4__.KYCModal, {\n                onClose: ()=>setShowKYCModal(false),\n                existingProfile: clientProfile\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                lineNumber: 551,\n                columnNumber: 9\n            }, this),\n            showOrderModal && selectedTokenForOrder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg leading-6 font-medium text-gray-900\",\n                                        children: [\n                                            \"Order \",\n                                            selectedTokenForOrder.name,\n                                            \" (\",\n                                            selectedTokenForOrder.symbol,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowOrderModal(false);\n                                            setSelectedTokenForOrder(null);\n                                            setOrderAmount('');\n                                            setOrderError(null);\n                                        },\n                                        className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 567,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                lineNumber: 563,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Token Details\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 p-3 rounded-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Token:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            selectedTokenForOrder.name,\n                                                            \" (\",\n                                                            selectedTokenForOrder.symbol,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Price:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                lineNumber: 594,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            formatPrice(selectedTokenForOrder.price, selectedTokenForOrder.currency),\n                                                            \" per token\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Available:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                lineNumber: 597,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            selectedTokenForOrder.maxSupply,\n                                                            \" tokens\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                        lineNumber: 596,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 589,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"order-amount\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Number of Tokens to Order\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"order-amount\",\n                                                type: \"number\",\n                                                min: \"1\",\n                                                step: \"1\",\n                                                value: orderAmount,\n                                                onChange: (e)=>{\n                                                    const value = e.target.value;\n                                                    setOrderAmount(value);\n                                                    setOrderError(null);\n                                                },\n                                                placeholder: \"Enter amount of tokens\",\n                                                className: \"block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 17\n                                    }, this),\n                                    orderAmount && !isNaN(Number(orderAmount)) && Number(orderAmount) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 p-3 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-blue-800\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Total Amount:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \" \",\n                                                    (()=>{\n                                                        const tokenPrice = Number(selectedTokenForOrder.price);\n                                                        const orderQty = Number(orderAmount);\n                                                        console.log('Price calculation debug:', {\n                                                            tokenPrice,\n                                                            orderQty,\n                                                            priceIsNaN: isNaN(tokenPrice),\n                                                            qtyIsNaN: isNaN(orderQty),\n                                                            rawPrice: selectedTokenForOrder.price\n                                                        });\n                                                        if (isNaN(tokenPrice) || isNaN(orderQty)) {\n                                                            return \"Error: Price=\".concat(selectedTokenForOrder.price, \", Qty=\").concat(orderAmount);\n                                                        }\n                                                        const total = orderQty * tokenPrice;\n                                                        return formatPrice(total.toString(), selectedTokenForOrder.currency);\n                                                    })()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-600 mt-1\",\n                                                children: \"This order will be submitted for admin approval\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 19\n                                    }, this),\n                                    orderError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 rounded-md p-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600\",\n                                            children: orderError\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 656,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 655,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 flex flex-col space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleOrderSubmit,\n                                        disabled: isSubmittingOrder || !orderAmount,\n                                        className: \"w-full px-4 py-2 bg-green-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isSubmittingOrder ? 'Submitting...' : 'Submit Order'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowOrderModal(false);\n                                            setSelectedTokenForOrder(null);\n                                            setOrderAmount('');\n                                            setOrderError(null);\n                                        },\n                                        className: \"w-full px-4 py-2 bg-gray-200 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                    lineNumber: 560,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                lineNumber: 559,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-4 right-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"w3m-button\", {}, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                    lineNumber: 689,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                lineNumber: 688,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n        lineNumber: 322,\n        columnNumber: 5\n    }, this);\n}\n_s(OffersPage, \"ApMYDulHuTrKf3ifJxh2CXa6xLY=\", false, function() {\n    return [\n        _auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2__.useUser,\n        _components_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_6__.useMockUser,\n        _lib_api_client__WEBPACK_IMPORTED_MODULE_5__.useApiClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery\n    ];\n});\n_c = OffersPage;\nvar _c;\n$RefreshReg$(_c, \"OffersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/offers/page.tsx\n"));

/***/ })

});