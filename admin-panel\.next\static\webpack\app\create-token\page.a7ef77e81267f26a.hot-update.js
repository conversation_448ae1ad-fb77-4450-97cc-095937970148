"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-token/page",{

/***/ "(app-pages-browser)/./src/app/create-token/components/TokenForm.tsx":
/*!*******************************************************!*\
  !*** ./src/app/create-token/components/TokenForm.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../config */ \"(app-pages-browser)/./src/config.ts\");\n\n\n\n/**\r\n * TokenForm Component\r\n *\r\n * Form for creating a new security token with various configuration options\r\n */ const TokenForm = (param)=>{\n    let { formData, handleInputChange, handleNetworkChange, handleSubmit, isSubmitting, network, getNetworkLabel, deploymentStep, kycSupported } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white shadow-md rounded-lg p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-gray-700 text-sm font-bold mb-2\",\n                            htmlFor: \"network\",\n                            children: \"Network\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            id: \"network\",\n                            name: \"network\",\n                            className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                            value: network,\n                            onChange: handleNetworkChange,\n                            disabled: isSubmitting,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"amoy\",\n                                    children: \"Amoy Testnet\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"polygon\",\n                                    children: \"Polygon Mainnet\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, undefined),\n                formData.enableKYC && !kycSupported && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6 rounded-md\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-5 w-5 text-yellow-400\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    viewBox: \"0 0 20 20\",\n                                    fill: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-yellow-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"KYC Support Not Available:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \" The deployed factory contract doesn't support KYC functionality. Your token will be deployed without KYC support. To enable KYC, the contract factory needs to be updated.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"name\",\n                                    children: \"Token Name*\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"name\",\n                                    name: \"name\",\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    placeholder: \"e.g., Example Security Token\",\n                                    value: formData.name,\n                                    onChange: handleInputChange,\n                                    disabled: isSubmitting,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"symbol\",\n                                    children: \"Token Symbol*\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"symbol\",\n                                    name: \"symbol\",\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    placeholder: \"e.g., EXST\",\n                                    value: formData.symbol,\n                                    onChange: handleInputChange,\n                                    disabled: isSubmitting,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"decimals\",\n                                    children: \"Decimals*\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"decimals\",\n                                    name: \"decimals\",\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    value: formData.decimals,\n                                    onChange: handleInputChange,\n                                    disabled: isSubmitting,\n                                    required: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: 0,\n                                            children: \"0 (No decimals)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: 6,\n                                            children: \"6 decimals\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: 8,\n                                            children: \"8 decimals\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: 18,\n                                            children: \"18 decimals (Standard)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-green-600 mt-1\",\n                                    children: \"✅ Choose the number of decimal places for your token. 0 for whole numbers only, 18 for standard ERC-20 tokens.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"maxSupply\",\n                                    children: \"Maximum Supply*\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"maxSupply\",\n                                    name: \"maxSupply\",\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    placeholder: \"e.g., 1000000\",\n                                    value: formData.maxSupply,\n                                    onChange: handleInputChange,\n                                    disabled: isSubmitting,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"ownerAddress\",\n                                    children: \"Owner Address (Whitelisted)*\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"ownerAddress\",\n                                    name: \"ownerAddress\",\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    placeholder: \"e.g., 0x...\",\n                                    value: formData.ownerAddress,\n                                    onChange: handleInputChange,\n                                    disabled: isSubmitting,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"tokenPrice\",\n                                    children: \"Token Price\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    id: \"tokenPrice\",\n                                    name: \"tokenPrice\",\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    placeholder: \"e.g., 10\",\n                                    value: formData.tokenPrice,\n                                    onChange: handleInputChange,\n                                    disabled: isSubmitting\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"currency\",\n                                    children: \"Currency\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"currency\",\n                                    name: \"currency\",\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    value: formData.currency,\n                                    onChange: handleInputChange,\n                                    disabled: isSubmitting,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"USD\",\n                                            children: \"USD - US Dollar\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"EUR\",\n                                            children: \"EUR - Euro\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"GBP\",\n                                            children: \"GBP - British Pound\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"JPY\",\n                                            children: \"JPY - Japanese Yen\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"CAD\",\n                                            children: \"CAD - Canadian Dollar\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"AUD\",\n                                            children: \"AUD - Australian Dollar\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"CHF\",\n                                            children: \"CHF - Swiss Franc\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"CNY\",\n                                            children: \"CNY - Chinese Yuan\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"BTC\",\n                                            children: \"BTC - Bitcoin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"ETH\",\n                                            children: \"ETH - Ethereum\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"USDC\",\n                                            children: \"USDC - USD Coin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"USDT\",\n                                            children: \"USDT - Tether\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: \"Select the currency for token pricing and fees\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"tokenImageUrl\",\n                                    children: \"Token Logo/Image URL\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"url\",\n                                    id: \"tokenImageUrl\",\n                                    name: \"tokenImageUrl\",\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    placeholder: \"e.g., https://example.com/logo.png\",\n                                    value: formData.tokenImageUrl,\n                                    onChange: handleInputChange,\n                                    disabled: isSubmitting\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: \"Optional: URL to your token's logo or image (PNG, JPG, SVG recommended)\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"tokenType\",\n                                    children: \"Token Type\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"tokenType\",\n                                    name: \"tokenType\",\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    value: formData.tokenType,\n                                    onChange: handleInputChange,\n                                    disabled: isSubmitting,\n                                    children: _config__WEBPACK_IMPORTED_MODULE_2__.tokenTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: type.id,\n                                            children: type.name\n                                        }, type.id, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center h-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center text-gray-700 font-bold\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"enableKYC\",\n                                                name: \"enableKYC\",\n                                                className: \"mr-2 h-4 w-4 text-blue-600\",\n                                                checked: formData.enableKYC,\n                                                onChange: handleInputChange,\n                                                disabled: isSubmitting || !kycSupported\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Enable KYC\",\n                                            !kycSupported && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-1 text-xs bg-gray-200 text-gray-600 px-1 py-0.5 rounded\",\n                                                children: \"Not Available\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 ml-6\",\n                                        children: [\n                                            \"Requires KYC approval for token transfers\",\n                                            !kycSupported && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-red-500\",\n                                                children: \"KYC support requires an updated contract factory\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 p-4 border border-blue-200 rounded-lg bg-blue-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-bold text-gray-800 mb-3 flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 mr-2 text-blue-600\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"ERC-3643 Claims to Issue\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 mb-4\",\n                            children: [\n                                \"Select which Tokeny-style claims to automatically issue for the token owner during deployment. Claims will be generated in format: \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    className: \"bg-gray-200 px-1 rounded\",\n                                    children: \"YYMMDDHHMMSS + CountryCode + ClaimType\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 49\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    htmlFor: \"issuerCountry\",\n                                    children: \"Issuer Country (for claims)\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"issuerCountry\",\n                                    name: \"issuerCountry\",\n                                    className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                                    value: formData.issuerCountry,\n                                    onChange: handleInputChange,\n                                    disabled: isSubmitting,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"US\",\n                                            children: \"\\uD83C\\uDDFA\\uD83C\\uDDF8 United States (840)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"CA\",\n                                            children: \"\\uD83C\\uDDE8\\uD83C\\uDDE6 Canada (124)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"GB\",\n                                            children: \"\\uD83C\\uDDEC\\uD83C\\uDDE7 United Kingdom (826)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"DE\",\n                                            children: \"\\uD83C\\uDDE9\\uD83C\\uDDEA Germany (276)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"FR\",\n                                            children: \"\\uD83C\\uDDEB\\uD83C\\uDDF7 France (250)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"IT\",\n                                            children: \"\\uD83C\\uDDEE\\uD83C\\uDDF9 Italy (380)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"ES\",\n                                            children: \"\\uD83C\\uDDEA\\uD83C\\uDDF8 Spain (724)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"NL\",\n                                            children: \"\\uD83C\\uDDF3\\uD83C\\uDDF1 Netherlands (528)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"CH\",\n                                            children: \"\\uD83C\\uDDE8\\uD83C\\uDDED Switzerland (756)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"AU\",\n                                            children: \"\\uD83C\\uDDE6\\uD83C\\uDDFA Australia (36)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"JP\",\n                                            children: \"\\uD83C\\uDDEF\\uD83C\\uDDF5 Japan (392)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"SG\",\n                                            children: \"\\uD83C\\uDDF8\\uD83C\\uDDEC Singapore (702)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                            children: [\n                                {\n                                    id: 'KYC',\n                                    name: 'KYC Verification',\n                                    description: 'Know Your Customer compliance claim',\n                                    code: '001'\n                                },\n                                {\n                                    id: 'AML',\n                                    name: 'AML Compliance',\n                                    description: 'Anti-Money Laundering verification',\n                                    code: '002'\n                                },\n                                {\n                                    id: 'IDENTITY',\n                                    name: 'Identity Verification',\n                                    description: 'Identity document verification',\n                                    code: '003'\n                                },\n                                {\n                                    id: 'QUALIFICATION',\n                                    name: 'Qualified Investor',\n                                    description: 'Accredited/qualified investor status',\n                                    code: '004'\n                                },\n                                {\n                                    id: 'ACCREDITATION',\n                                    name: 'Accreditation',\n                                    description: 'Professional accreditation claim',\n                                    code: '005'\n                                },\n                                {\n                                    id: 'RESIDENCE',\n                                    name: 'Residence Proof',\n                                    description: 'Proof of residence verification',\n                                    code: '006'\n                                },\n                                {\n                                    id: 'TOKEN_ISSUER',\n                                    name: 'Token Issuer',\n                                    description: 'Authorized token issuer claim',\n                                    code: '007'\n                                }\n                            ].map((claim)=>{\n                                var _formData_selectedClaims;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3 p-3 border border-gray-200 rounded-lg bg-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"claim-\".concat(claim.id),\n                                            className: \"mt-1 h-4 w-4 text-blue-600 rounded\",\n                                            checked: ((_formData_selectedClaims = formData.selectedClaims) === null || _formData_selectedClaims === void 0 ? void 0 : _formData_selectedClaims.includes(claim.id)) || false,\n                                            onChange: (e)=>{\n                                                const currentClaims = formData.selectedClaims || [];\n                                                const newClaims = e.target.checked ? [\n                                                    ...currentClaims,\n                                                    claim.id\n                                                ] : currentClaims.filter((c)=>c !== claim.id);\n                                                handleInputChange({\n                                                    target: {\n                                                        name: 'selectedClaims',\n                                                        value: newClaims\n                                                    }\n                                                });\n                                            },\n                                            disabled: isSubmitting\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"claim-\".concat(claim.id),\n                                                    className: \"text-sm font-medium text-gray-900 cursor-pointer\",\n                                                    children: [\n                                                        claim.name,\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"(Topic \",\n                                                                claim.code,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 34\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: claim.description\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, claim.id, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 15\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-yellow-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDCA1 Tip:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \" Claims will be automatically generated with current timestamp and selected country code. Example format: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        children: \"241218143045840001\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 31\n                                    }, undefined),\n                                    \" (Dec 18, 2024 14:30:45, USA, KYC)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-gray-700 text-sm font-bold mb-2\",\n                            htmlFor: \"bonusTiers\",\n                            children: \"Bonus Tiers\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            id: \"bonusTiers\",\n                            name: \"bonusTiers\",\n                            className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\",\n                            placeholder: \"e.g., Tier 1: 5%, Tier 2: 10%, Tier 3: 15%\",\n                            value: formData.bonusTiers,\n                            onChange: handleInputChange,\n                            disabled: isSubmitting,\n                            rows: 3\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center\",\n                        disabled: isSubmitting,\n                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 17\n                                }, undefined),\n                                deploymentStep === 'preparing' && 'Preparing...',\n                                deploymentStep === 'connecting' && 'Connecting...',\n                                deploymentStep === 'deploying' && 'Deploying Token...',\n                                deploymentStep === 'confirming' && 'Confirming Transaction...',\n                                deploymentStep === 'fetching' && 'Fetching Token Details...',\n                                deploymentStep === 'setting_up_compliance' && 'Setting up ERC-3643 Compliance...'\n                            ]\n                        }, void 0, true) : \"Create Token on \".concat(getNetworkLabel(network))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n                    lineNumber: 399,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\create-token\\\\components\\\\TokenForm.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\n_c = TokenForm;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TokenForm);\nvar _c;\n$RefreshReg$(_c, \"TokenForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/create-token/components/TokenForm.tsx\n"));

/***/ })

});