generator client {
  provider = "prisma-client-js"
  previewFeatures = []
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Client {
  id                      String              @id @default(cuid())
  firstName               String
  lastName                String
  gender                  Gender
  nationality             String
  birthday                DateTime
  birthPlace              String
  identificationType      IdentificationType
  passportNumber          String?
  idCardNumber            String?
  documentExpiration      DateTime
  phoneNumber             String
  email                   String?             @unique
  occupation              String
  sectorOfActivity        String
  pepStatus               PEPStatus
  pepDetails              String?
  street                  String
  buildingNumber          String
  city                    String
  state                   String?
  country                 String
  zipCode                 String
  sourceOfWealth          String
  bankAccountNumber       String
  sourceOfFunds           String
  taxIdentificationNumber String              @unique
  kycStatus               KYCStatus           @default(PENDING)
  kycCompletedAt          DateTime?
  kycNotes                String?
  walletAddress           String?             @unique
  walletSignature         String?
  walletVerifiedAt        DateTime?
  isWhitelisted           Boolean             @default(false)
  whitelistedAt           DateTime?
  agreementAccepted       Boolean             @default(false)
  agreementAcceptedAt     DateTime?
  createdAt               DateTime            @default(now())
  updatedAt               DateTime            @updatedAt
  createdBy               String?
  updatedBy               String?
  documents               ClientDocument[]
  transactions            ClientTransaction[]
  tokenApprovals          TokenClientApproval[]
  orders                  Order[]

  @@map("clients")
}

model ClientDocument {
  id               String         @id @default(cuid())
  clientId         String
  documentType     DocumentType
  fileName         String
  originalFileName String
  fileSize         Int
  mimeType         String
  filePath         String
  status           DocumentStatus @default(PENDING)
  verifiedAt       DateTime?
  verifiedBy       String?
  rejectionReason  String?
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt
  client           Client         @relation(fields: [clientId], references: [id], onDelete: Cascade)

  @@map("client_documents")
}

model ClientTransaction {
  id              String            @id @default(cuid())
  clientId        String
  transactionHash String            @unique
  tokenAddress    String
  transactionType TransactionType
  amount          String
  fromAddress     String?
  toAddress       String?
  status          TransactionStatus @default(PENDING)
  blockNumber     String?
  gasUsed         String?
  gasPrice        String?
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  client          Client            @relation(fields: [clientId], references: [id], onDelete: Cascade)
  token           Token?            @relation(fields: [tokenAddress], references: [address])

  @@map("client_transactions")
}

model Token {
  id                  String              @id @default(cuid())
  // Blockchain identifiers
  address             String              @unique
  transactionHash     String?             @unique  // Made optional for existing data
  blockNumber         String?
  network             String              @default("amoy")  // Default network

  // Token basic information
  name                String
  symbol              String              @unique
  decimals            Int                 @default(18)  // Default decimals
  maxSupply           String              @default("1000000")  // Default max supply
  totalSupply         String              @default("0")  // Current total supply

  // Token configuration
  tokenType           String              @default("equity")  // Default token type
  tokenPrice          String              @default("10 USD")  // Default price
  currency            String              @default("USD")
  bonusTiers          String?
  tokenImageUrl       String?

  // Contract addresses and admin
  whitelistAddress    String?
  adminAddress        String?             // Made optional for existing data

  // Features and settings
  hasKYC              Boolean             @default(false)
  isActive            Boolean             @default(true)

  // Deployment metadata
  deployedBy          String?             // Admin who deployed
  deploymentNotes     String?

  // Timestamps
  createdAt           DateTime            @default(now())
  updatedAt           DateTime            @updatedAt

  // Relations
  transactions        ClientTransaction[]
  clientApprovals     TokenClientApproval[]
  orders              Order[]

  @@map("tokens")
}

model TokenClientApproval {
  id                  String              @id @default(cuid())
  tokenId             String
  clientId            String

  // Approval status
  approvalStatus      ApprovalStatus      @default(PENDING)
  kycApproved         Boolean             @default(false)
  whitelistApproved   Boolean             @default(false)

  // Approval metadata
  approvedBy          String?             // Admin who approved
  approvedAt          DateTime?
  rejectedReason      String?
  notes               String?

  // Timestamps
  createdAt           DateTime            @default(now())
  updatedAt           DateTime            @updatedAt

  // Relations
  token               Token               @relation(fields: [tokenId], references: [id], onDelete: Cascade)
  client              Client              @relation(fields: [clientId], references: [id], onDelete: Cascade)

  // Ensure unique token-client combinations
  @@unique([tokenId, clientId])
  @@map("token_client_approvals")
}

model Order {
  id                  String              @id @default(cuid())
  tokenId             String
  clientId            String

  // Order details
  status              OrderStatus         @default(PENDING_APPROVAL)
  tokensOrdered       String              // Number of tokens ordered (stored as string for precision)
  tokensConfirmed     String              @default("0") // Number of tokens confirmed
  amountToPay         String              // Amount to pay (tokens * price)
  confirmedPayment    String              @default("0") // Amount of confirmed payment
  tokenPrice          String              // Token price at time of order
  paymentReference    String              @unique // Randomly generated order reference

  // Blockchain transaction details
  transactionHash     String?             // Transaction hash when tokens are minted
  blockNumber         String?             // Block number of the minting transaction

  // Timestamps
  createdAt           DateTime            @default(now())
  updatedAt           DateTime            @updatedAt

  // Relations
  token               Token               @relation(fields: [tokenId], references: [id], onDelete: Cascade)
  client              Client              @relation(fields: [clientId], references: [id], onDelete: Cascade)

  @@map("orders")
}

enum Gender {
  MALE
  FEMALE
  OTHER
  PREFER_NOT_TO_SAY
}

enum IdentificationType {
  PASSPORT
  ID_CARD
  DRIVERS_LICENSE
  OTHER
}

enum PEPStatus {
  NOT_PEP
  DOMESTIC_PEP
  FOREIGN_PEP
  INTERNATIONAL_ORG_PEP
  FAMILY_MEMBER
  CLOSE_ASSOCIATE
}

enum KYCStatus {
  PENDING
  IN_REVIEW
  APPROVED
  REJECTED
  EXPIRED
}

enum DocumentType {
  PASSPORT
  ID_CARD
  DRIVERS_LICENSE
  PROOF_OF_ADDRESS
  BANK_STATEMENT
  INCOME_PROOF
  OTHER
}

enum DocumentStatus {
  PENDING
  APPROVED
  REJECTED
}

enum TransactionType {
  MINT
  TRANSFER
  BURN
  FREEZE
  UNFREEZE
  WHITELIST_ADD
  WHITELIST_REMOVE
}

enum TransactionStatus {
  PENDING
  CONFIRMED
  FAILED
  CANCELLED
}

enum ApprovalStatus {
  PENDING
  APPROVED
  REJECTED
}

enum OrderStatus {
  PENDING_APPROVAL
  CONFIRMED
  MINTED
  CANCELLED
}
