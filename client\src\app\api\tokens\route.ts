import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@auth0/nextjs-auth0';

export async function GET(request: NextRequest) {
  try {
    // Check for test wallet address in query params (for testing purposes)
    const { searchParams } = new URL(request.url);
    const testWalletAddress = searchParams.get('testWallet');

    // Get user session to check wallet address
    const session = await getSession(request, NextResponse.next());
    let userWalletAddress: string | null = testWalletAddress; // Use test wallet if provided

    // If no test wallet and user is logged in, try to get their wallet address
    if (!userWalletAddress && session?.user?.email) {
      try {
        const adminPanelUrl = process.env.ADMIN_PANEL_URL!;
        const clientResponse = await fetch(`${adminPanelUrl}/api/clients?search=${encodeURIComponent(session.user.email)}&limit=1`);
        if (clientResponse.ok) {
          const clientData = await clientResponse.json();
          const client = clientData.clients?.[0];
          userWalletAddress = client?.walletAddress || null;
        }
      } catch (error) {
        console.warn('Could not fetch user wallet address:', error);
      }
    }

    // Fetch tokens from the admin panel API
    const adminPanelUrl = process.env.ADMIN_PANEL_URL!;
    const response = await fetch(`${adminPanelUrl}/api/tokens?source=database&t=${Date.now()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      },
      // Add cache control to ensure fresh data
      cache: 'no-store'
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch tokens from admin panel: ${response.status}`);
    }

    const tokens = await response.json();

    // Get whitelist status for all tokens if user has a wallet
    let whitelistStatuses: { [tokenAddress: string]: boolean } = {};

    if (userWalletAddress && tokens.length > 0) {
      try {
        const tokenAddresses = tokens.map((token: any) => token.address);
        const whitelistResponse = await fetch(`${adminPanelUrl}/api/whitelist/check?t=${Date.now()}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate'
          },
          body: JSON.stringify({
            walletAddress: userWalletAddress,
            tokenAddresses: tokenAddresses
          })
        });

        if (whitelistResponse.ok) {
          const whitelistData = await whitelistResponse.json();
          whitelistData.tokens?.forEach((tokenStatus: any) => {
            whitelistStatuses[tokenStatus.tokenAddress.toLowerCase()] = tokenStatus.isWhitelisted;
          });
        }
      } catch (error) {
        console.warn('Could not fetch whitelist statuses:', error);
      }
    }

    // Transform the data to include only what we need for the client
    const transformedTokens = tokens.map((token: any) => {
      // Extract numeric price and currency from tokenPrice field (e.g., "1.5 ETH" -> price: "1.5", currency: "ETH")
      let numericPrice = '0';
      let extractedCurrency = token.currency || 'USD';

      if (token.tokenPrice) {
        // Try to extract price and currency from tokenPrice field
        const priceWithCurrencyMatch = token.tokenPrice.match(/([\d.]+)\s*([A-Z]{3,4})/i);
        if (priceWithCurrencyMatch) {
          numericPrice = priceWithCurrencyMatch[1];
          extractedCurrency = priceWithCurrencyMatch[2].toUpperCase();
        } else {
          // Fallback to just extracting the number
          const priceMatch = token.tokenPrice.match(/[\d.]+/);
          numericPrice = priceMatch ? priceMatch[0] : '0';
        }
      }

      return {
        id: token.id,
        name: token.name,
        symbol: token.symbol,
        address: token.address,
        totalSupply: token.totalSupply || '0',
        maxSupply: token.maxSupply || '0',
        price: numericPrice,
        currency: extractedCurrency,
        category: token.tokenType || 'Unknown',
        description: token.deploymentNotes || '',
        imageUrl: token.tokenImageUrl || null,
        network: token.network || 'amoy',
        decimals: token.decimals || 0,
        version: '1.0.0', // Default version since not in database
        bonusTiers: token.bonusTiers || '',
        whitelistAddress: token.whitelistAddress || '',
        createdAt: token.createdAt || new Date().toISOString(),
        isWhitelisted: whitelistStatuses[token.address?.toLowerCase()] || false
      };
    });

    return NextResponse.json(transformedTokens);
  } catch (error) {
    console.error('Error fetching tokens:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tokens' },
      { status: 500 }
    );
  }
}
