{"_format": "hh-sol-artifact-1", "contractName": "SimpleClaimRegistry", "sourceName": "contracts/SimpleClaimRegistry.sol", "abi": [{"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "subject", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "claimType", "type": "uint256"}, {"indexed": true, "internalType": "bytes32", "name": "claimId", "type": "bytes32"}, {"indexed": false, "internalType": "address", "name": "issuer", "type": "address"}, {"indexed": false, "internalType": "string", "name": "uri", "type": "string"}], "name": "ClaimIssued", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "subject", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "claimType", "type": "uint256"}, {"indexed": true, "internalType": "bytes32", "name": "claimId", "type": "bytes32"}, {"indexed": false, "internalType": "address", "name": "issuer", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "claimTypeId", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}, {"indexed": false, "internalType": "string", "name": "description", "type": "string"}, {"indexed": true, "internalType": "address", "name": "creator", "type": "address"}], "name": "ClaimTypeCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "claimTypeId", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}, {"indexed": false, "internalType": "string", "name": "description", "type": "string"}, {"indexed": false, "internalType": "bool", "name": "active", "type": "bool"}], "name": "ClaimTypeUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"inputs": [], "name": "CLAIM_ISSUER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "CLAIM_VERIFIER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "claimIds", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "claimTypes", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "address", "name": "creator", "type": "address"}, {"internalType": "uint256", "name": "createdAt", "type": "uint256"}, {"internalType": "bool", "name": "active", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "claims", "outputs": [{"internalType": "uint256", "name": "claimType", "type": "uint256"}, {"internalType": "address", "name": "issuer", "type": "address"}, {"internalType": "address", "name": "subject", "type": "address"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "string", "name": "uri", "type": "string"}, {"internalType": "uint256", "name": "issuedAt", "type": "uint256"}, {"internalType": "uint256", "name": "expiresAt", "type": "uint256"}, {"internalType": "bool", "name": "revoked", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}], "name": "createClaimType", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "creatorClaimTypes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "offset", "type": "uint256"}, {"internalType": "uint256", "name": "limit", "type": "uint256"}], "name": "getActiveClaimTypes", "outputs": [{"components": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "address", "name": "creator", "type": "address"}, {"internalType": "uint256", "name": "createdAt", "type": "uint256"}, {"internalType": "bool", "name": "active", "type": "bool"}], "internalType": "struct SimpleClaimRegistry.ClaimType[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "subject", "type": "address"}, {"internalType": "uint256", "name": "claimType", "type": "uint256"}], "name": "getClaimIds", "outputs": [{"internalType": "bytes32[]", "name": "", "type": "bytes32[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "creator", "type": "address"}], "name": "getClaimTypesByCreator", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getTotalClaimTypes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "subject", "type": "address"}, {"internalType": "uint256", "name": "claimType", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "claimTypeId", "type": "uint256"}], "name": "isValidClaimType", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "subject", "type": "address"}, {"internalType": "uint256", "name": "claimType", "type": "uint256"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "string", "name": "uri", "type": "string"}, {"internalType": "uint256", "name": "expiresAt", "type": "uint256"}], "name": "issueClaim", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "claimTypeId", "type": "uint256"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "bool", "name": "active", "type": "bool"}], "name": "updateClaimType", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}