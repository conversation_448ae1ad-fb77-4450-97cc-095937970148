"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/offers/page",{

/***/ "(app-pages-browser)/./src/app/offers/page.tsx":
/*!*********************************!*\
  !*** ./src/app/offers/page.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OffersPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @auth0/nextjs-auth0/client */ \"(app-pages-browser)/./node_modules/@auth0/nextjs-auth0/dist/client/index.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Navbar */ \"(app-pages-browser)/./src/components/Navbar.tsx\");\n/* harmony import */ var _components_KYCModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/KYCModal */ \"(app-pages-browser)/./src/components/KYCModal.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _components_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/providers/MockAuthProvider */ \"(app-pages-browser)/./src/components/providers/MockAuthProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction OffersPage() {\n    _s();\n    const useMockAuth = \"false\" === 'true';\n    // Use mock auth or real Auth0 based on environment\n    const auth0User = (0,_auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2__.useUser)();\n    const mockAuth = useMockAuth ? (0,_components_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_6__.useMockUser)() : {\n        user: undefined,\n        isLoading: false\n    };\n    const user = useMockAuth ? mockAuth.user : auth0User.user;\n    const userLoading = useMockAuth ? mockAuth.isLoading : auth0User.isLoading;\n    const [tokens, setTokens] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showKYCModal, setShowKYCModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showOrderModal, setShowOrderModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTokenForOrder, setSelectedTokenForOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [orderAmount, setOrderAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSubmittingOrder, setIsSubmittingOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [orderError, setOrderError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const apiClient = (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_5__.useApiClient)();\n    // Fetch client profile\n    const { data: clientProfile } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'client-profile'\n        ],\n        queryFn: {\n            \"OffersPage.useQuery\": ()=>apiClient.getClientProfile()\n        }[\"OffersPage.useQuery\"],\n        enabled: !!user\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OffersPage.useEffect\": ()=>{\n            fetchTokens();\n        }\n    }[\"OffersPage.useEffect\"], [\n        clientProfile === null || clientProfile === void 0 ? void 0 : clientProfile.walletAddress\n    ]); // Refetch when wallet address changes\n    const fetchTokens = async ()=>{\n        try {\n            setLoading(true);\n            // Construct URL with proper query parameters\n            const params = new URLSearchParams();\n            if (clientProfile === null || clientProfile === void 0 ? void 0 : clientProfile.walletAddress) {\n                params.append('testWallet', clientProfile.walletAddress);\n            }\n            params.append('_t', Date.now().toString());\n            const url = \"/api/tokens?\".concat(params.toString());\n            console.log('Fetching tokens from:', url);\n            const response = await fetch(url);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Token fetch error:', response.status, errorText);\n                throw new Error(\"Failed to fetch tokens: \".concat(response.status));\n            }\n            const data = await response.json();\n            console.log('Fetched tokens:', data);\n            setTokens(data);\n        } catch (err) {\n            console.error('Error in fetchTokens:', err);\n            setError(err instanceof Error ? err.message : 'Unknown error');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const formatPrice = (price, currency)=>{\n        const numPrice = parseFloat(price);\n        // Handle crypto currencies that don't have standard currency codes\n        const cryptoCurrencies = [\n            'ETH',\n            'BTC',\n            'USDC',\n            'USDT',\n            'DAI'\n        ];\n        if (cryptoCurrencies.includes(currency.toUpperCase())) {\n            return \"\".concat(numPrice, \" \").concat(currency.toUpperCase());\n        }\n        // Handle standard fiat currencies\n        const supportedCurrencies = [\n            'USD',\n            'EUR',\n            'GBP',\n            'JPY',\n            'CAD',\n            'AUD'\n        ];\n        const currencyCode = supportedCurrencies.includes(currency.toUpperCase()) ? currency.toUpperCase() : 'USD';\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: currencyCode,\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 2\n        }).format(numPrice);\n    };\n    const formatSupply = function(supply) {\n        let decimals = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        const numSupply = parseFloat(supply);\n        // Handle very large numbers (like 1000000000000000000000000)\n        if (decimals > 0 && numSupply > 1000000000000) {\n            // This is likely already in wei/smallest unit, convert to human readable\n            const humanReadable = numSupply / Math.pow(10, decimals);\n            return new Intl.NumberFormat('en-US', {\n                maximumFractionDigits: 0\n            }).format(humanReadable);\n        }\n        // For normal numbers or 0 decimals, display as-is\n        return new Intl.NumberFormat('en-US', {\n            maximumFractionDigits: 0\n        }).format(numSupply);\n    };\n    const getCategoryColor = (category)=>{\n        switch(category.toLowerCase()){\n            case 'commodity':\n            case 'commodities':\n                return 'bg-amber-100 text-amber-800';\n            case 'real estate':\n            case 'realestate':\n                return 'bg-green-100 text-green-800';\n            case 'equity':\n            case 'equities':\n                return 'bg-blue-100 text-blue-800';\n            case 'debt':\n            case 'bonds':\n                return 'bg-purple-100 text-purple-800';\n            case 'fund':\n            case 'funds':\n                return 'bg-indigo-100 text-indigo-800';\n            case 'security':\n            case 'securities':\n                return 'bg-teal-100 text-teal-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getDefaultImage = (category)=>{\n        switch(category.toLowerCase()){\n            case 'commodity':\n            case 'commodities':\n                return '🏗️';\n            case 'real estate':\n            case 'realestate':\n                return '🏢';\n            case 'equity':\n            case 'equities':\n                return '📈';\n            case 'debt':\n            case 'bonds':\n                return '💰';\n            case 'fund':\n            case 'funds':\n                return '🏦';\n            case 'security':\n            case 'securities':\n                return '🛡️';\n            default:\n                return '🪙';\n        }\n    };\n    const handleOrderSubmit = async ()=>{\n        if (!selectedTokenForOrder || !orderAmount || !(clientProfile === null || clientProfile === void 0 ? void 0 : clientProfile.id)) {\n            setOrderError('Missing token, amount, or client information.');\n            return;\n        }\n        // Debug logging\n        console.log('Order submission debug:', {\n            tokenPrice: selectedTokenForOrder.price,\n            tokenCurrency: selectedTokenForOrder.currency,\n            orderAmount: orderAmount,\n            clientWallet: clientProfile.walletAddress,\n            tokenWhitelisted: selectedTokenForOrder.isWhitelisted\n        });\n        // Validate orderAmount is a positive number\n        const amountNumber = parseFloat(orderAmount);\n        if (isNaN(amountNumber) || amountNumber <= 0) {\n            setOrderError('Please enter a valid positive amount.');\n            return;\n        }\n        // Check if amount exceeds max supply\n        if (amountNumber > Number(selectedTokenForOrder.maxSupply)) {\n            setOrderError(\"Cannot order more than \".concat(selectedTokenForOrder.maxSupply, \" tokens\"));\n            return;\n        }\n        // Check if user is whitelisted for this token\n        console.log('Whitelist check:', {\n            tokenAddress: selectedTokenForOrder.address,\n            isWhitelisted: selectedTokenForOrder.isWhitelisted,\n            clientWallet: clientProfile.walletAddress\n        });\n        if (!selectedTokenForOrder.isWhitelisted) {\n            setOrderError(\"You must be whitelisted for this token before placing an order. Please contact support to get whitelisted for \".concat(selectedTokenForOrder.name, \".\"));\n            return;\n        }\n        setIsSubmittingOrder(true);\n        setOrderError(null);\n        try {\n            const response = await fetch('/api/client-orders', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenId: selectedTokenForOrder.id,\n                    clientId: clientProfile.id,\n                    tokensOrdered: orderAmount\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to submit order');\n            }\n            // Order submitted successfully\n            setShowOrderModal(false);\n            setSelectedTokenForOrder(null);\n            setOrderAmount('');\n            // Show success message with order details\n            const totalAmount = formatPrice((amountNumber * Number(selectedTokenForOrder.price)).toString(), selectedTokenForOrder.currency);\n            alert(\"Order submitted successfully!\\n\\nToken: \".concat(selectedTokenForOrder.name, \"\\nAmount: \").concat(orderAmount, \" tokens\\nTotal: \").concat(totalAmount, \"\\n\\nYou will be notified once it is approved.\"));\n        } catch (err) {\n            console.error('Error submitting order:', err);\n            setOrderError(err.message || 'Failed to submit order. Please try again.');\n        } finally{\n            setIsSubmittingOrder(false);\n        }\n    };\n    if (userLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                lineNumber: 276,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n            lineNumber: 275,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                    children: \"TokenDev Offers\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Please log in to view available token offers\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: useMockAuth ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: mockAuth.login,\n                                disabled: mockAuth.isLoading,\n                                className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50\",\n                                children: mockAuth.isLoading ? 'Signing In...' : 'Sign In (Demo)'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/api/auth/login\",\n                                className: \"w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors inline-block text-center\",\n                                children: \"Sign In with Auth0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                lineNumber: 286,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n            lineNumber: 285,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_3__.Navbar, {\n                user: user,\n                clientProfile: clientProfile,\n                onGetQualified: ()=>setShowKYCModal(true),\n                onLogout: useMockAuth ? mockAuth.logout : undefined,\n                useMockAuth: useMockAuth\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                                children: \"Token Investment Opportunities\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Discover and invest in qualified security tokens based on your claims\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500 mb-1\",\n                                                    children: \"Wallet Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"w3m-button\", {\n                                                    size: \"sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, this),\n                            user && clientProfile && tokens.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-blue-900 mb-1\",\n                                                    children: \"Available Investment Opportunities\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-700 text-sm\",\n                                                    children: [\n                                                        \"You have access to \",\n                                                        tokens.length,\n                                                        \" investment \",\n                                                        tokens.length === 1 ? 'opportunity' : 'opportunities'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-blue-900\",\n                                                    children: tokens.length\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-blue-600\",\n                                                    children: \"Available Tokens\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, this),\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-5 w-5 text-red-400\",\n                                        viewBox: \"0 0 20 20\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-red-800\",\n                                            children: \"Error loading tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-700 mt-1\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 11\n                    }, this),\n                    !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: tokens.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: \"\\uD83E\\uDE99\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"No tokens available\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Check back later for new investment opportunities.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: tokens.map((token)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-48 bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n                                            children: token.imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: token.imageUrl,\n                                                alt: token.name,\n                                                className: \"w-24 h-24 object-cover rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 25\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-6xl\",\n                                                children: getDefaultImage(token.category)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                                                    children: token.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500 font-mono\",\n                                                                    children: token.symbol\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col gap-1 items-end\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(getCategoryColor(token.category)),\n                                                                    children: token.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 441,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                token.isWhitelisted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\",\n                                                                    children: \"✅ WHITELISTED\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: formatPrice(token.price, token.currency)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"per token\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4 mb-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"Total Supply\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 463,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: formatSupply(token.totalSupply, token.decimals)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 464,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"Max Supply\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 469,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: formatSupply(token.maxSupply, token.decimals)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 23\n                                                }, this),\n                                                token.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mb-4 line-clamp-2\",\n                                                    children: token.description\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 25\n                                                }, this),\n                                                user && clientProfile ? token.isQualified ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        setSelectedTokenForOrder(token);\n                                                        setShowOrderModal(true);\n                                                    },\n                                                    className: \"w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors font-medium\",\n                                                    children: \"\\uD83D\\uDE80 Invest Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 27\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            disabled: true,\n                                                            className: \"w-full bg-gray-400 text-white py-2 px-4 rounded-lg cursor-not-allowed font-medium\",\n                                                            title: \"You need to complete the required qualifications\",\n                                                            children: \"❌ Qualification Required\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 499,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600 text-center\",\n                                                            children: \"Complete the required qualifications above to invest\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 27\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        if (!user) {\n                                                            // Redirect to login\n                                                            window.location.href = '/api/auth/login';\n                                                        } else {\n                                                            // Show KYC modal\n                                                            setShowKYCModal(true);\n                                                        }\n                                                    },\n                                                    className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium\",\n                                                    children: !user ? 'Sign In to View Offers' : 'Complete Qualification'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-6 py-3 bg-gray-50 border-t\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center text-xs text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Network: \",\n                                                            token.network\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Decimals: \",\n                                                            token.decimals\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, token.id, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                lineNumber: 331,\n                columnNumber: 7\n            }, this),\n            showKYCModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_KYCModal__WEBPACK_IMPORTED_MODULE_4__.KYCModal, {\n                onClose: ()=>setShowKYCModal(false),\n                existingProfile: clientProfile\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                lineNumber: 546,\n                columnNumber: 9\n            }, this),\n            showOrderModal && selectedTokenForOrder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg leading-6 font-medium text-gray-900\",\n                                        children: [\n                                            \"Order \",\n                                            selectedTokenForOrder.name,\n                                            \" (\",\n                                            selectedTokenForOrder.symbol,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowOrderModal(false);\n                                            setSelectedTokenForOrder(null);\n                                            setOrderAmount('');\n                                            setOrderError(null);\n                                        },\n                                        className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                lineNumber: 558,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Token Details\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 581,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 p-3 rounded-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Token:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                lineNumber: 586,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            selectedTokenForOrder.name,\n                                                            \" (\",\n                                                            selectedTokenForOrder.symbol,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Price:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            formatPrice(selectedTokenForOrder.price, selectedTokenForOrder.currency),\n                                                            \" per token\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Available:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                                lineNumber: 592,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            selectedTokenForOrder.maxSupply,\n                                                            \" tokens\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                        lineNumber: 591,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"order-amount\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Number of Tokens to Order\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 599,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"order-amount\",\n                                                type: \"number\",\n                                                min: \"1\",\n                                                step: \"1\",\n                                                value: orderAmount,\n                                                onChange: (e)=>{\n                                                    const value = e.target.value;\n                                                    setOrderAmount(value);\n                                                    setOrderError(null);\n                                                },\n                                                placeholder: \"Enter amount of tokens\",\n                                                className: \"block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 17\n                                    }, this),\n                                    orderAmount && !isNaN(Number(orderAmount)) && Number(orderAmount) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 p-3 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-blue-800\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Total Amount:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                        lineNumber: 622,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \" \",\n                                                    (()=>{\n                                                        const tokenPrice = Number(selectedTokenForOrder.price);\n                                                        const orderQty = Number(orderAmount);\n                                                        console.log('Price calculation debug:', {\n                                                            tokenPrice,\n                                                            orderQty,\n                                                            priceIsNaN: isNaN(tokenPrice),\n                                                            qtyIsNaN: isNaN(orderQty),\n                                                            rawPrice: selectedTokenForOrder.price\n                                                        });\n                                                        if (isNaN(tokenPrice) || isNaN(orderQty)) {\n                                                            return \"Error: Price=\".concat(selectedTokenForOrder.price, \", Qty=\").concat(orderAmount);\n                                                        }\n                                                        const total = orderQty * tokenPrice;\n                                                        return formatPrice(total.toString(), selectedTokenForOrder.currency);\n                                                    })()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-600 mt-1\",\n                                                children: \"This order will be submitted for admin approval\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 620,\n                                        columnNumber: 19\n                                    }, this),\n                                    orderError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 rounded-md p-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600\",\n                                            children: orderError\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                lineNumber: 578,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 flex flex-col space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleOrderSubmit,\n                                        disabled: isSubmittingOrder || !orderAmount,\n                                        className: \"w-full px-4 py-2 bg-green-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isSubmittingOrder ? 'Submitting...' : 'Submit Order'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 658,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowOrderModal(false);\n                                            setSelectedTokenForOrder(null);\n                                            setOrderAmount('');\n                                            setOrderError(null);\n                                        },\n                                        className: \"w-full px-4 py-2 bg-gray-200 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                                lineNumber: 657,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                        lineNumber: 556,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                    lineNumber: 555,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                lineNumber: 554,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-4 right-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"w3m-button\", {}, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                    lineNumber: 684,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n                lineNumber: 683,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\client\\\\src\\\\app\\\\offers\\\\page.tsx\",\n        lineNumber: 322,\n        columnNumber: 5\n    }, this);\n}\n_s(OffersPage, \"ApMYDulHuTrKf3ifJxh2CXa6xLY=\", false, function() {\n    return [\n        _auth0_nextjs_auth0_client__WEBPACK_IMPORTED_MODULE_2__.useUser,\n        _components_providers_MockAuthProvider__WEBPACK_IMPORTED_MODULE_6__.useMockUser,\n        _lib_api_client__WEBPACK_IMPORTED_MODULE_5__.useApiClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery\n    ];\n});\n_c = OffersPage;\nvar _c;\n$RefreshReg$(_c, \"OffersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/offers/page.tsx\n"));

/***/ })

});