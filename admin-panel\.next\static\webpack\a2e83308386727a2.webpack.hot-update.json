{"c": ["app/layout", "app/identity/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentCheckIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cidentity%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./src/app/identity/page.tsx", "(app-pages-browser)/./src/components/IdentityManagement.tsx"]}