"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-token/page",{

/***/ "(app-pages-browser)/./src/app/create-token/hooks/useERC3643Integration.ts":
/*!*************************************************************!*\
  !*** ./src/app/create-token/hooks/useERC3643Integration.ts ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useERC3643Integration: () => (/* binding */ useERC3643Integration)\n/* harmony export */ });\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/abi/abi-coder.js\");\n\n// Contract ABIs for ERC-3643 integration\nconst IdentityRegistryABI = [\n    \"function registerIdentity(address investor, uint16 country) external\",\n    \"function addToWhitelist(address investor) external\",\n    \"function approveKyc(address investor) external\",\n    \"function isVerified(address investor) external view returns (bool)\",\n    \"function isWhitelisted(address investor) external view returns (bool)\",\n    \"function isKycApproved(address investor) external view returns (bool)\"\n];\nconst ClaimRegistryABI = [\n    \"function issueClaim(address subject, uint256 topic, bytes calldata signature, bytes calldata data, string calldata uri, uint256 validUntil) external\",\n    \"function hasValidClaim(address subject, uint256 topic) external view returns (bool)\"\n];\nconst ComplianceABI = [\n    \"function created(address to, uint256 value) external\",\n    \"function canTransfer(address from, address to, uint256 value) external view returns (bool)\"\n];\n// Tokeny Claim Topics (following Tokeny standard)\nconst CLAIM_TOPICS = {\n    KYC: 1,\n    AML: 2,\n    IDENTITY: 3,\n    QUALIFICATION: 4,\n    ACCREDITATION: 5,\n    RESIDENCE: 6,\n    TOKEN_ISSUER: 7 // Custom claim for token issuers\n};\n// Tokeny-style claim data format\n// Format: YYYYMMDDHHMMSS (timestamp) + country code + additional data\n// Example: 10101010000648 = timestamp + country + verification level\nfunction generateTokenyClaim(country, claimType) {\n    const now = new Date();\n    const timestamp = now.getFullYear().toString().slice(-2) + // YY\n    (now.getMonth() + 1).toString().padStart(2, '0') + // MM\n    now.getDate().toString().padStart(2, '0') + // DD\n    now.getHours().toString().padStart(2, '0') + // HH\n    now.getMinutes().toString().padStart(2, '0') + // MM\n    now.getSeconds().toString().padStart(2, '0'); // SS\n    const countryCode = getCountryCode(country).toString().padStart(3, '0');\n    // Additional data based on claim type\n    let additionalData = '';\n    switch(claimType){\n        case 'KYC':\n            additionalData = '001'; // KYC level 1\n            break;\n        case 'QUALIFICATION':\n            additionalData = '002'; // Qualified investor\n            break;\n        case 'TOKEN_ISSUER':\n            additionalData = '003'; // Token issuer\n            break;\n        default:\n            additionalData = '000';\n    }\n    return timestamp + countryCode + additionalData;\n}\n// Country code mapping (ISO-3166 numeric)\nconst COUNTRY_CODES = {\n    'US': 840,\n    'USA': 840,\n    'United States': 840,\n    'CA': 124,\n    'Canada': 124,\n    'GB': 826,\n    'UK': 826,\n    'United Kingdom': 826,\n    'DE': 276,\n    'Germany': 276,\n    'FR': 250,\n    'France': 250,\n    'IT': 380,\n    'Italy': 380,\n    'ES': 724,\n    'Spain': 724,\n    'NL': 528,\n    'Netherlands': 528,\n    'CH': 756,\n    'Switzerland': 756,\n    'AU': 36,\n    'Australia': 36,\n    'JP': 392,\n    'Japan': 392,\n    'SG': 702,\n    'Singapore': 702\n};\nfunction getCountryCode(country) {\n    return COUNTRY_CODES[country] || COUNTRY_CODES[country.toUpperCase()] || 840; // Default to USA\n}\n/**\n * Hook for ERC-3643 integration during token deployment\n */ function useERC3643Integration() {\n    /**\n   * Setup ERC-3643 compliance for a newly deployed token\n   */ const setupERC3643Compliance = async (tokenAddress, ownerAddress, signer, tokenData)=>{\n        console.log(\"🏛️ Setting up ERC-3643 compliance for token:\", tokenAddress);\n        const results = {\n            identityRegistered: false,\n            whitelisted: false,\n            kycApproved: false,\n            claimsIssued: [],\n            complianceNotified: false,\n            errors: []\n        };\n        try {\n            // Get contract addresses from environment\n            const identityRegistryAddress = \"0x129E04323E4c9bFBD097489473d8523E4015Bfc3\";\n            const claimRegistryAddress = \"******************************************\";\n            const complianceAddress = \"******************************************\";\n            if (!identityRegistryAddress || !claimRegistryAddress || !complianceAddress) {\n                console.warn(\"⚠️ ERC-3643 contract addresses not configured, skipping compliance setup\");\n                return results;\n            }\n            // Connect to contracts\n            const identityRegistry = new ethers__WEBPACK_IMPORTED_MODULE_0__.Contract(identityRegistryAddress, IdentityRegistryABI, signer);\n            const claimRegistry = new ethers__WEBPACK_IMPORTED_MODULE_0__.Contract(claimRegistryAddress, ClaimRegistryABI, signer);\n            const compliance = new ethers__WEBPACK_IMPORTED_MODULE_0__.Contract(complianceAddress, ComplianceABI, signer);\n            // Step 1: Register identity if not already registered\n            try {\n                const isVerified = await identityRegistry.isVerified(ownerAddress);\n                if (!isVerified) {\n                    console.log(\"📝 Registering identity for token owner...\");\n                    const countryCode = getCountryCode(tokenData.country || 'US');\n                    const tx1 = await identityRegistry.registerIdentity(ownerAddress, countryCode);\n                    await tx1.wait();\n                    results.identityRegistered = true;\n                    console.log(\"✅ Identity registered successfully\");\n                } else {\n                    console.log(\"✅ Identity already registered\");\n                    results.identityRegistered = true;\n                }\n            } catch (error) {\n                console.error(\"❌ Failed to register identity:\", error);\n                results.errors.push(\"Identity registration failed: \".concat(error.message));\n            }\n            // Step 2: Add to whitelist if not already whitelisted\n            try {\n                const isWhitelisted = await identityRegistry.isWhitelisted(ownerAddress);\n                if (!isWhitelisted) {\n                    console.log(\"📋 Adding to whitelist...\");\n                    const tx2 = await identityRegistry.addToWhitelist(ownerAddress);\n                    await tx2.wait();\n                    results.whitelisted = true;\n                    console.log(\"✅ Added to whitelist successfully\");\n                } else {\n                    console.log(\"✅ Already whitelisted\");\n                    results.whitelisted = true;\n                }\n            } catch (error) {\n                console.error(\"❌ Failed to add to whitelist:\", error);\n                results.errors.push(\"Whitelist addition failed: \".concat(error.message));\n            }\n            // Step 3: Approve KYC if not already approved\n            try {\n                const isKycApproved = await identityRegistry.isKycApproved(ownerAddress);\n                if (!isKycApproved) {\n                    console.log(\"🔍 Approving KYC...\");\n                    const tx3 = await identityRegistry.approveKyc(ownerAddress);\n                    await tx3.wait();\n                    results.kycApproved = true;\n                    console.log(\"✅ KYC approved successfully\");\n                } else {\n                    console.log(\"✅ KYC already approved\");\n                    results.kycApproved = true;\n                }\n            } catch (error) {\n                console.error(\"❌ Failed to approve KYC:\", error);\n                results.errors.push(\"KYC approval failed: \".concat(error.message));\n            }\n            // Step 4: Issue Tokeny-style claims for token issuer\n            try {\n                console.log(\"📜 Issuing Tokeny-style claims for token issuer...\");\n                // Generate Tokeny-style claim data\n                const kycClaimValue = generateTokenyClaim(tokenData.country || 'US', 'KYC');\n                const qualificationClaimValue = generateTokenyClaim(tokenData.country || 'US', 'QUALIFICATION');\n                const issuerClaimValue = generateTokenyClaim(tokenData.country || 'US', 'TOKEN_ISSUER');\n                console.log(\"🔢 Generated Tokeny claims:\", {\n                    KYC: kycClaimValue,\n                    QUALIFICATION: qualificationClaimValue,\n                    TOKEN_ISSUER: issuerClaimValue\n                });\n                // Issue KYC claim with Tokeny format\n                const kycClaimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                    \"string\",\n                    \"string\",\n                    \"uint256\"\n                ], [\n                    kycClaimValue,\n                    \"KYC_APPROVED\",\n                    Math.floor(Date.now() / 1000)\n                ]);\n                const hasKycClaim = await claimRegistry.hasValidClaim(ownerAddress, CLAIM_TOPICS.KYC);\n                if (!hasKycClaim) {\n                    const tx4 = await claimRegistry.issueClaim(ownerAddress, CLAIM_TOPICS.KYC, \"0x\", kycClaimData, \"KYC:\".concat(kycClaimValue), 0 // never expires\n                    );\n                    await tx4.wait();\n                    results.claimsIssued.push(\"KYC:\".concat(kycClaimValue));\n                    console.log(\"✅ KYC claim issued: \".concat(kycClaimValue));\n                } else {\n                    console.log(\"✅ KYC claim already exists\");\n                }\n                // Issue qualification claim with Tokeny format\n                const qualificationClaimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                    \"string\",\n                    \"string\",\n                    \"uint256\"\n                ], [\n                    qualificationClaimValue,\n                    \"QUALIFIED_INVESTOR\",\n                    Math.floor(Date.now() / 1000)\n                ]);\n                const hasQualificationClaim = await claimRegistry.hasValidClaim(ownerAddress, CLAIM_TOPICS.QUALIFICATION);\n                if (!hasQualificationClaim) {\n                    const tx5 = await claimRegistry.issueClaim(ownerAddress, CLAIM_TOPICS.QUALIFICATION, \"0x\", qualificationClaimData, \"QUALIFICATION:\".concat(qualificationClaimValue), 0);\n                    await tx5.wait();\n                    results.claimsIssued.push(\"QUALIFICATION:\".concat(qualificationClaimValue));\n                    console.log(\"✅ Qualification claim issued: \".concat(qualificationClaimValue));\n                } else {\n                    console.log(\"✅ Qualification claim already exists\");\n                }\n                // Issue token issuer claim with Tokeny format\n                const issuerClaimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                    \"string\",\n                    \"string\",\n                    \"string\",\n                    \"string\",\n                    \"uint256\"\n                ], [\n                    issuerClaimValue,\n                    tokenData.name,\n                    tokenData.symbol,\n                    tokenData.tokenType,\n                    Math.floor(Date.now() / 1000)\n                ]);\n                const tx6 = await claimRegistry.issueClaim(ownerAddress, CLAIM_TOPICS.TOKEN_ISSUER, \"0x\", issuerClaimData, \"TOKEN_ISSUER:\".concat(issuerClaimValue, \":\").concat(tokenData.symbol), 0);\n                await tx6.wait();\n                results.claimsIssued.push(\"TOKEN_ISSUER:\".concat(issuerClaimValue));\n                console.log(\"✅ Token issuer claim issued: \".concat(issuerClaimValue));\n            } catch (error) {\n                console.error(\"❌ Failed to issue claims:\", error);\n                results.errors.push(\"Claims issuance failed: \".concat(error.message));\n            }\n            // Step 5: Notify compliance contract (if needed)\n            try {\n                console.log(\"⚖️ Notifying compliance contract...\");\n                // This would typically be called when tokens are minted, but we can prepare it\n                const canTransfer = await compliance.canTransfer(ownerAddress, ownerAddress, 1);\n                console.log(\"✅ Compliance check passed:\", canTransfer);\n                results.complianceNotified = true;\n            } catch (error) {\n                console.error(\"❌ Compliance notification failed:\", error);\n                results.errors.push(\"Compliance notification failed: \".concat(error.message));\n            }\n            console.log(\"🎉 ERC-3643 compliance setup completed!\");\n            console.log(\"Results:\", {\n                identityRegistered: results.identityRegistered,\n                whitelisted: results.whitelisted,\n                kycApproved: results.kycApproved,\n                claimsIssued: results.claimsIssued,\n                complianceNotified: results.complianceNotified,\n                errorCount: results.errors.length\n            });\n        } catch (error) {\n            console.error(\"❌ ERC-3643 setup failed:\", error);\n            results.errors.push(\"General setup failed: \".concat(error.message));\n        }\n        return results;\n    };\n    /**\n   * Check if ERC-3643 contracts are available\n   */ const isERC3643Available = ()=>{\n        return !!( true && \"******************************************\");\n    };\n    /**\n   * Get ERC-3643 contract addresses\n   */ const getERC3643Addresses = ()=>{\n        return {\n            identityRegistry: \"0x129E04323E4c9bFBD097489473d8523E4015Bfc3\",\n            claimRegistry: \"******************************************\",\n            compliance: \"******************************************\"\n        };\n    };\n    return {\n        setupERC3643Compliance,\n        isERC3643Available,\n        getERC3643Addresses\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY3JlYXRlLXRva2VuL2hvb2tzL3VzZUVSQzM2NDNJbnRlZ3JhdGlvbi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0M7QUFFaEMseUNBQXlDO0FBQ3pDLE1BQU1DLHNCQUFzQjtJQUMxQjtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7Q0FDRDtBQUVELE1BQU1DLG1CQUFtQjtJQUN2QjtJQUNBO0NBQ0Q7QUFFRCxNQUFNQyxnQkFBZ0I7SUFDcEI7SUFDQTtDQUNEO0FBRUQsa0RBQWtEO0FBQ2xELE1BQU1DLGVBQWU7SUFDbkJDLEtBQUs7SUFDTEMsS0FBSztJQUNMQyxVQUFVO0lBQ1ZDLGVBQWU7SUFDZkMsZUFBZTtJQUNmQyxXQUFXO0lBQ1hDLGNBQWMsRUFBRSxpQ0FBaUM7QUFDbkQ7QUFFQSxpQ0FBaUM7QUFDakMsc0VBQXNFO0FBQ3RFLHFFQUFxRTtBQUNyRSxTQUFTQyxvQkFBb0JDLE9BQWUsRUFBRUMsU0FBaUI7SUFDN0QsTUFBTUMsTUFBTSxJQUFJQztJQUNoQixNQUFNQyxZQUFZRixJQUFJRyxXQUFXLEdBQUdDLFFBQVEsR0FBR0MsS0FBSyxDQUFDLENBQUMsS0FDckMsS0FEK0M7SUFDOUNMLENBQUFBLElBQUlNLFFBQVEsS0FBSyxHQUFHRixRQUFRLEdBQUdHLFFBQVEsQ0FBQyxHQUFHLE9BQU8sS0FBSztJQUN4RFAsSUFBSVEsT0FBTyxHQUFHSixRQUFRLEdBQUdHLFFBQVEsQ0FBQyxHQUFHLE9BQU8sS0FBSztJQUNqRFAsSUFBSVMsUUFBUSxHQUFHTCxRQUFRLEdBQUdHLFFBQVEsQ0FBQyxHQUFHLE9BQU8sS0FBSztJQUNsRFAsSUFBSVUsVUFBVSxHQUFHTixRQUFRLEdBQUdHLFFBQVEsQ0FBQyxHQUFHLE9BQU8sS0FBSztJQUNwRFAsSUFBSVcsVUFBVSxHQUFHUCxRQUFRLEdBQUdHLFFBQVEsQ0FBQyxHQUFHLE1BQU0sS0FBSztJQUVwRSxNQUFNSyxjQUFjQyxlQUFlZixTQUFTTSxRQUFRLEdBQUdHLFFBQVEsQ0FBQyxHQUFHO0lBRW5FLHNDQUFzQztJQUN0QyxJQUFJTyxpQkFBaUI7SUFDckIsT0FBUWY7UUFDTixLQUFLO1lBQ0hlLGlCQUFpQixPQUFPLGNBQWM7WUFDdEM7UUFDRixLQUFLO1lBQ0hBLGlCQUFpQixPQUFPLHFCQUFxQjtZQUM3QztRQUNGLEtBQUs7WUFDSEEsaUJBQWlCLE9BQU8sZUFBZTtZQUN2QztRQUNGO1lBQ0VBLGlCQUFpQjtJQUNyQjtJQUVBLE9BQU9aLFlBQVlVLGNBQWNFO0FBQ25DO0FBRUEsMENBQTBDO0FBQzFDLE1BQU1DLGdCQUEyQztJQUMvQyxNQUFNO0lBQUssT0FBTztJQUFLLGlCQUFpQjtJQUN4QyxNQUFNO0lBQUssVUFBVTtJQUNyQixNQUFNO0lBQUssTUFBTTtJQUFLLGtCQUFrQjtJQUN4QyxNQUFNO0lBQUssV0FBVztJQUN0QixNQUFNO0lBQUssVUFBVTtJQUNyQixNQUFNO0lBQUssU0FBUztJQUNwQixNQUFNO0lBQUssU0FBUztJQUNwQixNQUFNO0lBQUssZUFBZTtJQUMxQixNQUFNO0lBQUssZUFBZTtJQUMxQixNQUFNO0lBQUksYUFBYTtJQUN2QixNQUFNO0lBQUssU0FBUztJQUNwQixNQUFNO0lBQUssYUFBYTtBQUMxQjtBQUVBLFNBQVNGLGVBQWVmLE9BQWU7SUFDckMsT0FBT2lCLGFBQWEsQ0FBQ2pCLFFBQVEsSUFBSWlCLGFBQWEsQ0FBQ2pCLFFBQVFrQixXQUFXLEdBQUcsSUFBSSxLQUFLLGlCQUFpQjtBQUNqRztBQUVBOztDQUVDLEdBQ00sU0FBU0M7SUFFZDs7R0FFQyxHQUNELE1BQU1DLHlCQUF5QixPQUM3QkMsY0FDQUMsY0FDQUMsUUFDQUM7UUFRQUMsUUFBUUMsR0FBRyxDQUFDLGlEQUFpREw7UUFFN0QsTUFBTU0sVUFBVTtZQUNkQyxvQkFBb0I7WUFDcEJDLGFBQWE7WUFDYkMsYUFBYTtZQUNiQyxjQUFjLEVBQUU7WUFDaEJDLG9CQUFvQjtZQUNwQkMsUUFBUSxFQUFFO1FBQ1o7UUFFQSxJQUFJO1lBQ0YsMENBQTBDO1lBQzFDLE1BQU1DLDBCQUEwQkMsNENBQWlEO1lBQ2pGLE1BQU1HLHVCQUF1QkgsNENBQThDO1lBQzNFLE1BQU1LLG9CQUFvQkwsNENBQTBDO1lBRXBFLElBQUksQ0FBQ0QsMkJBQTJCLENBQUNJLHdCQUF3QixDQUFDRSxtQkFBbUI7Z0JBQzNFZixRQUFRaUIsSUFBSSxDQUFDO2dCQUNiLE9BQU9mO1lBQ1Q7WUFFQSx1QkFBdUI7WUFDdkIsTUFBTWdCLG1CQUFtQixJQUFJeEQsNENBQWUsQ0FBQytDLHlCQUF5QjlDLHFCQUFxQm1DO1lBQzNGLE1BQU1zQixnQkFBZ0IsSUFBSTFELDRDQUFlLENBQUNtRCxzQkFBc0JqRCxrQkFBa0JrQztZQUNsRixNQUFNdUIsYUFBYSxJQUFJM0QsNENBQWUsQ0FBQ3FELG1CQUFtQmxELGVBQWVpQztZQUV6RSxzREFBc0Q7WUFDdEQsSUFBSTtnQkFDRixNQUFNd0IsYUFBYSxNQUFNSixpQkFBaUJJLFVBQVUsQ0FBQ3pCO2dCQUNyRCxJQUFJLENBQUN5QixZQUFZO29CQUNmdEIsUUFBUUMsR0FBRyxDQUFDO29CQUNaLE1BQU1aLGNBQWNDLGVBQWVTLFVBQVV4QixPQUFPLElBQUk7b0JBQ3hELE1BQU1nRCxNQUFNLE1BQU1MLGlCQUFpQk0sZ0JBQWdCLENBQUMzQixjQUFjUjtvQkFDbEUsTUFBTWtDLElBQUlFLElBQUk7b0JBQ2R2QixRQUFRQyxrQkFBa0IsR0FBRztvQkFDN0JILFFBQVFDLEdBQUcsQ0FBQztnQkFDZCxPQUFPO29CQUNMRCxRQUFRQyxHQUFHLENBQUM7b0JBQ1pDLFFBQVFDLGtCQUFrQixHQUFHO2dCQUMvQjtZQUNGLEVBQUUsT0FBT3VCLE9BQU87Z0JBQ2QxQixRQUFRMEIsS0FBSyxDQUFDLGtDQUFrQ0E7Z0JBQ2hEeEIsUUFBUU0sTUFBTSxDQUFDbUIsSUFBSSxDQUFDLGlDQUErQyxPQUFkRCxNQUFNRSxPQUFPO1lBQ3BFO1lBRUEsc0RBQXNEO1lBQ3RELElBQUk7Z0JBQ0YsTUFBTUMsZ0JBQWdCLE1BQU1YLGlCQUFpQlcsYUFBYSxDQUFDaEM7Z0JBQzNELElBQUksQ0FBQ2dDLGVBQWU7b0JBQ2xCN0IsUUFBUUMsR0FBRyxDQUFDO29CQUNaLE1BQU02QixNQUFNLE1BQU1aLGlCQUFpQmEsY0FBYyxDQUFDbEM7b0JBQ2xELE1BQU1pQyxJQUFJTCxJQUFJO29CQUNkdkIsUUFBUUUsV0FBVyxHQUFHO29CQUN0QkosUUFBUUMsR0FBRyxDQUFDO2dCQUNkLE9BQU87b0JBQ0xELFFBQVFDLEdBQUcsQ0FBQztvQkFDWkMsUUFBUUUsV0FBVyxHQUFHO2dCQUN4QjtZQUNGLEVBQUUsT0FBT3NCLE9BQU87Z0JBQ2QxQixRQUFRMEIsS0FBSyxDQUFDLGlDQUFpQ0E7Z0JBQy9DeEIsUUFBUU0sTUFBTSxDQUFDbUIsSUFBSSxDQUFDLDhCQUE0QyxPQUFkRCxNQUFNRSxPQUFPO1lBQ2pFO1lBRUEsOENBQThDO1lBQzlDLElBQUk7Z0JBQ0YsTUFBTUksZ0JBQWdCLE1BQU1kLGlCQUFpQmMsYUFBYSxDQUFDbkM7Z0JBQzNELElBQUksQ0FBQ21DLGVBQWU7b0JBQ2xCaEMsUUFBUUMsR0FBRyxDQUFDO29CQUNaLE1BQU1nQyxNQUFNLE1BQU1mLGlCQUFpQmdCLFVBQVUsQ0FBQ3JDO29CQUM5QyxNQUFNb0MsSUFBSVIsSUFBSTtvQkFDZHZCLFFBQVFHLFdBQVcsR0FBRztvQkFDdEJMLFFBQVFDLEdBQUcsQ0FBQztnQkFDZCxPQUFPO29CQUNMRCxRQUFRQyxHQUFHLENBQUM7b0JBQ1pDLFFBQVFHLFdBQVcsR0FBRztnQkFDeEI7WUFDRixFQUFFLE9BQU9xQixPQUFPO2dCQUNkMUIsUUFBUTBCLEtBQUssQ0FBQyw0QkFBNEJBO2dCQUMxQ3hCLFFBQVFNLE1BQU0sQ0FBQ21CLElBQUksQ0FBQyx3QkFBc0MsT0FBZEQsTUFBTUUsT0FBTztZQUMzRDtZQUVBLHFEQUFxRDtZQUNyRCxJQUFJO2dCQUNGNUIsUUFBUUMsR0FBRyxDQUFDO2dCQUVaLG1DQUFtQztnQkFDbkMsTUFBTWtDLGdCQUFnQjdELG9CQUFvQnlCLFVBQVV4QixPQUFPLElBQUksTUFBTTtnQkFDckUsTUFBTTZELDBCQUEwQjlELG9CQUFvQnlCLFVBQVV4QixPQUFPLElBQUksTUFBTTtnQkFDL0UsTUFBTThELG1CQUFtQi9ELG9CQUFvQnlCLFVBQVV4QixPQUFPLElBQUksTUFBTTtnQkFFeEV5QixRQUFRQyxHQUFHLENBQUMsK0JBQStCO29CQUN6Q2xDLEtBQUtvRTtvQkFDTGpFLGVBQWVrRTtvQkFDZi9ELGNBQWNnRTtnQkFDaEI7Z0JBRUEscUNBQXFDO2dCQUNyQyxNQUFNQyxlQUFlNUUsNENBQWUsQ0FBQzhFLGVBQWUsR0FBR0MsTUFBTSxDQUMzRDtvQkFBQztvQkFBVTtvQkFBVTtpQkFBVSxFQUMvQjtvQkFBQ047b0JBQWU7b0JBQWdCTyxLQUFLQyxLQUFLLENBQUNqRSxLQUFLRCxHQUFHLEtBQUs7aUJBQU07Z0JBR2hFLE1BQU1tRSxjQUFjLE1BQU14QixjQUFjeUIsYUFBYSxDQUFDaEQsY0FBYy9CLGFBQWFDLEdBQUc7Z0JBQ3BGLElBQUksQ0FBQzZFLGFBQWE7b0JBQ2hCLE1BQU1FLE1BQU0sTUFBTTFCLGNBQWMyQixVQUFVLENBQ3hDbEQsY0FDQS9CLGFBQWFDLEdBQUcsRUFDaEIsTUFDQXVFLGNBQ0EsT0FBcUIsT0FBZEgsZ0JBQ1AsRUFBRSxnQkFBZ0I7O29CQUVwQixNQUFNVyxJQUFJckIsSUFBSTtvQkFDZHZCLFFBQVFJLFlBQVksQ0FBQ3FCLElBQUksQ0FBQyxPQUFxQixPQUFkUTtvQkFDakNuQyxRQUFRQyxHQUFHLENBQUMsdUJBQXFDLE9BQWRrQztnQkFDckMsT0FBTztvQkFDTG5DLFFBQVFDLEdBQUcsQ0FBQztnQkFDZDtnQkFFQSwrQ0FBK0M7Z0JBQy9DLE1BQU0rQyx5QkFBeUJ0Riw0Q0FBZSxDQUFDOEUsZUFBZSxHQUFHQyxNQUFNLENBQ3JFO29CQUFDO29CQUFVO29CQUFVO2lCQUFVLEVBQy9CO29CQUFDTDtvQkFBeUI7b0JBQXNCTSxLQUFLQyxLQUFLLENBQUNqRSxLQUFLRCxHQUFHLEtBQUs7aUJBQU07Z0JBR2hGLE1BQU13RSx3QkFBd0IsTUFBTTdCLGNBQWN5QixhQUFhLENBQUNoRCxjQUFjL0IsYUFBYUksYUFBYTtnQkFDeEcsSUFBSSxDQUFDK0UsdUJBQXVCO29CQUMxQixNQUFNQyxNQUFNLE1BQU05QixjQUFjMkIsVUFBVSxDQUN4Q2xELGNBQ0EvQixhQUFhSSxhQUFhLEVBQzFCLE1BQ0E4RSx3QkFDQSxpQkFBeUMsT0FBeEJaLDBCQUNqQjtvQkFFRixNQUFNYyxJQUFJekIsSUFBSTtvQkFDZHZCLFFBQVFJLFlBQVksQ0FBQ3FCLElBQUksQ0FBQyxpQkFBeUMsT0FBeEJTO29CQUMzQ3BDLFFBQVFDLEdBQUcsQ0FBQyxpQ0FBeUQsT0FBeEJtQztnQkFDL0MsT0FBTztvQkFDTHBDLFFBQVFDLEdBQUcsQ0FBQztnQkFDZDtnQkFFQSw4Q0FBOEM7Z0JBQzlDLE1BQU1rRCxrQkFBa0J6Riw0Q0FBZSxDQUFDOEUsZUFBZSxHQUFHQyxNQUFNLENBQzlEO29CQUFDO29CQUFVO29CQUFVO29CQUFVO29CQUFVO2lCQUFVLEVBQ25EO29CQUFDSjtvQkFBa0J0QyxVQUFVcUQsSUFBSTtvQkFBRXJELFVBQVVzRCxNQUFNO29CQUFFdEQsVUFBVXVELFNBQVM7b0JBQUVaLEtBQUtDLEtBQUssQ0FBQ2pFLEtBQUtELEdBQUcsS0FBSztpQkFBTTtnQkFHMUcsTUFBTThFLE1BQU0sTUFBTW5DLGNBQWMyQixVQUFVLENBQ3hDbEQsY0FDQS9CLGFBQWFPLFlBQVksRUFDekIsTUFDQThFLGlCQUNBLGdCQUFvQ3BELE9BQXBCc0Msa0JBQWlCLEtBQW9CLE9BQWpCdEMsVUFBVXNELE1BQU0sR0FDcEQ7Z0JBRUYsTUFBTUUsSUFBSTlCLElBQUk7Z0JBQ2R2QixRQUFRSSxZQUFZLENBQUNxQixJQUFJLENBQUMsZ0JBQWlDLE9BQWpCVTtnQkFDMUNyQyxRQUFRQyxHQUFHLENBQUMsZ0NBQWlELE9BQWpCb0M7WUFFOUMsRUFBRSxPQUFPWCxPQUFPO2dCQUNkMUIsUUFBUTBCLEtBQUssQ0FBQyw2QkFBNkJBO2dCQUMzQ3hCLFFBQVFNLE1BQU0sQ0FBQ21CLElBQUksQ0FBQywyQkFBeUMsT0FBZEQsTUFBTUUsT0FBTztZQUM5RDtZQUVBLGlEQUFpRDtZQUNqRCxJQUFJO2dCQUNGNUIsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLCtFQUErRTtnQkFDL0UsTUFBTXVELGNBQWMsTUFBTW5DLFdBQVdtQyxXQUFXLENBQUMzRCxjQUFjQSxjQUFjO2dCQUM3RUcsUUFBUUMsR0FBRyxDQUFDLDhCQUE4QnVEO2dCQUMxQ3RELFFBQVFLLGtCQUFrQixHQUFHO1lBQy9CLEVBQUUsT0FBT21CLE9BQU87Z0JBQ2QxQixRQUFRMEIsS0FBSyxDQUFDLHFDQUFxQ0E7Z0JBQ25EeEIsUUFBUU0sTUFBTSxDQUFDbUIsSUFBSSxDQUFDLG1DQUFpRCxPQUFkRCxNQUFNRSxPQUFPO1lBQ3RFO1lBRUE1QixRQUFRQyxHQUFHLENBQUM7WUFDWkQsUUFBUUMsR0FBRyxDQUFDLFlBQVk7Z0JBQ3RCRSxvQkFBb0JELFFBQVFDLGtCQUFrQjtnQkFDOUNDLGFBQWFGLFFBQVFFLFdBQVc7Z0JBQ2hDQyxhQUFhSCxRQUFRRyxXQUFXO2dCQUNoQ0MsY0FBY0osUUFBUUksWUFBWTtnQkFDbENDLG9CQUFvQkwsUUFBUUssa0JBQWtCO2dCQUM5Q2tELFlBQVl2RCxRQUFRTSxNQUFNLENBQUNrRCxNQUFNO1lBQ25DO1FBRUYsRUFBRSxPQUFPaEMsT0FBTztZQUNkMUIsUUFBUTBCLEtBQUssQ0FBQyw0QkFBNEJBO1lBQzFDeEIsUUFBUU0sTUFBTSxDQUFDbUIsSUFBSSxDQUFDLHlCQUF1QyxPQUFkRCxNQUFNRSxPQUFPO1FBQzVEO1FBRUEsT0FBTzFCO0lBQ1Q7SUFFQTs7R0FFQyxHQUNELE1BQU15RCxxQkFBcUI7UUFDekIsT0FBTyxDQUFDLENBQ05qRCxDQUFBQSxLQUM4QyxJQUM5Q0EsNENBQTBDO0lBRTlDO0lBRUE7O0dBRUMsR0FDRCxNQUFNa0Qsc0JBQXNCO1FBQzFCLE9BQU87WUFDTDFDLGtCQUFrQlIsNENBQWlEO1lBQ25FVSxlQUFlViw0Q0FBOEM7WUFDN0RXLFlBQVlYLDRDQUEwQztRQUN4RDtJQUNGO0lBRUEsT0FBTztRQUNMZjtRQUNBZ0U7UUFDQUM7SUFDRjtBQUNGIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxzcmNcXGFwcFxcY3JlYXRlLXRva2VuXFxob29rc1xcdXNlRVJDMzY0M0ludGVncmF0aW9uLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGV0aGVycyB9IGZyb20gJ2V0aGVycyc7XG5cbi8vIENvbnRyYWN0IEFCSXMgZm9yIEVSQy0zNjQzIGludGVncmF0aW9uXG5jb25zdCBJZGVudGl0eVJlZ2lzdHJ5QUJJID0gW1xuICBcImZ1bmN0aW9uIHJlZ2lzdGVySWRlbnRpdHkoYWRkcmVzcyBpbnZlc3RvciwgdWludDE2IGNvdW50cnkpIGV4dGVybmFsXCIsXG4gIFwiZnVuY3Rpb24gYWRkVG9XaGl0ZWxpc3QoYWRkcmVzcyBpbnZlc3RvcikgZXh0ZXJuYWxcIixcbiAgXCJmdW5jdGlvbiBhcHByb3ZlS3ljKGFkZHJlc3MgaW52ZXN0b3IpIGV4dGVybmFsXCIsXG4gIFwiZnVuY3Rpb24gaXNWZXJpZmllZChhZGRyZXNzIGludmVzdG9yKSBleHRlcm5hbCB2aWV3IHJldHVybnMgKGJvb2wpXCIsXG4gIFwiZnVuY3Rpb24gaXNXaGl0ZWxpc3RlZChhZGRyZXNzIGludmVzdG9yKSBleHRlcm5hbCB2aWV3IHJldHVybnMgKGJvb2wpXCIsXG4gIFwiZnVuY3Rpb24gaXNLeWNBcHByb3ZlZChhZGRyZXNzIGludmVzdG9yKSBleHRlcm5hbCB2aWV3IHJldHVybnMgKGJvb2wpXCJcbl07XG5cbmNvbnN0IENsYWltUmVnaXN0cnlBQkkgPSBbXG4gIFwiZnVuY3Rpb24gaXNzdWVDbGFpbShhZGRyZXNzIHN1YmplY3QsIHVpbnQyNTYgdG9waWMsIGJ5dGVzIGNhbGxkYXRhIHNpZ25hdHVyZSwgYnl0ZXMgY2FsbGRhdGEgZGF0YSwgc3RyaW5nIGNhbGxkYXRhIHVyaSwgdWludDI1NiB2YWxpZFVudGlsKSBleHRlcm5hbFwiLFxuICBcImZ1bmN0aW9uIGhhc1ZhbGlkQ2xhaW0oYWRkcmVzcyBzdWJqZWN0LCB1aW50MjU2IHRvcGljKSBleHRlcm5hbCB2aWV3IHJldHVybnMgKGJvb2wpXCJcbl07XG5cbmNvbnN0IENvbXBsaWFuY2VBQkkgPSBbXG4gIFwiZnVuY3Rpb24gY3JlYXRlZChhZGRyZXNzIHRvLCB1aW50MjU2IHZhbHVlKSBleHRlcm5hbFwiLFxuICBcImZ1bmN0aW9uIGNhblRyYW5zZmVyKGFkZHJlc3MgZnJvbSwgYWRkcmVzcyB0bywgdWludDI1NiB2YWx1ZSkgZXh0ZXJuYWwgdmlldyByZXR1cm5zIChib29sKVwiXG5dO1xuXG4vLyBUb2tlbnkgQ2xhaW0gVG9waWNzIChmb2xsb3dpbmcgVG9rZW55IHN0YW5kYXJkKVxuY29uc3QgQ0xBSU1fVE9QSUNTID0ge1xuICBLWUM6IDEsXG4gIEFNTDogMixcbiAgSURFTlRJVFk6IDMsXG4gIFFVQUxJRklDQVRJT046IDQsXG4gIEFDQ1JFRElUQVRJT046IDUsXG4gIFJFU0lERU5DRTogNixcbiAgVE9LRU5fSVNTVUVSOiA3IC8vIEN1c3RvbSBjbGFpbSBmb3IgdG9rZW4gaXNzdWVyc1xufTtcblxuLy8gVG9rZW55LXN0eWxlIGNsYWltIGRhdGEgZm9ybWF0XG4vLyBGb3JtYXQ6IFlZWVlNTURESEhNTVNTICh0aW1lc3RhbXApICsgY291bnRyeSBjb2RlICsgYWRkaXRpb25hbCBkYXRhXG4vLyBFeGFtcGxlOiAxMDEwMTAxMDAwMDY0OCA9IHRpbWVzdGFtcCArIGNvdW50cnkgKyB2ZXJpZmljYXRpb24gbGV2ZWxcbmZ1bmN0aW9uIGdlbmVyYXRlVG9rZW55Q2xhaW0oY291bnRyeTogc3RyaW5nLCBjbGFpbVR5cGU6IHN0cmluZyk6IHN0cmluZyB7XG4gIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XG4gIGNvbnN0IHRpbWVzdGFtcCA9IG5vdy5nZXRGdWxsWWVhcigpLnRvU3RyaW5nKCkuc2xpY2UoLTIpICsgLy8gWVlcbiAgICAgICAgICAgICAgICAgICAobm93LmdldE1vbnRoKCkgKyAxKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJykgKyAvLyBNTVxuICAgICAgICAgICAgICAgICAgIG5vdy5nZXREYXRlKCkudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpICsgLy8gRERcbiAgICAgICAgICAgICAgICAgICBub3cuZ2V0SG91cnMoKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJykgKyAvLyBISFxuICAgICAgICAgICAgICAgICAgIG5vdy5nZXRNaW51dGVzKCkudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpICsgLy8gTU1cbiAgICAgICAgICAgICAgICAgICBub3cuZ2V0U2Vjb25kcygpLnRvU3RyaW5nKCkucGFkU3RhcnQoMiwgJzAnKTsgLy8gU1NcblxuICBjb25zdCBjb3VudHJ5Q29kZSA9IGdldENvdW50cnlDb2RlKGNvdW50cnkpLnRvU3RyaW5nKCkucGFkU3RhcnQoMywgJzAnKTtcblxuICAvLyBBZGRpdGlvbmFsIGRhdGEgYmFzZWQgb24gY2xhaW0gdHlwZVxuICBsZXQgYWRkaXRpb25hbERhdGEgPSAnJztcbiAgc3dpdGNoIChjbGFpbVR5cGUpIHtcbiAgICBjYXNlICdLWUMnOlxuICAgICAgYWRkaXRpb25hbERhdGEgPSAnMDAxJzsgLy8gS1lDIGxldmVsIDFcbiAgICAgIGJyZWFrO1xuICAgIGNhc2UgJ1FVQUxJRklDQVRJT04nOlxuICAgICAgYWRkaXRpb25hbERhdGEgPSAnMDAyJzsgLy8gUXVhbGlmaWVkIGludmVzdG9yXG4gICAgICBicmVhaztcbiAgICBjYXNlICdUT0tFTl9JU1NVRVInOlxuICAgICAgYWRkaXRpb25hbERhdGEgPSAnMDAzJzsgLy8gVG9rZW4gaXNzdWVyXG4gICAgICBicmVhaztcbiAgICBkZWZhdWx0OlxuICAgICAgYWRkaXRpb25hbERhdGEgPSAnMDAwJztcbiAgfVxuXG4gIHJldHVybiB0aW1lc3RhbXAgKyBjb3VudHJ5Q29kZSArIGFkZGl0aW9uYWxEYXRhO1xufVxuXG4vLyBDb3VudHJ5IGNvZGUgbWFwcGluZyAoSVNPLTMxNjYgbnVtZXJpYylcbmNvbnN0IENPVU5UUllfQ09ERVM6IHsgW2tleTogc3RyaW5nXTogbnVtYmVyIH0gPSB7XG4gICdVUyc6IDg0MCwgJ1VTQSc6IDg0MCwgJ1VuaXRlZCBTdGF0ZXMnOiA4NDAsXG4gICdDQSc6IDEyNCwgJ0NhbmFkYSc6IDEyNCxcbiAgJ0dCJzogODI2LCAnVUsnOiA4MjYsICdVbml0ZWQgS2luZ2RvbSc6IDgyNixcbiAgJ0RFJzogMjc2LCAnR2VybWFueSc6IDI3NixcbiAgJ0ZSJzogMjUwLCAnRnJhbmNlJzogMjUwLFxuICAnSVQnOiAzODAsICdJdGFseSc6IDM4MCxcbiAgJ0VTJzogNzI0LCAnU3BhaW4nOiA3MjQsXG4gICdOTCc6IDUyOCwgJ05ldGhlcmxhbmRzJzogNTI4LFxuICAnQ0gnOiA3NTYsICdTd2l0emVybGFuZCc6IDc1NixcbiAgJ0FVJzogMzYsICdBdXN0cmFsaWEnOiAzNixcbiAgJ0pQJzogMzkyLCAnSmFwYW4nOiAzOTIsXG4gICdTRyc6IDcwMiwgJ1NpbmdhcG9yZSc6IDcwMixcbn07XG5cbmZ1bmN0aW9uIGdldENvdW50cnlDb2RlKGNvdW50cnk6IHN0cmluZyk6IG51bWJlciB7XG4gIHJldHVybiBDT1VOVFJZX0NPREVTW2NvdW50cnldIHx8IENPVU5UUllfQ09ERVNbY291bnRyeS50b1VwcGVyQ2FzZSgpXSB8fCA4NDA7IC8vIERlZmF1bHQgdG8gVVNBXG59XG5cbi8qKlxuICogSG9vayBmb3IgRVJDLTM2NDMgaW50ZWdyYXRpb24gZHVyaW5nIHRva2VuIGRlcGxveW1lbnRcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHVzZUVSQzM2NDNJbnRlZ3JhdGlvbigpIHtcbiAgXG4gIC8qKlxuICAgKiBTZXR1cCBFUkMtMzY0MyBjb21wbGlhbmNlIGZvciBhIG5ld2x5IGRlcGxveWVkIHRva2VuXG4gICAqL1xuICBjb25zdCBzZXR1cEVSQzM2NDNDb21wbGlhbmNlID0gYXN5bmMgKFxuICAgIHRva2VuQWRkcmVzczogc3RyaW5nLFxuICAgIG93bmVyQWRkcmVzczogc3RyaW5nLFxuICAgIHNpZ25lcjogZXRoZXJzLlNpZ25lcixcbiAgICB0b2tlbkRhdGE6IHtcbiAgICAgIG5hbWU6IHN0cmluZztcbiAgICAgIHN5bWJvbDogc3RyaW5nO1xuICAgICAgdG9rZW5UeXBlOiBzdHJpbmc7XG4gICAgICBjb3VudHJ5Pzogc3RyaW5nO1xuICAgICAgc2VsZWN0ZWRDbGFpbXM/OiBzdHJpbmdbXTtcbiAgICB9XG4gICkgPT4ge1xuICAgIGNvbnNvbGUubG9nKFwi8J+Pm++4jyBTZXR0aW5nIHVwIEVSQy0zNjQzIGNvbXBsaWFuY2UgZm9yIHRva2VuOlwiLCB0b2tlbkFkZHJlc3MpO1xuICAgIFxuICAgIGNvbnN0IHJlc3VsdHMgPSB7XG4gICAgICBpZGVudGl0eVJlZ2lzdGVyZWQ6IGZhbHNlLFxuICAgICAgd2hpdGVsaXN0ZWQ6IGZhbHNlLFxuICAgICAga3ljQXBwcm92ZWQ6IGZhbHNlLFxuICAgICAgY2xhaW1zSXNzdWVkOiBbXSxcbiAgICAgIGNvbXBsaWFuY2VOb3RpZmllZDogZmFsc2UsXG4gICAgICBlcnJvcnM6IFtdXG4gICAgfTtcblxuICAgIHRyeSB7XG4gICAgICAvLyBHZXQgY29udHJhY3QgYWRkcmVzc2VzIGZyb20gZW52aXJvbm1lbnRcbiAgICAgIGNvbnN0IGlkZW50aXR5UmVnaXN0cnlBZGRyZXNzID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfSURFTlRJVFlfUkVHSVNUUllfQUREUkVTUztcbiAgICAgIGNvbnN0IGNsYWltUmVnaXN0cnlBZGRyZXNzID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQ0xBSU1fUkVHSVNUUllfQUREUkVTUztcbiAgICAgIGNvbnN0IGNvbXBsaWFuY2VBZGRyZXNzID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQ09NUExJQU5DRV9BRERSRVNTO1xuXG4gICAgICBpZiAoIWlkZW50aXR5UmVnaXN0cnlBZGRyZXNzIHx8ICFjbGFpbVJlZ2lzdHJ5QWRkcmVzcyB8fCAhY29tcGxpYW5jZUFkZHJlc3MpIHtcbiAgICAgICAgY29uc29sZS53YXJuKFwi4pqg77iPIEVSQy0zNjQzIGNvbnRyYWN0IGFkZHJlc3NlcyBub3QgY29uZmlndXJlZCwgc2tpcHBpbmcgY29tcGxpYW5jZSBzZXR1cFwiKTtcbiAgICAgICAgcmV0dXJuIHJlc3VsdHM7XG4gICAgICB9XG5cbiAgICAgIC8vIENvbm5lY3QgdG8gY29udHJhY3RzXG4gICAgICBjb25zdCBpZGVudGl0eVJlZ2lzdHJ5ID0gbmV3IGV0aGVycy5Db250cmFjdChpZGVudGl0eVJlZ2lzdHJ5QWRkcmVzcywgSWRlbnRpdHlSZWdpc3RyeUFCSSwgc2lnbmVyKTtcbiAgICAgIGNvbnN0IGNsYWltUmVnaXN0cnkgPSBuZXcgZXRoZXJzLkNvbnRyYWN0KGNsYWltUmVnaXN0cnlBZGRyZXNzLCBDbGFpbVJlZ2lzdHJ5QUJJLCBzaWduZXIpO1xuICAgICAgY29uc3QgY29tcGxpYW5jZSA9IG5ldyBldGhlcnMuQ29udHJhY3QoY29tcGxpYW5jZUFkZHJlc3MsIENvbXBsaWFuY2VBQkksIHNpZ25lcik7XG5cbiAgICAgIC8vIFN0ZXAgMTogUmVnaXN0ZXIgaWRlbnRpdHkgaWYgbm90IGFscmVhZHkgcmVnaXN0ZXJlZFxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgaXNWZXJpZmllZCA9IGF3YWl0IGlkZW50aXR5UmVnaXN0cnkuaXNWZXJpZmllZChvd25lckFkZHJlc3MpO1xuICAgICAgICBpZiAoIWlzVmVyaWZpZWQpIHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhcIvCfk50gUmVnaXN0ZXJpbmcgaWRlbnRpdHkgZm9yIHRva2VuIG93bmVyLi4uXCIpO1xuICAgICAgICAgIGNvbnN0IGNvdW50cnlDb2RlID0gZ2V0Q291bnRyeUNvZGUodG9rZW5EYXRhLmNvdW50cnkgfHwgJ1VTJyk7XG4gICAgICAgICAgY29uc3QgdHgxID0gYXdhaXQgaWRlbnRpdHlSZWdpc3RyeS5yZWdpc3RlcklkZW50aXR5KG93bmVyQWRkcmVzcywgY291bnRyeUNvZGUpO1xuICAgICAgICAgIGF3YWl0IHR4MS53YWl0KCk7XG4gICAgICAgICAgcmVzdWx0cy5pZGVudGl0eVJlZ2lzdGVyZWQgPSB0cnVlO1xuICAgICAgICAgIGNvbnNvbGUubG9nKFwi4pyFIElkZW50aXR5IHJlZ2lzdGVyZWQgc3VjY2Vzc2Z1bGx5XCIpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNvbnNvbGUubG9nKFwi4pyFIElkZW50aXR5IGFscmVhZHkgcmVnaXN0ZXJlZFwiKTtcbiAgICAgICAgICByZXN1bHRzLmlkZW50aXR5UmVnaXN0ZXJlZCA9IHRydWU7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCLinYwgRmFpbGVkIHRvIHJlZ2lzdGVyIGlkZW50aXR5OlwiLCBlcnJvcik7XG4gICAgICAgIHJlc3VsdHMuZXJyb3JzLnB1c2goYElkZW50aXR5IHJlZ2lzdHJhdGlvbiBmYWlsZWQ6ICR7ZXJyb3IubWVzc2FnZX1gKTtcbiAgICAgIH1cblxuICAgICAgLy8gU3RlcCAyOiBBZGQgdG8gd2hpdGVsaXN0IGlmIG5vdCBhbHJlYWR5IHdoaXRlbGlzdGVkXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBpc1doaXRlbGlzdGVkID0gYXdhaXQgaWRlbnRpdHlSZWdpc3RyeS5pc1doaXRlbGlzdGVkKG93bmVyQWRkcmVzcyk7XG4gICAgICAgIGlmICghaXNXaGl0ZWxpc3RlZCkge1xuICAgICAgICAgIGNvbnNvbGUubG9nKFwi8J+TiyBBZGRpbmcgdG8gd2hpdGVsaXN0Li4uXCIpO1xuICAgICAgICAgIGNvbnN0IHR4MiA9IGF3YWl0IGlkZW50aXR5UmVnaXN0cnkuYWRkVG9XaGl0ZWxpc3Qob3duZXJBZGRyZXNzKTtcbiAgICAgICAgICBhd2FpdCB0eDIud2FpdCgpO1xuICAgICAgICAgIHJlc3VsdHMud2hpdGVsaXN0ZWQgPSB0cnVlO1xuICAgICAgICAgIGNvbnNvbGUubG9nKFwi4pyFIEFkZGVkIHRvIHdoaXRlbGlzdCBzdWNjZXNzZnVsbHlcIik7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc29sZS5sb2coXCLinIUgQWxyZWFkeSB3aGl0ZWxpc3RlZFwiKTtcbiAgICAgICAgICByZXN1bHRzLndoaXRlbGlzdGVkID0gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcihcIuKdjCBGYWlsZWQgdG8gYWRkIHRvIHdoaXRlbGlzdDpcIiwgZXJyb3IpO1xuICAgICAgICByZXN1bHRzLmVycm9ycy5wdXNoKGBXaGl0ZWxpc3QgYWRkaXRpb24gZmFpbGVkOiAke2Vycm9yLm1lc3NhZ2V9YCk7XG4gICAgICB9XG5cbiAgICAgIC8vIFN0ZXAgMzogQXBwcm92ZSBLWUMgaWYgbm90IGFscmVhZHkgYXBwcm92ZWRcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGlzS3ljQXBwcm92ZWQgPSBhd2FpdCBpZGVudGl0eVJlZ2lzdHJ5LmlzS3ljQXBwcm92ZWQob3duZXJBZGRyZXNzKTtcbiAgICAgICAgaWYgKCFpc0t5Y0FwcHJvdmVkKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coXCLwn5SNIEFwcHJvdmluZyBLWUMuLi5cIik7XG4gICAgICAgICAgY29uc3QgdHgzID0gYXdhaXQgaWRlbnRpdHlSZWdpc3RyeS5hcHByb3ZlS3ljKG93bmVyQWRkcmVzcyk7XG4gICAgICAgICAgYXdhaXQgdHgzLndhaXQoKTtcbiAgICAgICAgICByZXN1bHRzLmt5Y0FwcHJvdmVkID0gdHJ1ZTtcbiAgICAgICAgICBjb25zb2xlLmxvZyhcIuKchSBLWUMgYXBwcm92ZWQgc3VjY2Vzc2Z1bGx5XCIpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNvbnNvbGUubG9nKFwi4pyFIEtZQyBhbHJlYWR5IGFwcHJvdmVkXCIpO1xuICAgICAgICAgIHJlc3VsdHMua3ljQXBwcm92ZWQgPSB0cnVlO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKFwi4p2MIEZhaWxlZCB0byBhcHByb3ZlIEtZQzpcIiwgZXJyb3IpO1xuICAgICAgICByZXN1bHRzLmVycm9ycy5wdXNoKGBLWUMgYXBwcm92YWwgZmFpbGVkOiAke2Vycm9yLm1lc3NhZ2V9YCk7XG4gICAgICB9XG5cbiAgICAgIC8vIFN0ZXAgNDogSXNzdWUgVG9rZW55LXN0eWxlIGNsYWltcyBmb3IgdG9rZW4gaXNzdWVyXG4gICAgICB0cnkge1xuICAgICAgICBjb25zb2xlLmxvZyhcIvCfk5wgSXNzdWluZyBUb2tlbnktc3R5bGUgY2xhaW1zIGZvciB0b2tlbiBpc3N1ZXIuLi5cIik7XG5cbiAgICAgICAgLy8gR2VuZXJhdGUgVG9rZW55LXN0eWxlIGNsYWltIGRhdGFcbiAgICAgICAgY29uc3Qga3ljQ2xhaW1WYWx1ZSA9IGdlbmVyYXRlVG9rZW55Q2xhaW0odG9rZW5EYXRhLmNvdW50cnkgfHwgJ1VTJywgJ0tZQycpO1xuICAgICAgICBjb25zdCBxdWFsaWZpY2F0aW9uQ2xhaW1WYWx1ZSA9IGdlbmVyYXRlVG9rZW55Q2xhaW0odG9rZW5EYXRhLmNvdW50cnkgfHwgJ1VTJywgJ1FVQUxJRklDQVRJT04nKTtcbiAgICAgICAgY29uc3QgaXNzdWVyQ2xhaW1WYWx1ZSA9IGdlbmVyYXRlVG9rZW55Q2xhaW0odG9rZW5EYXRhLmNvdW50cnkgfHwgJ1VTJywgJ1RPS0VOX0lTU1VFUicpO1xuXG4gICAgICAgIGNvbnNvbGUubG9nKFwi8J+UoiBHZW5lcmF0ZWQgVG9rZW55IGNsYWltczpcIiwge1xuICAgICAgICAgIEtZQzoga3ljQ2xhaW1WYWx1ZSxcbiAgICAgICAgICBRVUFMSUZJQ0FUSU9OOiBxdWFsaWZpY2F0aW9uQ2xhaW1WYWx1ZSxcbiAgICAgICAgICBUT0tFTl9JU1NVRVI6IGlzc3VlckNsYWltVmFsdWVcbiAgICAgICAgfSk7XG5cbiAgICAgICAgLy8gSXNzdWUgS1lDIGNsYWltIHdpdGggVG9rZW55IGZvcm1hdFxuICAgICAgICBjb25zdCBreWNDbGFpbURhdGEgPSBldGhlcnMuQWJpQ29kZXIuZGVmYXVsdEFiaUNvZGVyKCkuZW5jb2RlKFxuICAgICAgICAgIFtcInN0cmluZ1wiLCBcInN0cmluZ1wiLCBcInVpbnQyNTZcIl0sXG4gICAgICAgICAgW2t5Y0NsYWltVmFsdWUsIFwiS1lDX0FQUFJPVkVEXCIsIE1hdGguZmxvb3IoRGF0ZS5ub3coKSAvIDEwMDApXVxuICAgICAgICApO1xuXG4gICAgICAgIGNvbnN0IGhhc0t5Y0NsYWltID0gYXdhaXQgY2xhaW1SZWdpc3RyeS5oYXNWYWxpZENsYWltKG93bmVyQWRkcmVzcywgQ0xBSU1fVE9QSUNTLktZQyk7XG4gICAgICAgIGlmICghaGFzS3ljQ2xhaW0pIHtcbiAgICAgICAgICBjb25zdCB0eDQgPSBhd2FpdCBjbGFpbVJlZ2lzdHJ5Lmlzc3VlQ2xhaW0oXG4gICAgICAgICAgICBvd25lckFkZHJlc3MsXG4gICAgICAgICAgICBDTEFJTV9UT1BJQ1MuS1lDLFxuICAgICAgICAgICAgXCIweFwiLCAvLyBlbXB0eSBzaWduYXR1cmVcbiAgICAgICAgICAgIGt5Y0NsYWltRGF0YSxcbiAgICAgICAgICAgIGBLWUM6JHtreWNDbGFpbVZhbHVlfWAsIC8vIFVSSSB3aXRoIFRva2VueSBmb3JtYXRcbiAgICAgICAgICAgIDAgLy8gbmV2ZXIgZXhwaXJlc1xuICAgICAgICAgICk7XG4gICAgICAgICAgYXdhaXQgdHg0LndhaXQoKTtcbiAgICAgICAgICByZXN1bHRzLmNsYWltc0lzc3VlZC5wdXNoKGBLWUM6JHtreWNDbGFpbVZhbHVlfWApO1xuICAgICAgICAgIGNvbnNvbGUubG9nKGDinIUgS1lDIGNsYWltIGlzc3VlZDogJHtreWNDbGFpbVZhbHVlfWApO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNvbnNvbGUubG9nKFwi4pyFIEtZQyBjbGFpbSBhbHJlYWR5IGV4aXN0c1wiKTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIElzc3VlIHF1YWxpZmljYXRpb24gY2xhaW0gd2l0aCBUb2tlbnkgZm9ybWF0XG4gICAgICAgIGNvbnN0IHF1YWxpZmljYXRpb25DbGFpbURhdGEgPSBldGhlcnMuQWJpQ29kZXIuZGVmYXVsdEFiaUNvZGVyKCkuZW5jb2RlKFxuICAgICAgICAgIFtcInN0cmluZ1wiLCBcInN0cmluZ1wiLCBcInVpbnQyNTZcIl0sXG4gICAgICAgICAgW3F1YWxpZmljYXRpb25DbGFpbVZhbHVlLCBcIlFVQUxJRklFRF9JTlZFU1RPUlwiLCBNYXRoLmZsb29yKERhdGUubm93KCkgLyAxMDAwKV1cbiAgICAgICAgKTtcblxuICAgICAgICBjb25zdCBoYXNRdWFsaWZpY2F0aW9uQ2xhaW0gPSBhd2FpdCBjbGFpbVJlZ2lzdHJ5Lmhhc1ZhbGlkQ2xhaW0ob3duZXJBZGRyZXNzLCBDTEFJTV9UT1BJQ1MuUVVBTElGSUNBVElPTik7XG4gICAgICAgIGlmICghaGFzUXVhbGlmaWNhdGlvbkNsYWltKSB7XG4gICAgICAgICAgY29uc3QgdHg1ID0gYXdhaXQgY2xhaW1SZWdpc3RyeS5pc3N1ZUNsYWltKFxuICAgICAgICAgICAgb3duZXJBZGRyZXNzLFxuICAgICAgICAgICAgQ0xBSU1fVE9QSUNTLlFVQUxJRklDQVRJT04sXG4gICAgICAgICAgICBcIjB4XCIsXG4gICAgICAgICAgICBxdWFsaWZpY2F0aW9uQ2xhaW1EYXRhLFxuICAgICAgICAgICAgYFFVQUxJRklDQVRJT046JHtxdWFsaWZpY2F0aW9uQ2xhaW1WYWx1ZX1gLFxuICAgICAgICAgICAgMFxuICAgICAgICAgICk7XG4gICAgICAgICAgYXdhaXQgdHg1LndhaXQoKTtcbiAgICAgICAgICByZXN1bHRzLmNsYWltc0lzc3VlZC5wdXNoKGBRVUFMSUZJQ0FUSU9OOiR7cXVhbGlmaWNhdGlvbkNsYWltVmFsdWV9YCk7XG4gICAgICAgICAgY29uc29sZS5sb2coYOKchSBRdWFsaWZpY2F0aW9uIGNsYWltIGlzc3VlZDogJHtxdWFsaWZpY2F0aW9uQ2xhaW1WYWx1ZX1gKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhcIuKchSBRdWFsaWZpY2F0aW9uIGNsYWltIGFscmVhZHkgZXhpc3RzXCIpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gSXNzdWUgdG9rZW4gaXNzdWVyIGNsYWltIHdpdGggVG9rZW55IGZvcm1hdFxuICAgICAgICBjb25zdCBpc3N1ZXJDbGFpbURhdGEgPSBldGhlcnMuQWJpQ29kZXIuZGVmYXVsdEFiaUNvZGVyKCkuZW5jb2RlKFxuICAgICAgICAgIFtcInN0cmluZ1wiLCBcInN0cmluZ1wiLCBcInN0cmluZ1wiLCBcInN0cmluZ1wiLCBcInVpbnQyNTZcIl0sXG4gICAgICAgICAgW2lzc3VlckNsYWltVmFsdWUsIHRva2VuRGF0YS5uYW1lLCB0b2tlbkRhdGEuc3ltYm9sLCB0b2tlbkRhdGEudG9rZW5UeXBlLCBNYXRoLmZsb29yKERhdGUubm93KCkgLyAxMDAwKV1cbiAgICAgICAgKTtcblxuICAgICAgICBjb25zdCB0eDYgPSBhd2FpdCBjbGFpbVJlZ2lzdHJ5Lmlzc3VlQ2xhaW0oXG4gICAgICAgICAgb3duZXJBZGRyZXNzLFxuICAgICAgICAgIENMQUlNX1RPUElDUy5UT0tFTl9JU1NVRVIsXG4gICAgICAgICAgXCIweFwiLFxuICAgICAgICAgIGlzc3VlckNsYWltRGF0YSxcbiAgICAgICAgICBgVE9LRU5fSVNTVUVSOiR7aXNzdWVyQ2xhaW1WYWx1ZX06JHt0b2tlbkRhdGEuc3ltYm9sfWAsXG4gICAgICAgICAgMFxuICAgICAgICApO1xuICAgICAgICBhd2FpdCB0eDYud2FpdCgpO1xuICAgICAgICByZXN1bHRzLmNsYWltc0lzc3VlZC5wdXNoKGBUT0tFTl9JU1NVRVI6JHtpc3N1ZXJDbGFpbVZhbHVlfWApO1xuICAgICAgICBjb25zb2xlLmxvZyhg4pyFIFRva2VuIGlzc3VlciBjbGFpbSBpc3N1ZWQ6ICR7aXNzdWVyQ2xhaW1WYWx1ZX1gKTtcblxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcihcIuKdjCBGYWlsZWQgdG8gaXNzdWUgY2xhaW1zOlwiLCBlcnJvcik7XG4gICAgICAgIHJlc3VsdHMuZXJyb3JzLnB1c2goYENsYWltcyBpc3N1YW5jZSBmYWlsZWQ6ICR7ZXJyb3IubWVzc2FnZX1gKTtcbiAgICAgIH1cblxuICAgICAgLy8gU3RlcCA1OiBOb3RpZnkgY29tcGxpYW5jZSBjb250cmFjdCAoaWYgbmVlZGVkKVxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc29sZS5sb2coXCLimpbvuI8gTm90aWZ5aW5nIGNvbXBsaWFuY2UgY29udHJhY3QuLi5cIik7XG4gICAgICAgIC8vIFRoaXMgd291bGQgdHlwaWNhbGx5IGJlIGNhbGxlZCB3aGVuIHRva2VucyBhcmUgbWludGVkLCBidXQgd2UgY2FuIHByZXBhcmUgaXRcbiAgICAgICAgY29uc3QgY2FuVHJhbnNmZXIgPSBhd2FpdCBjb21wbGlhbmNlLmNhblRyYW5zZmVyKG93bmVyQWRkcmVzcywgb3duZXJBZGRyZXNzLCAxKTtcbiAgICAgICAgY29uc29sZS5sb2coXCLinIUgQ29tcGxpYW5jZSBjaGVjayBwYXNzZWQ6XCIsIGNhblRyYW5zZmVyKTtcbiAgICAgICAgcmVzdWx0cy5jb21wbGlhbmNlTm90aWZpZWQgPSB0cnVlO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcihcIuKdjCBDb21wbGlhbmNlIG5vdGlmaWNhdGlvbiBmYWlsZWQ6XCIsIGVycm9yKTtcbiAgICAgICAgcmVzdWx0cy5lcnJvcnMucHVzaChgQ29tcGxpYW5jZSBub3RpZmljYXRpb24gZmFpbGVkOiAke2Vycm9yLm1lc3NhZ2V9YCk7XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKFwi8J+OiSBFUkMtMzY0MyBjb21wbGlhbmNlIHNldHVwIGNvbXBsZXRlZCFcIik7XG4gICAgICBjb25zb2xlLmxvZyhcIlJlc3VsdHM6XCIsIHtcbiAgICAgICAgaWRlbnRpdHlSZWdpc3RlcmVkOiByZXN1bHRzLmlkZW50aXR5UmVnaXN0ZXJlZCxcbiAgICAgICAgd2hpdGVsaXN0ZWQ6IHJlc3VsdHMud2hpdGVsaXN0ZWQsXG4gICAgICAgIGt5Y0FwcHJvdmVkOiByZXN1bHRzLmt5Y0FwcHJvdmVkLFxuICAgICAgICBjbGFpbXNJc3N1ZWQ6IHJlc3VsdHMuY2xhaW1zSXNzdWVkLFxuICAgICAgICBjb21wbGlhbmNlTm90aWZpZWQ6IHJlc3VsdHMuY29tcGxpYW5jZU5vdGlmaWVkLFxuICAgICAgICBlcnJvckNvdW50OiByZXN1bHRzLmVycm9ycy5sZW5ndGhcbiAgICAgIH0pO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCLinYwgRVJDLTM2NDMgc2V0dXAgZmFpbGVkOlwiLCBlcnJvcik7XG4gICAgICByZXN1bHRzLmVycm9ycy5wdXNoKGBHZW5lcmFsIHNldHVwIGZhaWxlZDogJHtlcnJvci5tZXNzYWdlfWApO1xuICAgIH1cblxuICAgIHJldHVybiByZXN1bHRzO1xuICB9O1xuXG4gIC8qKlxuICAgKiBDaGVjayBpZiBFUkMtMzY0MyBjb250cmFjdHMgYXJlIGF2YWlsYWJsZVxuICAgKi9cbiAgY29uc3QgaXNFUkMzNjQzQXZhaWxhYmxlID0gKCkgPT4ge1xuICAgIHJldHVybiAhIShcbiAgICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0lERU5USVRZX1JFR0lTVFJZX0FERFJFU1MgJiZcbiAgICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0NMQUlNX1JFR0lTVFJZX0FERFJFU1MgJiZcbiAgICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0NPTVBMSUFOQ0VfQUREUkVTU1xuICAgICk7XG4gIH07XG5cbiAgLyoqXG4gICAqIEdldCBFUkMtMzY0MyBjb250cmFjdCBhZGRyZXNzZXNcbiAgICovXG4gIGNvbnN0IGdldEVSQzM2NDNBZGRyZXNzZXMgPSAoKSA9PiB7XG4gICAgcmV0dXJuIHtcbiAgICAgIGlkZW50aXR5UmVnaXN0cnk6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0lERU5USVRZX1JFR0lTVFJZX0FERFJFU1MsXG4gICAgICBjbGFpbVJlZ2lzdHJ5OiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19DTEFJTV9SRUdJU1RSWV9BRERSRVNTLFxuICAgICAgY29tcGxpYW5jZTogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQ09NUExJQU5DRV9BRERSRVNTXG4gICAgfTtcbiAgfTtcblxuICByZXR1cm4ge1xuICAgIHNldHVwRVJDMzY0M0NvbXBsaWFuY2UsXG4gICAgaXNFUkMzNjQzQXZhaWxhYmxlLFxuICAgIGdldEVSQzM2NDNBZGRyZXNzZXNcbiAgfTtcbn1cbiJdLCJuYW1lcyI6WyJldGhlcnMiLCJJZGVudGl0eVJlZ2lzdHJ5QUJJIiwiQ2xhaW1SZWdpc3RyeUFCSSIsIkNvbXBsaWFuY2VBQkkiLCJDTEFJTV9UT1BJQ1MiLCJLWUMiLCJBTUwiLCJJREVOVElUWSIsIlFVQUxJRklDQVRJT04iLCJBQ0NSRURJVEFUSU9OIiwiUkVTSURFTkNFIiwiVE9LRU5fSVNTVUVSIiwiZ2VuZXJhdGVUb2tlbnlDbGFpbSIsImNvdW50cnkiLCJjbGFpbVR5cGUiLCJub3ciLCJEYXRlIiwidGltZXN0YW1wIiwiZ2V0RnVsbFllYXIiLCJ0b1N0cmluZyIsInNsaWNlIiwiZ2V0TW9udGgiLCJwYWRTdGFydCIsImdldERhdGUiLCJnZXRIb3VycyIsImdldE1pbnV0ZXMiLCJnZXRTZWNvbmRzIiwiY291bnRyeUNvZGUiLCJnZXRDb3VudHJ5Q29kZSIsImFkZGl0aW9uYWxEYXRhIiwiQ09VTlRSWV9DT0RFUyIsInRvVXBwZXJDYXNlIiwidXNlRVJDMzY0M0ludGVncmF0aW9uIiwic2V0dXBFUkMzNjQzQ29tcGxpYW5jZSIsInRva2VuQWRkcmVzcyIsIm93bmVyQWRkcmVzcyIsInNpZ25lciIsInRva2VuRGF0YSIsImNvbnNvbGUiLCJsb2ciLCJyZXN1bHRzIiwiaWRlbnRpdHlSZWdpc3RlcmVkIiwid2hpdGVsaXN0ZWQiLCJreWNBcHByb3ZlZCIsImNsYWltc0lzc3VlZCIsImNvbXBsaWFuY2VOb3RpZmllZCIsImVycm9ycyIsImlkZW50aXR5UmVnaXN0cnlBZGRyZXNzIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0lERU5USVRZX1JFR0lTVFJZX0FERFJFU1MiLCJjbGFpbVJlZ2lzdHJ5QWRkcmVzcyIsIk5FWFRfUFVCTElDX0NMQUlNX1JFR0lTVFJZX0FERFJFU1MiLCJjb21wbGlhbmNlQWRkcmVzcyIsIk5FWFRfUFVCTElDX0NPTVBMSUFOQ0VfQUREUkVTUyIsIndhcm4iLCJpZGVudGl0eVJlZ2lzdHJ5IiwiQ29udHJhY3QiLCJjbGFpbVJlZ2lzdHJ5IiwiY29tcGxpYW5jZSIsImlzVmVyaWZpZWQiLCJ0eDEiLCJyZWdpc3RlcklkZW50aXR5Iiwid2FpdCIsImVycm9yIiwicHVzaCIsIm1lc3NhZ2UiLCJpc1doaXRlbGlzdGVkIiwidHgyIiwiYWRkVG9XaGl0ZWxpc3QiLCJpc0t5Y0FwcHJvdmVkIiwidHgzIiwiYXBwcm92ZUt5YyIsImt5Y0NsYWltVmFsdWUiLCJxdWFsaWZpY2F0aW9uQ2xhaW1WYWx1ZSIsImlzc3VlckNsYWltVmFsdWUiLCJreWNDbGFpbURhdGEiLCJBYmlDb2RlciIsImRlZmF1bHRBYmlDb2RlciIsImVuY29kZSIsIk1hdGgiLCJmbG9vciIsImhhc0t5Y0NsYWltIiwiaGFzVmFsaWRDbGFpbSIsInR4NCIsImlzc3VlQ2xhaW0iLCJxdWFsaWZpY2F0aW9uQ2xhaW1EYXRhIiwiaGFzUXVhbGlmaWNhdGlvbkNsYWltIiwidHg1IiwiaXNzdWVyQ2xhaW1EYXRhIiwibmFtZSIsInN5bWJvbCIsInRva2VuVHlwZSIsInR4NiIsImNhblRyYW5zZmVyIiwiZXJyb3JDb3VudCIsImxlbmd0aCIsImlzRVJDMzY0M0F2YWlsYWJsZSIsImdldEVSQzM2NDNBZGRyZXNzZXMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/create-token/hooks/useERC3643Integration.ts\n"));

/***/ })

});