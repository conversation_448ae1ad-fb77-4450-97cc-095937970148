import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET /api/claims - Get claims for an address
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const walletAddress = searchParams.get('walletAddress');
    const claimType = searchParams.get('claimType');

    if (!walletAddress) {
      return NextResponse.json(
        { error: 'Wallet address is required' },
        { status: 400 }
      );
    }

    // Get claim registry address from environment or database
    const claimRegistryAddress = process.env.CLAIM_REGISTRY_ADDRESS;
    if (!claimRegistryAddress) {
      return NextResponse.json(
        { error: 'Claim registry not configured' },
        { status: 500 }
      );
    }

    // Connect to blockchain
    const provider = new ethers.JsonRpcProvider(process.env.POLYGON_AMOY_RPC_URL);
    const claimRegistryABI = [
      "function hasValidClaim(address subject, uint256 claimType) external view returns (bool)",
      "function getClaimIds(address subject, uint256 claimType) external view returns (bytes32[])",
      "function getClaim(address subject, uint256 claimType, bytes32 claimId) external view returns (tuple(uint256 claimType, address issuer, bytes signature, bytes data, string uri, uint256 issuedAt, uint256 expiresAt, bool revoked))"
    ];

    const claimRegistry = new ethers.Contract(claimRegistryAddress, claimRegistryABI, provider);

    // Claim types
    const CLAIM_TYPES = {
      KYC_CLAIM: 1,
      ACCREDITED_INVESTOR_CLAIM: 2,
      JURISDICTION_CLAIM: 3,
      QUALIFICATION_CLAIM: 4
    };

    const claims = [];

    // If specific claim type requested
    if (claimType && CLAIM_TYPES[claimType as keyof typeof CLAIM_TYPES]) {
      const typeId = CLAIM_TYPES[claimType as keyof typeof CLAIM_TYPES];
      const hasValid = await claimRegistry.hasValidClaim(walletAddress, typeId);
      const claimIds = await claimRegistry.getClaimIds(walletAddress, typeId);

      for (const claimId of claimIds) {
        try {
          const claim = await claimRegistry.getClaim(walletAddress, typeId, claimId);
          claims.push({
            claimId,
            claimType: claimType,
            claimTypeId: typeId,
            issuer: claim.issuer,
            issuedAt: new Date(Number(claim.issuedAt) * 1000),
            expiresAt: claim.expiresAt > 0 ? new Date(Number(claim.expiresAt) * 1000) : null,
            revoked: claim.revoked,
            valid: !claim.revoked && (claim.expiresAt === 0n || claim.expiresAt > BigInt(Math.floor(Date.now() / 1000))),
            data: claim.data,
            uri: claim.uri
          });
        } catch (error) {
          console.error('Error fetching claim details:', error);
        }
      }
    } else {
      // Get all claim types
      for (const [typeName, typeId] of Object.entries(CLAIM_TYPES)) {
        try {
          const hasValid = await claimRegistry.hasValidClaim(walletAddress, typeId);
          const claimIds = await claimRegistry.getClaimIds(walletAddress, typeId);

          for (const claimId of claimIds) {
            try {
              const claim = await claimRegistry.getClaim(walletAddress, typeId, claimId);
              claims.push({
                claimId,
                claimType: typeName,
                claimTypeId: typeId,
                issuer: claim.issuer,
                issuedAt: new Date(Number(claim.issuedAt) * 1000),
                expiresAt: claim.expiresAt > 0 ? new Date(Number(claim.expiresAt) * 1000) : null,
                revoked: claim.revoked,
                valid: !claim.revoked && (claim.expiresAt === 0n || claim.expiresAt > BigInt(Math.floor(Date.now() / 1000))),
                data: claim.data,
                uri: claim.uri
              });
            } catch (error) {
              console.error('Error fetching claim details:', error);
            }
          }
        } catch (error) {
          console.error(`Error checking ${typeName} claims:`, error);
        }
      }
    }

    return NextResponse.json({
      walletAddress,
      claims,
      totalClaims: claims.length,
      validClaims: claims.filter(c => c.valid).length
    });

  } catch (error) {
    console.error('Error fetching claims:', error);
    return NextResponse.json(
      { error: 'Failed to fetch claims' },
      { status: 500 }
    );
  }
}

// POST /api/claims - Issue a new claim
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { walletAddress, claimType, data, uri, expiresAt } = body;

    if (!walletAddress || !claimType) {
      return NextResponse.json(
        { error: 'Wallet address and claim type are required' },
        { status: 400 }
      );
    }

    // Get claim registry address
    const claimRegistryAddress = process.env.CLAIM_REGISTRY_ADDRESS;
    if (!claimRegistryAddress) {
      return NextResponse.json(
        { error: 'Claim registry not configured' },
        { status: 500 }
      );
    }

    // Connect to blockchain with admin wallet
    const provider = new ethers.JsonRpcProvider(process.env.POLYGON_AMOY_RPC_URL);
    const adminWallet = new ethers.Wallet(process.env.CONTRACT_ADMIN_PRIVATE_KEY!, provider);

    const claimRegistryABI = [
      "function issueClaim(address subject, uint256 claimType, bytes calldata signature, bytes calldata data, string calldata uri, uint256 expiresAt) external returns (bytes32)"
    ];

    const claimRegistry = new ethers.Contract(claimRegistryAddress, claimRegistryABI, adminWallet);

    // Claim types
    const CLAIM_TYPES = {
      KYC_CLAIM: 1,
      ACCREDITED_INVESTOR_CLAIM: 2,
      JURISDICTION_CLAIM: 3,
      QUALIFICATION_CLAIM: 4
    };

    const claimTypeId = CLAIM_TYPES[claimType as keyof typeof CLAIM_TYPES];
    if (!claimTypeId) {
      return NextResponse.json(
        { error: 'Invalid claim type' },
        { status: 400 }
      );
    }

    // Issue the claim
    const encodedData = ethers.AbiCoder.defaultAbiCoder().encode(
      ['string', 'uint256'],
      [data || 'APPROVED', Math.floor(Date.now() / 1000)]
    );

    const expirationTimestamp = expiresAt ? Math.floor(new Date(expiresAt).getTime() / 1000) : 0;

    const tx = await claimRegistry.issueClaim(
      walletAddress,
      claimTypeId,
      '0x', // empty signature for admin-issued claims
      encodedData,
      uri || '',
      expirationTimestamp
    );

    const receipt = await tx.wait();

    return NextResponse.json({
      success: true,
      transactionHash: receipt.transactionHash,
      walletAddress,
      claimType,
      claimTypeId,
      message: 'Claim issued successfully'
    });

  } catch (error) {
    console.error('Error issuing claim:', error);
    return NextResponse.json(
      { error: 'Failed to issue claim' },
      { status: 500 }
    );
  }
}
