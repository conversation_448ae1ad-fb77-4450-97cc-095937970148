'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface Claim {
  claimId: string;
  claimType: string;
  claimTypeId: number;
  issuer: string;
  issuedAt: string;
  expiresAt: string | null;
  revoked: boolean;
  valid: boolean;
  data: string;
  uri: string;
}

interface ClaimResponse {
  walletAddress: string;
  claims: Claim[];
  totalClaims: number;
  validClaims: number;
}

export default function ClaimsPage() {
  const [walletAddress, setWalletAddress] = useState('');
  const [claims, setClaims] = useState<Claim[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [issuingClaim, setIssuingClaim] = useState(false);
  const [newClaim, setNewClaim] = useState({
    claimType: 'KYC_CLAIM',
    data: '',
    uri: '',
    expiresAt: ''
  });

  const claimTypes = [
    { value: 'KYC_CLAIM', label: 'KYC Verification', id: 1 },
    { value: 'ACCREDITED_INVESTOR_CLAIM', label: 'Accredited Investor', id: 2 },
    { value: 'JURISDICTION_CLAIM', label: 'Jurisdiction Verification', id: 3 },
    { value: 'QUALIFICATION_CLAIM', label: 'General Qualification', id: 4 }
  ];

  const fetchClaims = async () => {
    if (!walletAddress) return;

    setLoading(true);
    setError('');

    try {
      const response = await fetch(`/api/claims?walletAddress=${walletAddress}`);
      const data: ClaimResponse = await response.json();

      if (response.ok) {
        setClaims(data.claims);
      } else {
        setError(data.error || 'Failed to fetch claims');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  const issueClaim = async () => {
    if (!walletAddress || !newClaim.claimType) return;

    setIssuingClaim(true);
    setError('');

    try {
      const response = await fetch('/api/claims', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          walletAddress,
          claimType: newClaim.claimType,
          data: newClaim.data || 'APPROVED',
          uri: newClaim.uri,
          expiresAt: newClaim.expiresAt || null
        }),
      });

      const data = await response.json();

      if (response.ok) {
        alert(`✅ Claim issued successfully!\nTransaction: ${data.transactionHash}`);
        // Refresh claims
        await fetchClaims();
        // Reset form
        setNewClaim({
          claimType: 'KYC_CLAIM',
          data: '',
          uri: '',
          expiresAt: ''
        });
      } else {
        setError(data.error || 'Failed to issue claim');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setIssuingClaim(false);
    }
  };

  const getClaimTypeLabel = (claimType: string) => {
    const type = claimTypes.find(t => t.value === claimType);
    return type ? type.label : claimType;
  };

  const getStatusBadge = (claim: Claim) => {
    if (claim.revoked) {
      return <span className="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Revoked</span>;
    }
    if (claim.valid) {
      return <span className="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Valid</span>;
    }
    return <span className="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Expired</span>;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">Blockchain Claims Management</h1>
          <Link
            href="/clients"
            className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm"
          >
            Back to Clients
          </Link>
        </div>

        {/* Search Section */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Search Claims</h2>
          <div className="flex space-x-4">
            <div className="flex-1">
              <label htmlFor="walletAddress" className="block text-sm font-medium text-gray-700 mb-2">
                Wallet Address
              </label>
              <input
                type="text"
                id="walletAddress"
                value={walletAddress}
                onChange={(e) => setWalletAddress(e.target.value)}
                placeholder="0x..."
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              />
            </div>
            <div className="flex items-end">
              <button
                onClick={fetchClaims}
                disabled={loading || !walletAddress}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm disabled:opacity-50"
              >
                {loading ? 'Searching...' : 'Search Claims'}
              </button>
            </div>
          </div>
        </div>

        {/* Issue New Claim Section */}
        {walletAddress && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Issue New Claim</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Claim Type</label>
                <select
                  value={newClaim.claimType}
                  onChange={(e) => setNewClaim({ ...newClaim, claimType: e.target.value })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                >
                  {claimTypes.map(type => (
                    <option key={type.value} value={type.value}>{type.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Data</label>
                <input
                  type="text"
                  value={newClaim.data}
                  onChange={(e) => setNewClaim({ ...newClaim, data: e.target.value })}
                  placeholder="APPROVED"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">URI (optional)</label>
                <input
                  type="text"
                  value={newClaim.uri}
                  onChange={(e) => setNewClaim({ ...newClaim, uri: e.target.value })}
                  placeholder="https://..."
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Expires At (optional)</label>
                <input
                  type="datetime-local"
                  value={newClaim.expiresAt}
                  onChange={(e) => setNewClaim({ ...newClaim, expiresAt: e.target.value })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                />
              </div>
            </div>
            <button
              onClick={issueClaim}
              disabled={issuingClaim || !walletAddress}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm disabled:opacity-50"
            >
              {issuingClaim ? 'Issuing Claim...' : 'Issue Claim'}
            </button>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="text-red-800">{error}</div>
          </div>
        )}

        {/* Claims Table */}
        {claims.length > 0 && (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">
                Claims for {walletAddress.slice(0, 6)}...{walletAddress.slice(-4)}
              </h2>
              <p className="text-sm text-gray-600">
                {claims.filter(c => c.valid).length} valid claims out of {claims.length} total
              </p>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Claim Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Issued At
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Expires At
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Issuer
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Claim ID
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {claims.map((claim, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {getClaimTypeLabel(claim.claimType)}
                        </div>
                        <div className="text-sm text-gray-500">ID: {claim.claimTypeId}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(claim)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(claim.issuedAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {claim.expiresAt ? formatDate(claim.expiresAt) : 'Never'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">
                        {claim.issuer.slice(0, 6)}...{claim.issuer.slice(-4)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">
                        {claim.claimId.slice(0, 8)}...{claim.claimId.slice(-8)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* No Claims Message */}
        {walletAddress && !loading && claims.length === 0 && !error && (
          <div className="text-center py-8 text-gray-500">
            No claims found for this wallet address.
          </div>
        )}
      </div>
    </div>
  );
}
