/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/tokens/route";
exports.ids = ["app/api/tokens/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftokens%2Froute&page=%2Fapi%2Ftokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftokens%2Froute&page=%2Fapi%2Ftokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_tokens_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/tokens/route.ts */ \"(rsc)/./src/app/api/tokens/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/tokens/route\",\n        pathname: \"/api/tokens\",\n        filename: \"route\",\n        bundlePath: \"app/api/tokens/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\tokens\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_tokens_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftokens%2Froute&page=%2Fapi%2Ftokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/tokens/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/tokens/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/constants/addresses.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contracts/SecurityTokenFactory.json */ \"(rsc)/./src/contracts/SecurityTokenFactory.json\");\n/* harmony import */ var _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../contracts/SecurityToken.json */ \"(rsc)/./src/contracts/SecurityToken.json\");\n\n\n\n\n\n// RPC URLs for different networks\nconst RPC_URLS = {\n    amoy: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/',\n    polygon: process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com',\n    unknown: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/'\n};\n// Factory contract address\nconst FACTORY_ADDRESS = process.env.FACTORY_ADDRESS || '******************************************';\n// Helper function to determine if an address looks like a real contract address\nfunction isRealContractAddress(address) {\n    // Skip obvious test addresses\n    if (!address || address === ethers__WEBPACK_IMPORTED_MODULE_4__.ZeroAddress) {\n        return false;\n    }\n    // Skip addresses that are clearly test patterns (all zeros with small numbers)\n    const testPatterns = [\n        /^0x0+[0-9a-f]{1,2}$/i,\n        /^******************************************$/i,\n        /^0x0+$/i // All zeros\n    ];\n    return !testPatterns.some((pattern)=>pattern.test(address));\n}\n// GET /api/tokens - Get all tokens from database and optionally sync with blockchain\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const source = searchParams.get('source') || 'database'; // 'database', 'blockchain', or 'both'\n        if (source === 'database' || source === 'both') {\n            // Get tokens from database\n            const dbTokens = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.token.findMany({\n                orderBy: {\n                    createdAt: 'desc'\n                },\n                include: {\n                    transactions: {\n                        take: 5,\n                        orderBy: {\n                            createdAt: 'desc'\n                        }\n                    }\n                }\n            });\n            if (source === 'database') {\n                // Sync totalSupply from blockchain for database tokens if needed\n                const tokensWithUpdatedSupply = await Promise.all(dbTokens.map(async (token)=>{\n                    // Only attempt blockchain sync for tokens that:\n                    // 1. Have totalSupply of 0 or not set\n                    // 2. Have a real-looking address (not test addresses)\n                    // 3. Are on a supported network\n                    const shouldSyncFromBlockchain = (token.totalSupply === '0' || !token.totalSupply) && isRealContractAddress(token.address) && (token.network === 'amoy' || token.network === 'polygon');\n                    if (shouldSyncFromBlockchain) {\n                        try {\n                            const network = token.network || 'amoy';\n                            const rpcUrl = RPC_URLS[network] || RPC_URLS.amoy;\n                            const provider = new ethers__WEBPACK_IMPORTED_MODULE_5__.JsonRpcProvider(rpcUrl);\n                            // First check if there's code at this address\n                            const code = await provider.getCode(token.address);\n                            if (code === '0x') {\n                                console.log(`Skipping blockchain sync for ${token.address} - no contract deployed`);\n                                return token;\n                            }\n                            const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(token.address, _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__.abi, provider);\n                            const totalSupplyRaw = await tokenContract.totalSupply();\n                            const decimals = token.decimals || 18;\n                            const totalSupply = decimals === 0 ? totalSupplyRaw.toString() : ethers__WEBPACK_IMPORTED_MODULE_7__.formatUnits(totalSupplyRaw, decimals);\n                            console.log(`Successfully synced totalSupply for ${token.symbol}: ${totalSupply}`);\n                            // Update the database with the fetched totalSupply\n                            await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.token.update({\n                                where: {\n                                    id: token.id\n                                },\n                                data: {\n                                    totalSupply\n                                }\n                            });\n                            return {\n                                ...token,\n                                totalSupply\n                            };\n                        } catch (error) {\n                            console.warn(`Could not fetch totalSupply for token ${token.address}: ${error.message}`);\n                            return token;\n                        }\n                    }\n                    return token;\n                }));\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(tokensWithUpdatedSupply);\n            }\n            // If 'both', we'll merge with blockchain data below\n            if (source === 'both') {\n                // For now, just return database tokens\n                // TODO: Implement blockchain sync if needed\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(dbTokens);\n            }\n        }\n        // Blockchain-only fetch (legacy behavior)\n        const network = 'amoy'; // Default to amoy network\n        const rpcUrl = RPC_URLS[network];\n        // Create provider\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_5__.JsonRpcProvider(rpcUrl);\n        // Create factory contract instance\n        const factory = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(FACTORY_ADDRESS, _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_2__.abi, provider);\n        console.log(`Fetching tokens from factory: ${FACTORY_ADDRESS}`);\n        // Get token count\n        const tokenCount = await factory.getTokenCount();\n        console.log(`Factory reports ${tokenCount} deployed tokens`);\n        if (tokenCount === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json([]);\n        }\n        // Get all token addresses\n        const tokenAddresses = await factory.getAllDeployedTokens();\n        console.log(\"Retrieved token addresses:\", tokenAddresses);\n        // Load details for each token\n        const tokens = [];\n        for (const address of tokenAddresses){\n            try {\n                const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_6__.Contract(address, _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__.abi, provider);\n                // Get basic token info\n                const [name, symbol, totalSupply, decimals, owner] = await Promise.all([\n                    tokenContract.name(),\n                    tokenContract.symbol(),\n                    tokenContract.totalSupply(),\n                    tokenContract.decimals(),\n                    tokenContract.owner()\n                ]);\n                // Get additional token details\n                let tokenType = 'UNKNOWN';\n                let securityType = 'UNKNOWN';\n                let createdAt = new Date().toISOString();\n                try {\n                    // Try to get token metadata if available\n                    const tokenDetails = await tokenContract.tokenDetails();\n                    if (tokenDetails) {\n                        // Parse token details if it's a JSON string\n                        try {\n                            const parsed = JSON.parse(tokenDetails);\n                            tokenType = parsed.tokenType || 'UNKNOWN';\n                            securityType = parsed.securityType || 'UNKNOWN';\n                        } catch  {\n                            // If not JSON, use as is\n                            tokenType = 'EQUITY'; // Default\n                            securityType = 'REGULATION_D'; // Default\n                        }\n                    }\n                } catch (error) {\n                    console.warn(`Could not fetch token details for ${address}:`, error);\n                }\n                // Try to get creation timestamp from events\n                try {\n                    const filter = factory.filters.TokenDeployed(null, address);\n                    const events = await factory.queryFilter(filter, 0, 'latest');\n                    if (events.length > 0) {\n                        const block = await provider.getBlock(events[0].blockNumber);\n                        if (block) {\n                            createdAt = new Date(block.timestamp * 1000).toISOString();\n                        }\n                    }\n                } catch (error) {\n                    console.warn(`Could not fetch creation time for ${address}:`, error);\n                }\n                tokens.push({\n                    address,\n                    name,\n                    symbol,\n                    decimals: Number(decimals),\n                    totalSupply: totalSupply.toString(),\n                    owner,\n                    securityType,\n                    tokenType,\n                    createdAt\n                });\n            } catch (error) {\n                console.warn(`Failed to load token details for ${address}:`, error);\n                // Add minimal token info even if details fail\n                tokens.push({\n                    address,\n                    name: 'Unknown Token',\n                    symbol: 'UNKNOWN',\n                    decimals: 0,\n                    totalSupply: '0',\n                    owner: ethers__WEBPACK_IMPORTED_MODULE_4__.ZeroAddress,\n                    securityType: 'UNKNOWN',\n                    tokenType: 'UNKNOWN',\n                    createdAt: new Date().toISOString()\n                });\n            }\n        }\n        console.log(`Successfully loaded ${tokens.length} tokens`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(tokens);\n    } catch (error) {\n        console.error('Error fetching tokens:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch tokens from factory'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/tokens - Create a new token record in database\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        // Validate required fields\n        const requiredFields = [\n            'address',\n            'name',\n            'symbol'\n        ];\n        for (const field of requiredFields){\n            if (!body[field]) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: `Missing required field: ${field}`\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Check if token already exists\n        const existingToken = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.token.findUnique({\n            where: {\n                address: body.address\n            }\n        });\n        if (existingToken) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Token with this address already exists'\n            }, {\n                status: 409\n            });\n        }\n        // Create new token record\n        const newToken = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.token.create({\n            data: {\n                address: body.address,\n                transactionHash: body.transactionHash || null,\n                blockNumber: body.blockNumber || null,\n                network: body.network || 'amoy',\n                name: body.name,\n                symbol: body.symbol,\n                decimals: body.decimals !== undefined ? parseInt(body.decimals) : 18,\n                maxSupply: body.maxSupply || '1000000',\n                totalSupply: body.totalSupply || '0',\n                tokenType: body.tokenType || 'equity',\n                tokenPrice: body.tokenPrice || '10 USD',\n                currency: body.currency || 'USD',\n                bonusTiers: body.bonusTiers || null,\n                tokenImageUrl: body.tokenImageUrl || null,\n                whitelistAddress: body.whitelistAddress || null,\n                adminAddress: body.adminAddress || null,\n                hasKYC: body.hasKYC || false,\n                isActive: body.isActive !== undefined ? body.isActive : true,\n                deployedBy: body.deployedBy || null,\n                deploymentNotes: body.deploymentNotes || null\n            }\n        });\n        console.log(`Created new token record: ${newToken.symbol} (${newToken.address})`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(newToken, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error creating token:', error);\n        // Handle Prisma unique constraint errors\n        if (error.code === 'P2002') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Token with this address or symbol already exists'\n            }, {\n                status: 409\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to create token record'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/tokens/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/contracts/SecurityToken.json":
/*!******************************************!*\
  !*** ./src/contracts/SecurityToken.json ***!
  \******************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"_format":"hh-sol-artifact-1","contractName":"SecurityToken","sourceName":"contracts/SecurityToken.sol","abi":[{"inputs":[],"stateMutability":"nonpayable","type":"constructor"},{"inputs":[],"name":"AccessControlBadConfirmation","type":"error"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bytes32","name":"neededRole","type":"bytes32"}],"name":"AccessControlUnauthorizedAccount","type":"error"},{"inputs":[{"internalType":"address","name":"target","type":"address"}],"name":"AddressEmptyCode","type":"error"},{"inputs":[{"internalType":"address","name":"implementation","type":"address"}],"name":"ERC1967InvalidImplementation","type":"error"},{"inputs":[],"name":"ERC1967NonPayable","type":"error"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"needed","type":"uint256"}],"name":"ERC20InsufficientAllowance","type":"error"},{"inputs":[{"internalType":"address","name":"sender","type":"address"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"uint256","name":"needed","type":"uint256"}],"name":"ERC20InsufficientBalance","type":"error"},{"inputs":[{"internalType":"address","name":"approver","type":"address"}],"name":"ERC20InvalidApprover","type":"error"},{"inputs":[{"internalType":"address","name":"receiver","type":"address"}],"name":"ERC20InvalidReceiver","type":"error"},{"inputs":[{"internalType":"address","name":"sender","type":"address"}],"name":"ERC20InvalidSender","type":"error"},{"inputs":[{"internalType":"address","name":"spender","type":"address"}],"name":"ERC20InvalidSpender","type":"error"},{"inputs":[],"name":"EnforcedPause","type":"error"},{"inputs":[],"name":"ExpectedPause","type":"error"},{"inputs":[],"name":"FailedCall","type":"error"},{"inputs":[],"name":"InvalidInitialization","type":"error"},{"inputs":[],"name":"NotInitializing","type":"error"},{"inputs":[],"name":"ReentrancyGuardReentrantCall","type":"error"},{"inputs":[],"name":"UUPSUnauthorizedCallContext","type":"error"},{"inputs":[{"internalType":"bytes32","name":"slot","type":"bytes32"}],"name":"UUPSUnsupportedProxiableUUID","type":"error"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"agent","type":"address"}],"name":"AgentAdded","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"agent","type":"address"}],"name":"AgentRemoved","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":false,"internalType":"uint256","name":"timestamp","type":"uint256"}],"name":"AgreementAccepted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"owner","type":"address"},{"indexed":true,"internalType":"address","name":"spender","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Approval","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"enabled","type":"bool"}],"name":"ConditionalTransfersUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"ForcedTransfer","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"oldRegistry","type":"address"},{"indexed":true,"internalType":"address","name":"newRegistry","type":"address"}],"name":"IdentityRegistryUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint64","name":"version","type":"uint64"}],"name":"Initialized","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"oldMaxSupply","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"newMaxSupply","type":"uint256"}],"name":"MaxSupplyUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"address","name":"account","type":"address"}],"name":"Paused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"previousAdminRole","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"newAdminRole","type":"bytes32"}],"name":"RoleAdminChanged","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleGranted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleRevoked","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"string","name":"tokenImageUrl","type":"string"}],"name":"TokenImageUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"string","name":"tokenPrice","type":"string"},{"indexed":false,"internalType":"string","name":"bonusTiers","type":"string"},{"indexed":false,"internalType":"string","name":"tokenDetails","type":"string"}],"name":"TokenMetadataUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Transfer","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"transferId","type":"bytes32"},{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"amount","type":"uint256"}],"name":"TransferApproved","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"transferAmount","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"feeAmount","type":"uint256"}],"name":"TransferFeeCollected","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"enabled","type":"bool"},{"indexed":false,"internalType":"uint256","name":"feePercentage","type":"uint256"},{"indexed":false,"internalType":"address","name":"feeCollector","type":"address"}],"name":"TransferFeesUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":false,"internalType":"bool","name":"whitelisted","type":"bool"}],"name":"TransferWhitelistAddressUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"enabled","type":"bool"}],"name":"TransferWhitelistUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"address","name":"account","type":"address"}],"name":"Unpaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"implementation","type":"address"}],"name":"Upgraded","type":"event"},{"inputs":[],"name":"AGENT_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"DEFAULT_ADMIN_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"TRANSFER_MANAGER_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"UPGRADE_INTERFACE_VERSION","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"acceptAgreement","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"agent","type":"address"}],"name":"addAgent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"addToWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"owner","type":"address"},{"internalType":"address","name":"spender","type":"address"}],"name":"allowance","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"approve","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"nonce","type":"uint256"}],"name":"approveTransfer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"balanceOf","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address[]","name":"accounts","type":"address[]"}],"name":"batchAddToWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"bonusTiers","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"value","type":"uint256"}],"name":"burn","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"burnFrom","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"canTransfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"conditionalTransfersEnabled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"decimals","outputs":[{"internalType":"uint8","name":"","type":"uint8"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"nonce","type":"uint256"}],"name":"executeApprovedTransfer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"forcedTransfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"freezeAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getAgentAt","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getAgentCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"getAgreementAcceptanceTimestamp","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getAllAgents","outputs":[{"internalType":"address[]","name":"","type":"address[]"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"}],"name":"getRoleAdmin","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getTransferFeeConfig","outputs":[{"internalType":"uint256","name":"feePercentage","type":"uint256"},{"internalType":"address","name":"feeCollector","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"getTransferNonce","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"grantRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"hasAcceptedAgreement","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"hasRole","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"identityRegistry","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"string","name":"name_","type":"string"},{"internalType":"string","name":"symbol_","type":"string"},{"internalType":"uint8","name":"decimals_","type":"uint8"},{"internalType":"uint256","name":"maxSupply_","type":"uint256"},{"internalType":"address","name":"identityRegistry_","type":"address"},{"internalType":"address","name":"admin_","type":"address"},{"internalType":"string","name":"tokenPrice_","type":"string"},{"internalType":"string","name":"bonusTiers_","type":"string"},{"internalType":"string","name":"tokenDetails_","type":"string"},{"internalType":"string","name":"tokenImageUrl_","type":"string"}],"name":"initialize","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isAgent","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isTransferWhitelisted","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isWhitelisted","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"maxSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"mint","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"name","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"pause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"paused","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"proxiableUUID","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"agent","type":"address"}],"name":"removeAgent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"removeFromWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"callerConfirmation","type":"address"}],"name":"renounceRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"revokeRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"enabled","type":"bool"}],"name":"setConditionalTransfers","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"uint256","name":"feePercentage","type":"uint256"},{"internalType":"address","name":"feeCollector","type":"address"}],"name":"setTransferFees","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"enabled","type":"bool"}],"name":"setTransferWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bool","name":"whitelisted","type":"bool"}],"name":"setTransferWhitelistAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes4","name":"interfaceId","type":"bytes4"}],"name":"supportsInterface","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"symbol","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenDetails","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenImageUrl","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenPrice","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"totalSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"transfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"transferFeesEnabled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"transferFrom","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"transferWhitelistEnabled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"unfreezeAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"unpause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newIdentityRegistry","type":"address"}],"name":"updateIdentityRegistry","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"newMaxSupply","type":"uint256"}],"name":"updateMaxSupply","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"tokenImageUrl_","type":"string"}],"name":"updateTokenImageUrl","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"tokenPrice_","type":"string"},{"internalType":"string","name":"bonusTiers_","type":"string"},{"internalType":"string","name":"tokenDetails_","type":"string"}],"name":"updateTokenMetadata","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newImplementation","type":"address"},{"internalType":"bytes","name":"data","type":"bytes"}],"name":"upgradeToAndCall","outputs":[],"stateMutability":"payable","type":"function"},{"inputs":[],"name":"version","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"pure","type":"function"}],"bytecode":"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","deployedBytecode":"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","linkReferences":{},"deployedLinkReferences":{}}');

/***/ }),

/***/ "(rsc)/./src/contracts/SecurityTokenFactory.json":
/*!*************************************************!*\
  !*** ./src/contracts/SecurityTokenFactory.json ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"abi":[{"inputs":[{"internalType":"address","name":"admin","type":"address"}],"stateMutability":"nonpayable","type":"constructor"},{"inputs":[],"name":"AccessControlBadConfirmation","type":"error"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bytes32","name":"neededRole","type":"bytes32"}],"name":"AccessControlUnauthorizedAccount","type":"error"},{"inputs":[],"name":"ReentrancyGuardReentrantCall","type":"error"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"oldTokenImplementation","type":"address"},{"indexed":true,"internalType":"address","name":"newTokenImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"oldWhitelistImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"newWhitelistImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"oldWhitelistWithKYCImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"newWhitelistWithKYCImplementation","type":"address"}],"name":"ImplementationsUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"previousAdminRole","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"newAdminRole","type":"bytes32"}],"name":"RoleAdminChanged","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleGranted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleRevoked","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"tokenAddress","type":"address"},{"indexed":true,"internalType":"address","name":"identityRegistryAddress","type":"address"},{"indexed":false,"internalType":"string","name":"name","type":"string"},{"indexed":false,"internalType":"string","name":"symbol","type":"string"},{"indexed":false,"internalType":"uint8","name":"decimals","type":"uint8"},{"indexed":false,"internalType":"uint256","name":"maxSupply","type":"uint256"},{"indexed":false,"internalType":"address","name":"admin","type":"address"},{"indexed":false,"internalType":"bool","name":"hasKYC","type":"bool"},{"indexed":false,"internalType":"string","name":"tokenImageUrl","type":"string"}],"name":"TokenDeployed","type":"event"},{"inputs":[],"name":"DEFAULT_ADMIN_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"DEPLOYER_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"deployer","type":"address"}],"name":"addDeployer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"name","type":"string"},{"internalType":"string","name":"symbol","type":"string"},{"internalType":"uint8","name":"decimals","type":"uint8"},{"internalType":"uint256","name":"maxSupply","type":"uint256"},{"internalType":"address","name":"admin","type":"address"},{"internalType":"string","name":"tokenPrice","type":"string"},{"internalType":"string","name":"bonusTiers","type":"string"},{"internalType":"string","name":"tokenDetails","type":"string"},{"internalType":"string","name":"tokenImageUrl","type":"string"}],"name":"deploySecurityToken","outputs":[{"internalType":"address","name":"tokenAddress","type":"address"},{"internalType":"address","name":"identityRegistryAddress","type":"address"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"name","type":"string"},{"internalType":"string","name":"symbol","type":"string"},{"internalType":"uint8","name":"decimals","type":"uint8"},{"internalType":"uint256","name":"maxSupply","type":"uint256"},{"internalType":"address","name":"admin","type":"address"},{"internalType":"string","name":"tokenPrice","type":"string"},{"internalType":"string","name":"bonusTiers","type":"string"},{"internalType":"string","name":"tokenDetails","type":"string"},{"internalType":"string","name":"tokenImageUrl","type":"string"},{"internalType":"bool","name":"withKYC","type":"bool"}],"name":"deploySecurityTokenWithOptions","outputs":[{"internalType":"address","name":"tokenAddress","type":"address"},{"internalType":"address","name":"identityRegistryAddress","type":"address"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"","type":"uint256"}],"name":"deployedTokens","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getAllDeployedTokens","outputs":[{"internalType":"address[]","name":"","type":"address[]"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getDeployedToken","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"}],"name":"getRoleAdmin","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"string","name":"symbol","type":"string"}],"name":"getTokenAddressBySymbol","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getTokenCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"grantRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"hasRole","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"deployer","type":"address"}],"name":"removeDeployer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"callerConfirmation","type":"address"}],"name":"renounceRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"revokeRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"securityTokenImplementation","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes4","name":"interfaceId","type":"bytes4"}],"name":"supportsInterface","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"newTokenImplementation","type":"address"},{"internalType":"address","name":"newWhitelistImplementation","type":"address"},{"internalType":"address","name":"newWhitelistWithKYCImplementation","type":"address"}],"name":"updateImplementations","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"whitelistImplementation","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"whitelistWithKYCImplementation","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"}]}');

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log:  true ? [\n        'query',\n        'error',\n        'warn'\n    ] : 0\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsQ0FBQztJQUMvREksS0FBS0MsS0FBc0MsR0FBRztRQUFDO1FBQVM7UUFBUztLQUFPLEdBQUcsQ0FBUztBQUN0RixHQUFHO0FBRUgsSUFBSUEsSUFBcUMsRUFBRUosZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcclxuXHJcbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XHJcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KHtcclxuICBsb2c6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnID8gWydxdWVyeScsICdlcnJvcicsICd3YXJuJ10gOiBbJ2Vycm9yJ10sXHJcbn0pO1xyXG5cclxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftokens%2Froute&page=%2Fapi%2Ftokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();