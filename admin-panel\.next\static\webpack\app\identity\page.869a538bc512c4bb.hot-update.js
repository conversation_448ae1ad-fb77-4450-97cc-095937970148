"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/identity/page",{

/***/ "(app-pages-browser)/./src/app/identity/page.tsx":
/*!***********************************!*\
  !*** ./src/app/identity/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IdentityPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_IdentityManagement__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/IdentityManagement */ \"(app-pages-browser)/./src/components/IdentityManagement.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction IdentityPage() {\n    _s();\n    const [contracts, setContracts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [batchAddresses, setBatchAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [batchLoading, setBatchLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('individual');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IdentityPage.useEffect\": ()=>{\n            fetchContractInfo();\n            fetchStats();\n        }\n    }[\"IdentityPage.useEffect\"], []);\n    const fetchContractInfo = async ()=>{\n        const contractAddresses = [\n            {\n                address: process.env.NEXT_PUBLIC_CLAIM_REGISTRY_ADDRESS || process.env.CLAIM_REGISTRY_ADDRESS || '',\n                name: 'ClaimRegistry'\n            },\n            {\n                address: process.env.NEXT_PUBLIC_IDENTITY_REGISTRY_ADDRESS || process.env.IDENTITY_REGISTRY_ADDRESS || '',\n                name: 'IdentityRegistry'\n            },\n            {\n                address: process.env.NEXT_PUBLIC_COMPLIANCE_ADDRESS || process.env.COMPLIANCE_ADDRESS || '',\n                name: 'Compliance'\n            }\n        ];\n        const contractInfo = contractAddresses.map((contract)=>({\n                ...contract,\n                status: contract.address ? 'connected' : 'error'\n            }));\n        setContracts(contractInfo);\n    };\n    const fetchStats = async ()=>{\n        try {\n            // This would typically fetch from your API\n            // For now, we'll use placeholder data\n            setStats({\n                totalVerified: 0,\n                totalWhitelisted: 0,\n                totalKycApproved: 0,\n                totalClaims: 0\n            });\n        } catch (error) {\n            console.error('Error fetching stats:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const performBatchAction = async (action)=>{\n        const addresses = batchAddresses.split('\\n').map((addr)=>addr.trim()).filter((addr)=>addr.length > 0);\n        if (addresses.length === 0) {\n            setMessage({\n                type: 'error',\n                text: 'Please enter at least one address'\n            });\n            return;\n        }\n        if (addresses.length > 50) {\n            setMessage({\n                type: 'error',\n                text: 'Maximum 50 addresses allowed per batch'\n            });\n            return;\n        }\n        setBatchLoading(action);\n        setMessage(null);\n        try {\n            const response = await fetch('/api/identity/batch', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action,\n                    addresses\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                setMessage({\n                    type: 'success',\n                    text: \"Batch \".concat(action, \" completed: \").concat(data.summary.successful, \"/\").concat(data.summary.total, \" successful\")\n                });\n                setBatchAddresses('');\n                fetchStats(); // Refresh stats\n            } else {\n                throw new Error(data.error || 'Batch action failed');\n            }\n        } catch (error) {\n            console.error(\"Error performing batch \".concat(action, \":\"), error);\n            setMessage({\n                type: 'error',\n                text: \"Failed to perform batch \".concat(action, \": \").concat(error.message)\n            });\n        } finally{\n            setBatchLoading(null);\n        }\n    };\n    const getStatusBadge = (status)=>{\n        switch(status){\n            case 'connected':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this),\n                        \"Connected\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 11\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this),\n                        \"Not Configured\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 11\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-3 h-3 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, this),\n                        \"Unknown\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"ERC-3643 Identity Management\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                        variant: \"outline\",\n                        className: \"text-sm\",\n                        children: \"Compliance System v3.0\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this),\n            message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                variant: message.type === 'error' ? 'destructive' : 'default',\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertCircle, {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertDescription, {\n                        children: message.text\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                lineNumber: 160,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tabs, {\n                defaultValue: \"individual\",\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsList, {\n                        className: \"grid w-full grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsTrigger, {\n                                value: \"individual\",\n                                children: \"Individual Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsTrigger, {\n                                value: \"batch\",\n                                children: \"Batch Operations\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsTrigger, {\n                                value: \"stats\",\n                                children: \"Statistics\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsTrigger, {\n                                value: \"system\",\n                                children: \"System Status\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsContent, {\n                        value: \"individual\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_IdentityManagement__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            onStatusUpdate: fetchStats\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsContent, {\n                        value: \"batch\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Users, {\n                                                className: \"w-5 h-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Batch Operations\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"Wallet Addresses (one per line, max 50)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Textarea, {\n                                                    placeholder: \"0x1234... 0x5678... 0x9abc...\",\n                                                    value: batchAddresses,\n                                                    onChange: (e)=>setBatchAddresses(e.target.value),\n                                                    rows: 8,\n                                                    className: \"font-mono text-sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 mt-1\",\n                                                    children: [\n                                                        batchAddresses.split('\\n').filter((addr)=>addr.trim().length > 0).length,\n                                                        \" addresses\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-4 gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                                    onClick: ()=>performBatchAction('batch_whitelist'),\n                                                    disabled: !!batchLoading,\n                                                    variant: \"outline\",\n                                                    children: batchLoading === 'batch_whitelist' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Loader2, {\n                                                        className: \"w-4 h-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 57\n                                                    }, this) : 'Batch Whitelist'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                                    onClick: ()=>performBatchAction('batch_approve_kyc'),\n                                                    disabled: !!batchLoading,\n                                                    variant: \"outline\",\n                                                    children: batchLoading === 'batch_approve_kyc' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Loader2, {\n                                                        className: \"w-4 h-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 59\n                                                    }, this) : 'Batch Approve KYC'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                                    onClick: ()=>performBatchAction('batch_unwhitelist'),\n                                                    disabled: !!batchLoading,\n                                                    variant: \"outline\",\n                                                    children: batchLoading === 'batch_unwhitelist' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Loader2, {\n                                                        className: \"w-4 h-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 59\n                                                    }, this) : 'Batch Remove Whitelist'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                                    onClick: ()=>performBatchAction('batch_issue_qualification_claims'),\n                                                    disabled: !!batchLoading,\n                                                    variant: \"outline\",\n                                                    children: batchLoading === 'batch_issue_qualification_claims' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Loader2, {\n                                                        className: \"w-4 h-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 74\n                                                    }, this) : 'Issue Qualification Claims'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsContent, {\n                        value: \"stats\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                            className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Total Verified\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Shield, {\n                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: loading ? '...' : (stats === null || stats === void 0 ? void 0 : stats.totalVerified) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Registered identities\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                            className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Total Whitelisted\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Users, {\n                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: loading ? '...' : (stats === null || stats === void 0 ? void 0 : stats.totalWhitelisted) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Approved for trading\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                            className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"KYC Approved\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileCheck, {\n                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: loading ? '...' : (stats === null || stats === void 0 ? void 0 : stats.totalKycApproved) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Passed verification\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                            className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Total Claims\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Activity, {\n                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: loading ? '...' : (stats === null || stats === void 0 ? void 0 : stats.totalClaims) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Issued credentials\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsContent, {\n                        value: \"system\",\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Settings, {\n                                                    className: \"w-5 h-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Contract Status\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                        className: \"space-y-4\",\n                                        children: contracts.map((contract, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium\",\n                                                                children: contract.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500 font-mono\",\n                                                                children: contract.address || 'Not configured'\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    getStatusBadge(contract.status)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Database, {\n                                                    className: \"w-5 h-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Environment Configuration\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                        className: \"space-y-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Network:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" Polygon Amoy Testnet\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"RPC URL:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        process.env.NEXT_PUBLIC_AMOY_RPC_URL || 'Not configured'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Admin Address:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" \",\n                                                        process.env.NEXT_PUBLIC_CLAIM_REGISTRY_ADMIN || 'Not configured'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"System Version:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" ERC-3643 v3.0.0\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\identity\\\\page.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, this);\n}\n_s(IdentityPage, \"+XbPkwYwe0WZpIf1g4WW5sMnEK8=\");\n_c = IdentityPage;\nvar _c;\n$RefreshReg$(_c, \"IdentityPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/identity/page.tsx\n"));

/***/ })

});