"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-token/page",{

/***/ "(app-pages-browser)/./src/app/create-token/hooks/useERC3643Integration.ts":
/*!*************************************************************!*\
  !*** ./src/app/create-token/hooks/useERC3643Integration.ts ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useERC3643Integration: () => (/* binding */ useERC3643Integration)\n/* harmony export */ });\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/abi/abi-coder.js\");\n\n// Contract ABIs for ERC-3643 integration\nconst IdentityRegistryABI = [\n    \"function registerIdentity(address investor, uint16 country) external\",\n    \"function addToWhitelist(address investor) external\",\n    \"function approveKyc(address investor) external\",\n    \"function isVerified(address investor) external view returns (bool)\",\n    \"function isWhitelisted(address investor) external view returns (bool)\",\n    \"function isKycApproved(address investor) external view returns (bool)\"\n];\nconst ClaimRegistryABI = [\n    \"function issueClaim(address subject, uint256 topic, bytes calldata signature, bytes calldata data, string calldata uri, uint256 validUntil) external\",\n    \"function hasValidClaim(address subject, uint256 topic) external view returns (bool)\"\n];\nconst ComplianceABI = [\n    \"function created(address to, uint256 value) external\",\n    \"function canTransfer(address from, address to, uint256 value) external view returns (bool)\"\n];\n// Tokeny Claim Topics (following Tokeny standard)\nconst CLAIM_TOPICS = {\n    KYC: 1,\n    AML: 2,\n    IDENTITY: 3,\n    QUALIFICATION: 4,\n    ACCREDITATION: 5,\n    RESIDENCE: 6,\n    TOKEN_ISSUER: 7 // Custom claim for token issuers\n};\n// Tokeny-style claim data format\n// Format: YYYYMMDDHHMMSS (timestamp) + country code + additional data\n// Example: 10101010000648 = timestamp + country + verification level\nfunction generateTokenyClaim(country, claimType) {\n    const now = new Date();\n    const timestamp = now.getFullYear().toString().slice(-2) + // YY\n    (now.getMonth() + 1).toString().padStart(2, '0') + // MM\n    now.getDate().toString().padStart(2, '0') + // DD\n    now.getHours().toString().padStart(2, '0') + // HH\n    now.getMinutes().toString().padStart(2, '0') + // MM\n    now.getSeconds().toString().padStart(2, '0'); // SS\n    const countryCode = getCountryCode(country).toString().padStart(3, '0');\n    // Additional data based on claim type\n    let additionalData = '';\n    switch(claimType){\n        case 'KYC':\n            additionalData = '001'; // KYC level 1\n            break;\n        case 'QUALIFICATION':\n            additionalData = '002'; // Qualified investor\n            break;\n        case 'TOKEN_ISSUER':\n            additionalData = '003'; // Token issuer\n            break;\n        default:\n            additionalData = '000';\n    }\n    return timestamp + countryCode + additionalData;\n}\n// Country code mapping (ISO-3166 numeric)\nconst COUNTRY_CODES = {\n    'US': 840,\n    'USA': 840,\n    'United States': 840,\n    'CA': 124,\n    'Canada': 124,\n    'GB': 826,\n    'UK': 826,\n    'United Kingdom': 826,\n    'DE': 276,\n    'Germany': 276,\n    'FR': 250,\n    'France': 250,\n    'IT': 380,\n    'Italy': 380,\n    'ES': 724,\n    'Spain': 724,\n    'NL': 528,\n    'Netherlands': 528,\n    'CH': 756,\n    'Switzerland': 756,\n    'AU': 36,\n    'Australia': 36,\n    'JP': 392,\n    'Japan': 392,\n    'SG': 702,\n    'Singapore': 702\n};\nfunction getCountryCode(country) {\n    return COUNTRY_CODES[country] || COUNTRY_CODES[country.toUpperCase()] || 840; // Default to USA\n}\n// Global cache to prevent duplicate operations\nconst operationCache = new Map();\n/**\n * Hook for ERC-3643 integration during token deployment\n */ function useERC3643Integration() {\n    /**\n   * Setup ERC-3643 compliance for a newly deployed token\n   */ const setupERC3643Compliance = async (tokenAddress, ownerAddress, signer, tokenData)=>{\n        // Create a unique operation key to prevent duplicates\n        const operationKey = \"\".concat(tokenAddress, \"-\").concat(ownerAddress, \"-\").concat(JSON.stringify(tokenData.selectedClaims));\n        // Check if this operation is already in progress\n        if (operationCache.has(operationKey)) {\n            console.log(\"🔄 ERC-3643 compliance setup already in progress, returning cached promise\");\n            return operationCache.get(operationKey);\n        }\n        console.log(\"🏛️ Setting up ERC-3643 compliance for token:\", tokenAddress);\n        const results = {\n            identityRegistered: false,\n            whitelisted: false,\n            kycApproved: false,\n            claimsIssued: [],\n            complianceNotified: false,\n            errors: []\n        };\n        // Create the operation promise\n        const operationPromise = (async ()=>{\n            try {\n                // Get contract addresses from environment\n                const identityRegistryAddress = \"0x129E04323E4c9bFBD097489473d8523E4015Bfc3\";\n                const claimRegistryAddress = \"******************************************\";\n                const complianceAddress = \"******************************************\";\n                if (!identityRegistryAddress || !claimRegistryAddress || !complianceAddress) {\n                    console.warn(\"⚠️ ERC-3643 contract addresses not configured, skipping compliance setup\");\n                    return results;\n                }\n                // Connect to contracts\n                const identityRegistry = new ethers__WEBPACK_IMPORTED_MODULE_0__.Contract(identityRegistryAddress, IdentityRegistryABI, signer);\n                const claimRegistry = new ethers__WEBPACK_IMPORTED_MODULE_0__.Contract(claimRegistryAddress, ClaimRegistryABI, signer);\n                const compliance = new ethers__WEBPACK_IMPORTED_MODULE_0__.Contract(complianceAddress, ComplianceABI, signer);\n                // Pre-check: Get current status to avoid unnecessary transactions\n                console.log(\"🔍 Checking current compliance status...\");\n                const [isVerified, isWhitelisted, isKycApproved] = await Promise.all([\n                    identityRegistry.isVerified(ownerAddress).catch(()=>false),\n                    identityRegistry.isWhitelisted(ownerAddress).catch(()=>false),\n                    identityRegistry.isKycApproved(ownerAddress).catch(()=>false)\n                ]);\n                console.log(\"📊 Current status:\", {\n                    isVerified,\n                    isWhitelisted,\n                    isKycApproved\n                });\n                // Step 1: Register identity if not already registered\n                try {\n                    const isVerified = await identityRegistry.isVerified(ownerAddress);\n                    if (!isVerified) {\n                        console.log(\"📝 Registering identity for token owner...\");\n                        const countryCode = getCountryCode(tokenData.country || 'US');\n                        const tx1 = await identityRegistry.registerIdentity(ownerAddress, countryCode);\n                        await tx1.wait();\n                        results.identityRegistered = true;\n                        console.log(\"✅ Identity registered successfully\");\n                    } else {\n                        console.log(\"✅ Identity already registered\");\n                        results.identityRegistered = true;\n                    }\n                } catch (error) {\n                    console.error(\"❌ Failed to register identity:\", error);\n                    results.errors.push(\"Identity registration failed: \".concat(error.message));\n                }\n                // Step 2: Add to whitelist if not already whitelisted\n                try {\n                    const isWhitelisted = await identityRegistry.isWhitelisted(ownerAddress);\n                    if (!isWhitelisted) {\n                        console.log(\"📋 Adding to whitelist...\");\n                        const tx2 = await identityRegistry.addToWhitelist(ownerAddress);\n                        await tx2.wait();\n                        results.whitelisted = true;\n                        console.log(\"✅ Added to whitelist successfully\");\n                    } else {\n                        console.log(\"✅ Already whitelisted\");\n                        results.whitelisted = true;\n                    }\n                } catch (error) {\n                    console.error(\"❌ Failed to add to whitelist:\", error);\n                    results.errors.push(\"Whitelist addition failed: \".concat(error.message));\n                }\n                // Step 3: Approve KYC if not already approved\n                try {\n                    const isKycApproved = await identityRegistry.isKycApproved(ownerAddress);\n                    if (!isKycApproved) {\n                        console.log(\"🔍 Approving KYC...\");\n                        const tx3 = await identityRegistry.approveKyc(ownerAddress);\n                        await tx3.wait();\n                        results.kycApproved = true;\n                        console.log(\"✅ KYC approved successfully\");\n                    } else {\n                        console.log(\"✅ KYC already approved\");\n                        results.kycApproved = true;\n                    }\n                } catch (error) {\n                    console.error(\"❌ Failed to approve KYC:\", error);\n                    results.errors.push(\"KYC approval failed: \".concat(error.message));\n                }\n                // Step 4: Issue selected Tokeny-style claims for token issuer\n                try {\n                    const selectedClaims = tokenData.selectedClaims || [\n                        'KYC',\n                        'QUALIFICATION',\n                        'TOKEN_ISSUER'\n                    ];\n                    console.log(\"📜 Issuing selected Tokeny-style claims:\", selectedClaims);\n                    // Issue claims based on user selection\n                    for (const claimType of selectedClaims){\n                        try {\n                            const claimValue = generateTokenyClaim(tokenData.country || 'US', claimType);\n                            const claimTopic = CLAIM_TOPICS[claimType];\n                            if (!claimTopic) {\n                                console.warn(\"⚠️ Unknown claim type: \".concat(claimType));\n                                continue;\n                            }\n                            console.log(\"\\uD83D\\uDD22 Generated \".concat(claimType, \" claim: \").concat(claimValue));\n                            // Check if claim already exists\n                            const hasExistingClaim = await claimRegistry.hasValidClaim(ownerAddress, claimTopic);\n                            if (hasExistingClaim) {\n                                console.log(\"✅ \".concat(claimType, \" claim already exists\"));\n                                continue;\n                            }\n                            // Prepare claim data based on type\n                            let claimData;\n                            let claimUri;\n                            switch(claimType){\n                                case 'KYC':\n                                    claimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                                        \"string\",\n                                        \"string\",\n                                        \"uint256\"\n                                    ], [\n                                        claimValue,\n                                        \"KYC_APPROVED\",\n                                        Math.floor(Date.now() / 1000)\n                                    ]);\n                                    claimUri = \"KYC:\".concat(claimValue);\n                                    break;\n                                case 'AML':\n                                    claimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                                        \"string\",\n                                        \"string\",\n                                        \"uint256\"\n                                    ], [\n                                        claimValue,\n                                        \"AML_VERIFIED\",\n                                        Math.floor(Date.now() / 1000)\n                                    ]);\n                                    claimUri = \"AML:\".concat(claimValue);\n                                    break;\n                                case 'IDENTITY':\n                                    claimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                                        \"string\",\n                                        \"string\",\n                                        \"uint256\"\n                                    ], [\n                                        claimValue,\n                                        \"IDENTITY_VERIFIED\",\n                                        Math.floor(Date.now() / 1000)\n                                    ]);\n                                    claimUri = \"IDENTITY:\".concat(claimValue);\n                                    break;\n                                case 'QUALIFICATION':\n                                    claimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                                        \"string\",\n                                        \"string\",\n                                        \"uint256\"\n                                    ], [\n                                        claimValue,\n                                        \"QUALIFIED_INVESTOR\",\n                                        Math.floor(Date.now() / 1000)\n                                    ]);\n                                    claimUri = \"QUALIFICATION:\".concat(claimValue);\n                                    break;\n                                case 'ACCREDITATION':\n                                    claimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                                        \"string\",\n                                        \"string\",\n                                        \"uint256\"\n                                    ], [\n                                        claimValue,\n                                        \"ACCREDITED_INVESTOR\",\n                                        Math.floor(Date.now() / 1000)\n                                    ]);\n                                    claimUri = \"ACCREDITATION:\".concat(claimValue);\n                                    break;\n                                case 'RESIDENCE':\n                                    claimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                                        \"string\",\n                                        \"string\",\n                                        \"uint256\"\n                                    ], [\n                                        claimValue,\n                                        \"RESIDENCE_VERIFIED\",\n                                        Math.floor(Date.now() / 1000)\n                                    ]);\n                                    claimUri = \"RESIDENCE:\".concat(claimValue);\n                                    break;\n                                case 'TOKEN_ISSUER':\n                                    claimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                                        \"string\",\n                                        \"string\",\n                                        \"string\",\n                                        \"string\",\n                                        \"uint256\"\n                                    ], [\n                                        claimValue,\n                                        tokenData.name,\n                                        tokenData.symbol,\n                                        tokenData.tokenType,\n                                        Math.floor(Date.now() / 1000)\n                                    ]);\n                                    claimUri = \"TOKEN_ISSUER:\".concat(claimValue, \":\").concat(tokenData.symbol);\n                                    break;\n                                default:\n                                    console.warn(\"⚠️ Unsupported claim type: \".concat(claimType));\n                                    continue;\n                            }\n                            // Issue the claim\n                            const tx = await claimRegistry.issueClaim(ownerAddress, claimTopic, \"0x\", claimData, claimUri, 0 // never expires\n                            );\n                            await tx.wait();\n                            results.claimsIssued.push(\"\".concat(claimType, \":\").concat(claimValue));\n                            console.log(\"✅ \".concat(claimType, \" claim issued: \").concat(claimValue));\n                        } catch (claimError) {\n                            console.error(\"❌ Failed to issue \".concat(claimType, \" claim:\"), claimError);\n                            results.errors.push(\"\".concat(claimType, \" claim issuance failed: \").concat(claimError.message));\n                        }\n                    }\n                    console.log(\"\\uD83C\\uDF89 Claims issuance completed! Issued \".concat(results.claimsIssued.length, \" claims\"));\n                } catch (error) {\n                    console.error(\"❌ Failed to issue claims:\", error);\n                    results.errors.push(\"Claims issuance failed: \".concat(error.message));\n                }\n                // Step 5: Notify compliance contract (if needed)\n                try {\n                    console.log(\"⚖️ Notifying compliance contract...\");\n                    // This would typically be called when tokens are minted, but we can prepare it\n                    const canTransfer = await compliance.canTransfer(ownerAddress, ownerAddress, 1);\n                    console.log(\"✅ Compliance check passed:\", canTransfer);\n                    results.complianceNotified = true;\n                } catch (error) {\n                    console.error(\"❌ Compliance notification failed:\", error);\n                    results.errors.push(\"Compliance notification failed: \".concat(error.message));\n                }\n                console.log(\"🎉 ERC-3643 compliance setup completed!\");\n                console.log(\"Results:\", {\n                    identityRegistered: results.identityRegistered,\n                    whitelisted: results.whitelisted,\n                    kycApproved: results.kycApproved,\n                    claimsIssued: results.claimsIssued,\n                    complianceNotified: results.complianceNotified,\n                    errorCount: results.errors.length\n                });\n            } catch (error) {\n                console.error(\"❌ ERC-3643 setup failed:\", error);\n                results.errors.push(\"General setup failed: \".concat(error.message));\n            } finally{\n                // Clean up cache entry\n                operationCache.delete(operationKey);\n            }\n            return results;\n        })();\n        // Cache the operation promise\n        operationCache.set(operationKey, operationPromise);\n        return operationPromise;\n    };\n    /**\n   * Check if ERC-3643 contracts are available\n   */ const isERC3643Available = ()=>{\n        return !!( true && \"******************************************\");\n    };\n    /**\n   * Get ERC-3643 contract addresses\n   */ const getERC3643Addresses = ()=>{\n        return {\n            identityRegistry: \"0x129E04323E4c9bFBD097489473d8523E4015Bfc3\",\n            claimRegistry: \"******************************************\",\n            compliance: \"******************************************\"\n        };\n    };\n    return {\n        setupERC3643Compliance,\n        isERC3643Available,\n        getERC3643Addresses\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/create-token/hooks/useERC3643Integration.ts\n"));

/***/ })

});