import { ethers } from 'ethers';
import { NextRequest, NextResponse } from 'next/server';
import WhitelistABI from '../../../../../contracts/Whitelist.json';

// Load private key from environment variable
const PRIVATE_KEY = process.env.CONTRACT_ADMIN_PRIVATE_KEY;
const RPC_URLS = {
  amoy: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/',
  polygon: process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com',
  unknown: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/', // Default to Amoy for unknown networks
};

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      whitelistAddress, 
      action, 
      address, 
      addresses, 
      network = 'amoy' 
    } = body;
    
    // Validate required inputs
    if (!whitelistAddress || !action) {
      return NextResponse.json(
        { error: 'Whitelist address and action are required' },
        { status: 400 }
      );
    }
    
    // Check if private key is configured
    if (!PRIVATE_KEY) {
      return NextResponse.json(
        {
          error: 'CONTRACT_ADMIN_PRIVATE_KEY environment variable is not set',
          details: 'For security reasons, the API requires a secure method to sign transactions.',
          clientSideInstructions: true,
          message: 'The server is not configured with admin credentials. You can still use the whitelist management script:',
          commandExample: `# Unix/Linux/Mac:\nexport WHITELIST_ADDRESS=${whitelistAddress}\nexport ACTION=${action}\nexport ADDRESS=${address || ''}\nexport ADDRESSES=${addresses ? addresses.join(',') : ''}\nnpx hardhat run scripts/03-manage-whitelist.js --network ${network === 'unknown' ? 'amoy' : network}\n\n# Windows Command Prompt:\nset WHITELIST_ADDRESS=${whitelistAddress}\nset ACTION=${action}\nset ADDRESS=${address || ''}\nset ADDRESSES=${addresses ? addresses.join(',') : ''}\nnpx hardhat run scripts/03-manage-whitelist.js --network ${network === 'unknown' ? 'amoy' : network}\n\n# Windows PowerShell:\n$env:WHITELIST_ADDRESS="${whitelistAddress}"\n$env:ACTION="${action}"\n$env:ADDRESS="${address || ''}"\n$env:ADDRESSES="${addresses ? addresses.join(',') : ''}"\nnpx hardhat run scripts/03-manage-whitelist.js --network ${network === 'unknown' ? 'amoy' : network}`
        },
        { status: 422 }
      );
    }
    
    // Get RPC URL for the specified network, defaulting to Amoy
    const actualNetwork = network === 'unknown' ? 'amoy' : network;
    const rpcUrl = RPC_URLS[actualNetwork as keyof typeof RPC_URLS] || RPC_URLS.amoy;
    
    console.log(`Using network: ${actualNetwork}, RPC URL: ${rpcUrl}`);
    
    // Validate inputs based on action
    if (['addToWhitelist', 'removeFromWhitelist', 'freezeAddress', 'unfreezeAddress', 'isWhitelisted', 'isFrozen',
         'approveKyc', 'revokeKyc', 'isKycApproved'].includes(action) && !address) {
      return NextResponse.json(
        { error: 'Address is required for this action' },
        { status: 400 }
      );
    }
    
    if (['batchAddToWhitelist', 'batchRemoveFromWhitelist', 'batchFreezeAddresses', 'batchUnfreezeAddresses',
         'batchApproveKyc', 'batchRevokeKyc'].includes(action) && 
        (!addresses || !Array.isArray(addresses) || addresses.length === 0)) {
      return NextResponse.json(
        { error: 'Addresses array is required for batch operations' },
        { status: 400 }
      );
    }
    
    // Connect to the network
    const provider = new ethers.JsonRpcProvider(rpcUrl);
    const wallet = new ethers.Wallet(PRIVATE_KEY, provider);
    
    // Connect to the whitelist contract directly
    const whitelistContract = new ethers.Contract(
      whitelistAddress,
      WhitelistABI.abi,
      wallet
    );
    
    // Execute the requested action
    let tx;
    let result;
    
    switch (action) {
      case 'addToWhitelist':
        tx = await whitelistContract.addToWhitelist(address);
        break;
      
      case 'batchAddToWhitelist':
        tx = await whitelistContract.batchAddToWhitelist(addresses);
        break;
      
      case 'removeFromWhitelist':
        tx = await whitelistContract.removeFromWhitelist(address);
        break;
      
      case 'batchRemoveFromWhitelist':
        tx = await whitelistContract.batchRemoveFromWhitelist(addresses);
        break;
      
      case 'freezeAddress':
        tx = await whitelistContract.freezeAddress(address);
        break;
      
      case 'unfreezeAddress':
        tx = await whitelistContract.unfreezeAddress(address);
        break;
      
      case 'batchFreezeAddresses':
        tx = await whitelistContract.batchFreezeAddresses(addresses);
        break;
      
      case 'batchUnfreezeAddresses':
        tx = await whitelistContract.batchUnfreezeAddresses(addresses);
        break;
      
      case 'isWhitelisted':
        // View function
        result = await whitelistContract.isWhitelisted(address);
        return NextResponse.json({
          success: true,
          isWhitelisted: result
        });
      
      case 'isFrozen':
        // View function
        result = await whitelistContract.isFrozen(address);
        return NextResponse.json({
          success: true,
          isFrozen: result
        });

      // KYC operations
      case 'approveKyc':
        tx = await whitelistContract.approveKyc(address);
        break;
      
      case 'revokeKyc':
        tx = await whitelistContract.revokeKyc(address);
        break;
      
      case 'batchApproveKyc':
        tx = await whitelistContract.batchApproveKyc(addresses);
        break;
      
      case 'batchRevokeKyc':
        tx = await whitelistContract.batchRevokeKyc(addresses);
        break;
      
      case 'isKycApproved':
        // View function
        result = await whitelistContract.isKycApproved(address);
        return NextResponse.json({
          success: true,
          isKycApproved: result
        });
      
      default:
        return NextResponse.json(
          { 
            error: 'Invalid action. Supported actions: addToWhitelist, batchAddToWhitelist, removeFromWhitelist, batchRemoveFromWhitelist, freezeAddress, unfreezeAddress, batchFreezeAddresses, batchUnfreezeAddresses, isWhitelisted, isFrozen, approveKyc, revokeKyc, batchApproveKyc, batchRevokeKyc, isKycApproved' 
          },
          { status: 400 }
        );
    }
    
    // Wait for the transaction to be mined
    const receipt = await tx.wait();
    
    return NextResponse.json({
      success: true,
      action,
      txHash: tx.hash,
      blockNumber: receipt.blockNumber
    });
    
  } catch (error: any) {
    console.error('Error managing whitelist:', error);
    return NextResponse.json(
      { error: error.message || 'An unknown error occurred' },
      { status: 500 }
    );
  }
} 