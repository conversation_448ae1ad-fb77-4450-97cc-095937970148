import React from 'react';
import { tokenTypes } from '../../../config';
import { DeploymentStep, TokenFormData } from '../types';

interface TokenFormProps {
  formData: TokenFormData;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
  handleNetworkChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  handleSubmit: (e: React.FormEvent) => Promise<void>;
  isSubmitting: boolean;
  network: string;
  getNetworkLabel: (networkKey: string) => string;
  deploymentStep: DeploymentStep;
  kycSupported: boolean;
}

/**
 * TokenForm Component
 *
 * Form for creating a new security token with various configuration options
 */
const TokenForm: React.FC<TokenFormProps> = ({
  formData,
  handleInputChange,
  handleNetworkChange,
  handleSubmit,
  isSubmitting,
  network,
  getNetworkLabel,
  deploymentStep,
  kycSupported
}) => {
  return (
    <div className="bg-white shadow-md rounded-lg p-6">
      <form onSubmit={handleSubmit}>
        {/* Network Selection */}
        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="network">
            Network
          </label>
          <select
            id="network"
            name="network"
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            value={network}
            onChange={handleNetworkChange}
            disabled={isSubmitting}
          >
            <option value="amoy">Amoy Testnet</option>
            <option value="polygon">Polygon Mainnet</option>
          </select>
        </div>

        {/* KYC Warning */}
        {formData.enableKYC && !kycSupported && (
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  <strong>KYC Support Not Available:</strong> The deployed factory contract doesn't support KYC functionality.
                  Your token will be deployed without KYC support. To enable KYC, the contract factory needs to be updated.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Token Configuration Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          {/* Token Name */}
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="name">
              Token Name*
            </label>
            <input
              type="text"
              id="name"
              name="name"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              placeholder="e.g., Example Security Token"
              value={formData.name}
              onChange={handleInputChange}
              disabled={isSubmitting}
              required
            />
          </div>

          {/* Token Symbol */}
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="symbol">
              Token Symbol*
            </label>
            <input
              type="text"
              id="symbol"
              name="symbol"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              placeholder="e.g., EXST"
              value={formData.symbol}
              onChange={handleInputChange}
              disabled={isSubmitting}
              required
            />
          </div>

          {/* Token Decimals */}
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="decimals">
              Decimals*
            </label>
            <select
              id="decimals"
              name="decimals"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={formData.decimals}
              onChange={handleInputChange}
              disabled={isSubmitting}
              required
            >
              <option value={0}>0 (No decimals)</option>
              <option value={6}>6 decimals</option>
              <option value={8}>8 decimals</option>
              <option value={18}>18 decimals (Standard)</option>
            </select>
            <p className="text-xs text-green-600 mt-1">
              ✅ Choose the number of decimal places for your token. 0 for whole numbers only, 18 for standard ERC-20 tokens.
            </p>
          </div>

          {/* Maximum Supply */}
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="maxSupply">
              Maximum Supply*
            </label>
            <input
              type="text"
              id="maxSupply"
              name="maxSupply"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              placeholder="e.g., 1000000"
              value={formData.maxSupply}
              onChange={handleInputChange}
              disabled={isSubmitting}
              required
            />
          </div>

          {/* Owner Address */}
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="ownerAddress">
              Owner Address (Whitelisted)*
            </label>
            <input
              type="text"
              id="ownerAddress"
              name="ownerAddress"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              placeholder="e.g., 0x..."
              value={formData.ownerAddress}
              onChange={handleInputChange}
              disabled={isSubmitting}
              required
            />
          </div>

          {/* Token Price */}
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="tokenPrice">
              Token Price
            </label>
            <input
              type="text"
              id="tokenPrice"
              name="tokenPrice"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              placeholder="e.g., 10"
              value={formData.tokenPrice}
              onChange={handleInputChange}
              disabled={isSubmitting}
            />
          </div>

          {/* Currency */}
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="currency">
              Currency
            </label>
            <select
              id="currency"
              name="currency"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={formData.currency}
              onChange={handleInputChange}
              disabled={isSubmitting}
            >
              <option value="USD">USD - US Dollar</option>
              <option value="EUR">EUR - Euro</option>
              <option value="GBP">GBP - British Pound</option>
              <option value="JPY">JPY - Japanese Yen</option>
              <option value="CAD">CAD - Canadian Dollar</option>
              <option value="AUD">AUD - Australian Dollar</option>
              <option value="CHF">CHF - Swiss Franc</option>
              <option value="CNY">CNY - Chinese Yuan</option>
              <option value="BTC">BTC - Bitcoin</option>
              <option value="ETH">ETH - Ethereum</option>
              <option value="USDC">USDC - USD Coin</option>
              <option value="USDT">USDT - Tether</option>
            </select>
            <p className="text-xs text-gray-500 mt-1">
              Select the currency for token pricing and fees
            </p>
          </div>

          {/* Token Image URL */}
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="tokenImageUrl">
              Token Logo/Image URL
            </label>
            <input
              type="url"
              id="tokenImageUrl"
              name="tokenImageUrl"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              placeholder="e.g., https://example.com/logo.png"
              value={formData.tokenImageUrl}
              onChange={handleInputChange}
              disabled={isSubmitting}
            />
            <p className="text-xs text-gray-500 mt-1">
              Optional: URL to your token's logo or image (PNG, JPG, SVG recommended)
            </p>
          </div>



          {/* Token Type */}
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="tokenType">
              Token Type
            </label>
            <select
              id="tokenType"
              name="tokenType"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={formData.tokenType}
              onChange={handleInputChange}
              disabled={isSubmitting}
            >
              {tokenTypes.map((type) => (
                <option key={type.id} value={type.id}>
                  {type.name}
                </option>
              ))}
            </select>
          </div>

          {/* KYC Checkbox */}
          <div className="flex items-center h-full">
            <div className="mt-4">
              <label className="flex items-center text-gray-700 font-bold">
                <input
                  type="checkbox"
                  id="enableKYC"
                  name="enableKYC"
                  className="mr-2 h-4 w-4 text-blue-600"
                  checked={formData.enableKYC}
                  onChange={handleInputChange}
                  disabled={isSubmitting || !kycSupported}
                />
                Enable KYC
                {!kycSupported && (
                  <span className="ml-1 text-xs bg-gray-200 text-gray-600 px-1 py-0.5 rounded">
                    Not Available
                  </span>
                )}
              </label>
              <p className="text-sm text-gray-500 ml-6">
                Requires KYC approval for token transfers
                {!kycSupported && (
                  <span className="block text-xs text-red-500">
                    KYC support requires an updated contract factory
                  </span>
                )}
              </p>
            </div>
          </div>
        </div>

        {/* Bonus Tiers */}
        <div className="mb-6">
          <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="bonusTiers">
            Bonus Tiers
          </label>
          <textarea
            id="bonusTiers"
            name="bonusTiers"
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            placeholder="e.g., Tier 1: 5%, Tier 2: 10%, Tier 3: 15%"
            value={formData.bonusTiers}
            onChange={handleInputChange}
            disabled={isSubmitting}
            rows={3}
          ></textarea>
        </div>

        {/* Submit Button */}
        <div className="flex items-center justify-end">
          <button
            type="submit"
            className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {deploymentStep === 'preparing' && 'Preparing...'}
                {deploymentStep === 'connecting' && 'Connecting...'}
                {deploymentStep === 'deploying' && 'Deploying Token...'}
                {deploymentStep === 'confirming' && 'Confirming Transaction...'}
                {deploymentStep === 'fetching' && 'Fetching Token Details...'}
                {deploymentStep === 'setting_up_compliance' && 'Setting up ERC-3643 Compliance...'}
              </>
            ) : (
              `Create Token on ${getNetworkLabel(network)}`
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default TokenForm;