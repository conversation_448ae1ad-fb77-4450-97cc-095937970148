'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, XCircle, AlertCircle, Users, Shield, FileCheck } from 'lucide-react';

interface IdentityStatus {
  address: string;
  blockchain: {
    isVerified: boolean;
    isWhitelisted: boolean;
    isKycApproved: boolean;
    country: string;
    isFrozen: boolean;
    claims: number;
  };
  database: {
    exists: boolean;
    kycStatus: string;
    isWhitelisted: boolean;
    nationality?: string;
  };
  client: any;
}

interface IdentityManagementProps {
  clientId?: string;
  walletAddress?: string;
  onStatusUpdate?: () => void;
}

export default function IdentityManagement({ 
  clientId, 
  walletAddress: initialAddress, 
  onStatusUpdate 
}: IdentityManagementProps) {
  const [address, setAddress] = useState(initialAddress || '');
  const [status, setStatus] = useState<IdentityStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const fetchIdentityStatus = async (addr: string) => {
    if (!addr) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/identity?address=${addr}`);
      if (response.ok) {
        const data = await response.json();
        setStatus(data);
      } else {
        throw new Error('Failed to fetch identity status');
      }
    } catch (error) {
      console.error('Error fetching identity status:', error);
      setMessage({ type: 'error', text: 'Failed to fetch identity status' });
    } finally {
      setLoading(false);
    }
  };

  const performAction = async (action: string, country?: string) => {
    if (!address) return;

    setActionLoading(action);
    setMessage(null);

    try {
      const response = await fetch('/api/identity', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action,
          address,
          country,
          clientId
        })
      });

      const data = await response.json();

      if (response.ok) {
        setMessage({ 
          type: 'success', 
          text: `${action.replace('_', ' ')} completed successfully. Tx: ${data.txHash}` 
        });
        
        // Refresh status
        await fetchIdentityStatus(address);
        
        // Notify parent component
        if (onStatusUpdate) {
          onStatusUpdate();
        }
      } else {
        throw new Error(data.error || 'Action failed');
      }
    } catch (error) {
      console.error(`Error performing ${action}:`, error);
      setMessage({ type: 'error', text: `Failed to ${action.replace('_', ' ')}: ${error.message}` });
    } finally {
      setActionLoading(null);
    }
  };

  useEffect(() => {
    if (initialAddress) {
      fetchIdentityStatus(initialAddress);
    }
  }, [initialAddress]);

  const getStatusBadge = (condition: boolean, trueText: string, falseText: string) => {
    return (
      <Badge variant={condition ? "default" : "secondary"} className="ml-2">
        {condition ? (
          <><CheckCircle className="w-3 h-3 mr-1" /> {trueText}</>
        ) : (
          <><XCircle className="w-3 h-3 mr-1" /> {falseText}</>
        )}
      </Badge>
    );
  };

  const getCountryName = (code: string) => {
    const countries: { [key: string]: string } = {
      '840': 'United States',
      '124': 'Canada',
      '826': 'United Kingdom',
      '276': 'Germany',
      '250': 'France',
      '380': 'Italy',
      '724': 'Spain',
      '528': 'Netherlands',
      '756': 'Switzerland',
      '36': 'Australia',
      '392': 'Japan',
      '702': 'Singapore'
    };
    return countries[code] || `Country ${code}`;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Shield className="w-5 h-5 mr-2" />
            ERC-3643 Identity Management
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              placeholder="Enter wallet address (0x...)"
              value={address}
              onChange={(e) => setAddress(e.target.value)}
              className="flex-1"
            />
            <Button 
              onClick={() => fetchIdentityStatus(address)}
              disabled={!address || loading}
            >
              {loading ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Check Status'}
            </Button>
          </div>

          {message && (
            <Alert variant={message.type === 'error' ? 'destructive' : 'default'}>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{message.text}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {status && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Blockchain Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="w-5 h-5 mr-2" />
                Blockchain Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span>Identity Verified:</span>
                {getStatusBadge(status.blockchain.isVerified, 'Verified', 'Not Verified')}
              </div>
              
              <div className="flex items-center justify-between">
                <span>Whitelisted:</span>
                {getStatusBadge(status.blockchain.isWhitelisted, 'Whitelisted', 'Not Whitelisted')}
              </div>
              
              <div className="flex items-center justify-between">
                <span>KYC Approved:</span>
                {getStatusBadge(status.blockchain.isKycApproved, 'Approved', 'Not Approved')}
              </div>
              
              <div className="flex items-center justify-between">
                <span>Country:</span>
                <Badge variant="outline">
                  {getCountryName(status.blockchain.country)}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span>Frozen:</span>
                {getStatusBadge(!status.blockchain.isFrozen, 'Active', 'Frozen')}
              </div>
              
              <div className="flex items-center justify-between">
                <span>Claims:</span>
                <Badge variant="outline">
                  {status.blockchain.claims} claims
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Database Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileCheck className="w-5 h-5 mr-2" />
                Database Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span>Client Exists:</span>
                {getStatusBadge(status.database.exists, 'Found', 'Not Found')}
              </div>
              
              {status.database.exists && (
                <>
                  <div className="flex items-center justify-between">
                    <span>KYC Status:</span>
                    <Badge variant={
                      status.database.kycStatus === 'APPROVED' ? 'default' :
                      status.database.kycStatus === 'REJECTED' ? 'destructive' : 'secondary'
                    }>
                      {status.database.kycStatus}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span>DB Whitelisted:</span>
                    {getStatusBadge(status.database.isWhitelisted, 'Yes', 'No')}
                  </div>
                  
                  {status.database.nationality && (
                    <div className="flex items-center justify-between">
                      <span>Nationality:</span>
                      <Badge variant="outline">{status.database.nationality}</Badge>
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Action Buttons */}
      {status && (
        <Card>
          <CardHeader>
            <CardTitle>Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {!status.blockchain.isVerified && (
                <Button
                  onClick={() => performAction('register', status.database.nationality || 'US')}
                  disabled={!!actionLoading}
                  variant="outline"
                >
                  {actionLoading === 'register' ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Register Identity'}
                </Button>
              )}
              
              {status.blockchain.isVerified && !status.blockchain.isWhitelisted && (
                <Button
                  onClick={() => performAction('whitelist')}
                  disabled={!!actionLoading}
                  variant="outline"
                >
                  {actionLoading === 'whitelist' ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Add to Whitelist'}
                </Button>
              )}
              
              {status.blockchain.isWhitelisted && (
                <Button
                  onClick={() => performAction('unwhitelist')}
                  disabled={!!actionLoading}
                  variant="outline"
                >
                  {actionLoading === 'unwhitelist' ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Remove from Whitelist'}
                </Button>
              )}
              
              {status.blockchain.isVerified && !status.blockchain.isKycApproved && (
                <Button
                  onClick={() => performAction('approve_kyc')}
                  disabled={!!actionLoading}
                  variant="outline"
                >
                  {actionLoading === 'approve_kyc' ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Approve KYC'}
                </Button>
              )}
              
              {status.blockchain.isKycApproved && (
                <Button
                  onClick={() => performAction('revoke_kyc')}
                  disabled={!!actionLoading}
                  variant="outline"
                >
                  {actionLoading === 'revoke_kyc' ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Revoke KYC'}
                </Button>
              )}
              
              {status.blockchain.isVerified && !status.blockchain.isFrozen && (
                <Button
                  onClick={() => performAction('freeze')}
                  disabled={!!actionLoading}
                  variant="destructive"
                >
                  {actionLoading === 'freeze' ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Freeze Address'}
                </Button>
              )}
              
              {status.blockchain.isFrozen && (
                <Button
                  onClick={() => performAction('unfreeze')}
                  disabled={!!actionLoading}
                  variant="outline"
                >
                  {actionLoading === 'unfreeze' ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Unfreeze Address'}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
