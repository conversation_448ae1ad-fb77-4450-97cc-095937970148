'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useParams, useRouter } from 'next/navigation';
import { ArrowLeftIcon, PencilIcon, CheckIcon, XMarkIcon, ArrowPathIcon } from '@heroicons/react/24/outline';

interface ClientDetails {
  id: string;
  firstName: string;
  lastName: string;
  gender: string;
  nationality: string;
  birthday: string;
  birthPlace: string;
  identificationType: string;
  passportNumber?: string;
  idCardNumber?: string;
  documentExpiration: string;
  phoneNumber: string;
  email?: string;
  occupation: string;
  sectorOfActivity: string;
  pepStatus: string;
  pepDetails?: string;
  street: string;
  buildingNumber: string;
  city: string;
  state?: string;
  country: string;
  zipCode: string;
  sourceOfWealth: string;
  bankAccountNumber: string;
  sourceOfFunds: string;
  taxIdentificationNumber: string;
  kycStatus: 'PENDING' | 'IN_REVIEW' | 'APPROVED' | 'REJECTED' | 'EXPIRED';
  kycNotes?: string;
  isWhitelisted: boolean;
  walletAddress?: string;
  agreementAccepted?: boolean;
  agreementAcceptedAt?: string;
  createdAt: string;
  updatedAt: string;
  documents?: Array<{
    id: string;
    documentType: string;
    fileName: string;
    status: string;
    createdAt: string;
  }>;
  transactions?: Array<{
    id: string;
    transactionHash: string;
    transactionType: string;
    amount: string;
    status: string;
    createdAt: string;
  }>;
}

export default function ClientDetailPage() {
  const params = useParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const clientId = params.id as string;

  const [editingKyc, setEditingKyc] = useState(false);
  const [editingWhitelist, setEditingWhitelist] = useState(false);
  const [kycStatus, setKycStatus] = useState('');
  const [kycNotes, setKycNotes] = useState('');
  const [walletAddress, setWalletAddress] = useState('');
  const [isWhitelisted, setIsWhitelisted] = useState(false);

  // Fetch client details with automatic refresh
  const { data: client, isLoading, error } = useQuery<ClientDetails>({
    queryKey: ['client', clientId],
    queryFn: async () => {
      console.log('Fetching client details for ID:', clientId);
      const response = await fetch(`/api/clients/${clientId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch client details');
      }
      const data = await response.json();
      console.log('Client data received:', data);
      return data;
    },
    enabled: !!clientId,
    refetchInterval: 30000, // Refresh every 30 seconds
    refetchOnWindowFocus: true, // Refresh when user returns to tab
    refetchOnMount: true, // Always refresh on component mount
  });

  // Update KYC status mutation
  const updateKycMutation = useMutation({
    mutationFn: async ({ kycStatus, kycNotes }: { kycStatus: string; kycNotes?: string }) => {
      const response = await fetch(`/api/clients/${clientId}/kyc`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ kycStatus, kycNotes }),
      });
      if (!response.ok) throw new Error('Failed to update KYC status');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['client', clientId] });
      setEditingKyc(false);
    },
  });

  // Update whitelist status mutation
  const updateWhitelistMutation = useMutation({
    mutationFn: async ({ walletAddress, isWhitelisted }: { walletAddress: string; isWhitelisted: boolean }) => {
      const response = await fetch(`/api/clients/${clientId}/whitelist`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ walletAddress, isWhitelisted }),
      });
      if (!response.ok) throw new Error('Failed to update whitelist status');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['client', clientId] });
      setEditingWhitelist(false);
    },
  });

  const handleKycEdit = () => {
    setKycStatus(client?.kycStatus || '');
    setKycNotes(client?.kycNotes || '');
    setEditingKyc(true);
  };

  const handleWhitelistEdit = () => {
    setWalletAddress(client?.walletAddress || '');
    setIsWhitelisted(client?.isWhitelisted || false);
    setEditingWhitelist(true);
  };

  const handleKycSave = () => {
    updateKycMutation.mutate({ kycStatus, kycNotes });
  };

  const handleWhitelistSave = () => {
    updateWhitelistMutation.mutate({ walletAddress, isWhitelisted });
  };

  const getKycStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED': return 'text-green-600 bg-green-100';
      case 'REJECTED': return 'text-red-600 bg-red-100';
      case 'IN_REVIEW': return 'text-yellow-600 bg-yellow-100';
      case 'PENDING': return 'text-gray-600 bg-gray-100';
      case 'EXPIRED': return 'text-orange-600 bg-orange-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !client) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        Error loading client details: {error?.message || 'Client not found'}
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <button
          onClick={() => router.back()}
          className="flex items-center text-blue-600 hover:text-blue-700 mb-4"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to Clients
        </button>
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {client.firstName} {client.lastName}
            </h1>
            <p className="text-gray-600 mt-1">Client ID: {client.id}</p>
          </div>
          <div className="flex items-center space-x-3">
            <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getKycStatusColor(client.kycStatus)}`}>
              {client.kycStatus}
            </span>
            <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
              client.isWhitelisted
                ? 'text-green-600 bg-green-100'
                : 'text-gray-600 bg-gray-100'
            }`}>
              {client.isWhitelisted ? 'Whitelisted' : 'Not Whitelisted'}
            </span>
            <button
              onClick={() => queryClient.invalidateQueries({ queryKey: ['client', clientId] })}
              className="inline-flex items-center px-3 py-1 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
              title="Refresh client data"
            >
              <ArrowPathIcon className="h-4 w-4 mr-1" />
              Refresh
            </button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-8">
          {/* Personal Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Personal Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">First Name</label>
                <p className="mt-1 text-sm text-gray-900">{client.firstName}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Last Name</label>
                <p className="mt-1 text-sm text-gray-900">{client.lastName}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Gender</label>
                <p className="mt-1 text-sm text-gray-900">{client.gender}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Nationality</label>
                <p className="mt-1 text-sm text-gray-900">{client.nationality}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Birthday</label>
                <p className="mt-1 text-sm text-gray-900">{new Date(client.birthday).toLocaleDateString()}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Birth Place</label>
                <p className="mt-1 text-sm text-gray-900">{client.birthPlace}</p>
              </div>
            </div>
          </div>

          {/* Identification */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Identification</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">Identification Type</label>
                <p className="mt-1 text-sm text-gray-900">{client.identificationType}</p>
              </div>
              {client.passportNumber && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Passport Number</label>
                  <p className="mt-1 text-sm text-gray-900">{client.passportNumber}</p>
                </div>
              )}
              {client.idCardNumber && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">ID Card Number</label>
                  <p className="mt-1 text-sm text-gray-900">{client.idCardNumber}</p>
                </div>
              )}
              <div>
                <label className="block text-sm font-medium text-gray-700">Document Expiration</label>
                <p className="mt-1 text-sm text-gray-900">{new Date(client.documentExpiration).toLocaleDateString()}</p>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Contact Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">Phone Number</label>
                <p className="mt-1 text-sm text-gray-900">{client.phoneNumber}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Email</label>
                <p className="mt-1 text-sm text-gray-900">{client.email || 'N/A'}</p>
              </div>
            </div>
          </div>

          {/* Wallet Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Wallet Information</h2>
            <div className="grid grid-cols-1 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">Wallet Address</label>
                {client.walletAddress ? (
                  <div className="mt-1">
                    <p className="text-sm text-gray-900 font-mono break-all bg-gray-50 p-2 rounded border">
                      {client.walletAddress}
                    </p>
                  </div>
                ) : (
                  <p className="mt-1 text-sm text-gray-500 italic">No wallet connected</p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Verification Status</label>
                <div className="mt-1 flex items-center space-x-2">
                  {client.walletVerifiedAt ? (
                    <>
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-green-600 bg-green-100">
                        Verified
                      </span>
                      <span className="text-sm text-gray-500">
                        on {new Date(client.walletVerifiedAt).toLocaleString()}
                      </span>
                    </>
                  ) : client.walletAddress ? (
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-yellow-600 bg-yellow-100">
                      Connected but not verified
                    </span>
                  ) : (
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-gray-600 bg-gray-100">
                      Not connected
                    </span>
                  )}
                </div>
              </div>
              {client.walletSignature && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Digital Signature</label>
                  <p className="mt-1 text-sm text-gray-900 font-mono break-all bg-gray-50 p-2 rounded border">
                    {client.walletSignature.substring(0, 20)}...{client.walletSignature.substring(client.walletSignature.length - 20)}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Professional Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Professional Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">Occupation</label>
                <p className="mt-1 text-sm text-gray-900">{client.occupation}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Sector of Activity</label>
                <p className="mt-1 text-sm text-gray-900">{client.sectorOfActivity}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">PEP Status</label>
                <p className="mt-1 text-sm text-gray-900">{client.pepStatus}</p>
              </div>
              {client.pepDetails && (
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700">PEP Details</label>
                  <p className="mt-1 text-sm text-gray-900">{client.pepDetails}</p>
                </div>
              )}
            </div>
          </div>

          {/* Address Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Address Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">Street</label>
                <p className="mt-1 text-sm text-gray-900">{client.street}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Building Number</label>
                <p className="mt-1 text-sm text-gray-900">{client.buildingNumber}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">City</label>
                <p className="mt-1 text-sm text-gray-900">{client.city}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">State/Province</label>
                <p className="mt-1 text-sm text-gray-900">{client.state || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Country</label>
                <p className="mt-1 text-sm text-gray-900">{client.country}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Zip Code</label>
                <p className="mt-1 text-sm text-gray-900">{client.zipCode}</p>
              </div>
            </div>
          </div>

          {/* Financial Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Financial Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">Source of Wealth</label>
                <p className="mt-1 text-sm text-gray-900">{client.sourceOfWealth}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Source of Funds</label>
                <p className="mt-1 text-sm text-gray-900">{client.sourceOfFunds}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Bank Account Number</label>
                <p className="mt-1 text-sm text-gray-900">{client.bankAccountNumber}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Tax Identification Number</label>
                <p className="mt-1 text-sm text-gray-900">{client.taxIdentificationNumber}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-8">
          {/* KYC Management */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">KYC Status</h3>
              <div className="flex items-center space-x-2">
                {isLoading && (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                )}
                {!editingKyc && (
                  <button
                    onClick={handleKycEdit}
                    className="text-blue-600 hover:text-blue-700"
                  >
                    <PencilIcon className="h-5 w-5" />
                  </button>
                )}
              </div>
            </div>

            {editingKyc ? (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                  <select
                    value={kycStatus}
                    onChange={(e) => setKycStatus(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="PENDING">Pending</option>
                    <option value="IN_REVIEW">In Review</option>
                    <option value="APPROVED">Approved</option>
                    <option value="REJECTED">Rejected</option>
                    <option value="EXPIRED">Expired</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                  <textarea
                    value={kycNotes}
                    onChange={(e) => setKycNotes(e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Add notes about KYC status..."
                  />
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={handleKycSave}
                    disabled={updateKycMutation.isPending}
                    className="flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                  >
                    <CheckIcon className="h-4 w-4 mr-1" />
                    Save
                  </button>
                  <button
                    onClick={() => setEditingKyc(false)}
                    className="flex items-center px-3 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                  >
                    <XMarkIcon className="h-4 w-4 mr-1" />
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <div>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getKycStatusColor(client.kycStatus)}`}>
                    {client.kycStatus}
                  </span>
                </div>
                {client.kycNotes && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Notes</label>
                    <p className="mt-1 text-sm text-gray-900">{client.kycNotes}</p>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Whitelist Management */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Whitelist Status</h3>
              {!editingWhitelist && (
                <button
                  onClick={handleWhitelistEdit}
                  className="text-blue-600 hover:text-blue-700"
                >
                  <PencilIcon className="h-5 w-5" />
                </button>
              )}
            </div>

            {editingWhitelist ? (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Wallet Address</label>
                  <input
                    type="text"
                    value={walletAddress}
                    onChange={(e) => setWalletAddress(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="0x..."
                  />
                </div>
                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={isWhitelisted}
                      onChange={(e) => setIsWhitelisted(e.target.checked)}
                      className="mr-2"
                    />
                    <span className="text-sm font-medium text-gray-700">Whitelisted</span>
                  </label>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={handleWhitelistSave}
                    disabled={updateWhitelistMutation.isPending}
                    className="flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                  >
                    <CheckIcon className="h-4 w-4 mr-1" />
                    Save
                  </button>
                  <button
                    onClick={() => setEditingWhitelist(false)}
                    className="flex items-center px-3 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                  >
                    <XMarkIcon className="h-4 w-4 mr-1" />
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <div>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    client.isWhitelisted
                      ? 'text-green-600 bg-green-100'
                      : 'text-gray-600 bg-gray-100'
                  }`}>
                    {client.isWhitelisted ? 'Whitelisted' : 'Not Whitelisted'}
                  </span>
                </div>
                {client.walletAddress && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Wallet Address</label>
                    <p className="mt-1 text-sm text-gray-900 font-mono break-all">{client.walletAddress}</p>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Agreement Status */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Agreement Status</h3>
            <div className="space-y-3">
              <div>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  client.agreementAccepted
                    ? 'text-green-600 bg-green-100'
                    : 'text-gray-600 bg-gray-100'
                }`}>
                  {client.agreementAccepted ? 'Agreement Accepted' : 'Agreement Not Accepted'}
                </span>
              </div>
              {client.agreementAcceptedAt && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Accepted At</label>
                  <p className="mt-1 text-sm text-gray-900">{new Date(client.agreementAcceptedAt).toLocaleString()}</p>
                </div>
              )}
            </div>
          </div>

          {/* Client Metadata */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Client Information</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700">Created</label>
                <p className="mt-1 text-sm text-gray-900">{new Date(client.createdAt).toLocaleString()}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Last Updated</label>
                <p className="mt-1 text-sm text-gray-900">{new Date(client.updatedAt).toLocaleString()}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
