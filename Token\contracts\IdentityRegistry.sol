// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "./interfaces/IIdentityRegistry.sol";
import "./interfaces/IKYCRegistry.sol";
import "./ClaimRegistry.sol";

/**
 * @title IdentityRegistry
 * @dev ERC-3643 compliant Identity Registry implementation
 * Manages investor identities, countries, verification status, and claims
 * Centralized approach - no separate contracts per user
 */
contract IdentityRegistry is 
    Initializable,
    AccessControlUpgradeable,
    ReentrancyGuardUpgradeable,
    IIdentityRegistry,
    IKYCRegistry
{
    bytes32 public constant AGENT_ROLE = keccak256("AGENT_ROLE");
    bytes32 public constant COMPLIANCE_ROLE = keccak256("COMPLIANCE_ROLE");

    // Identity storage structure
    struct Identity {
        bool isVerified;        // Overall verification status
        bool isWhitelisted;     // Whitelist status
        bool isKycApproved;     // KYC approval status
        bool isFrozen;          // Frozen status
        uint16 country;         // ISO-3166 country code
        uint256 registeredAt;   // Registration timestamp
        uint256 updatedAt;      // Last update timestamp
    }

    // Core mappings
    mapping(address => Identity) private _identities;
    mapping(uint16 => uint256) private _countryInvestorCount;
    mapping(uint16 => bool) private _restrictedCountries;
    
    // Arrays for enumeration
    address[] private _verifiedAddresses;
    mapping(address => uint256) private _verifiedAddressIndex;
    
    // Claim registry integration
    ClaimRegistry public claimRegistry;
    
    // Required claim topics for verification
    uint256[] private _requiredClaimTopics;
    mapping(uint256 => bool) private _isRequiredClaimTopic;

    // Events
    event IdentityRegistered(address indexed userAddress, uint16 indexed country, uint256 timestamp);
    event IdentityUpdated(address indexed userAddress, uint16 indexed country, uint256 timestamp);
    event IdentityRemoved(address indexed userAddress, uint256 timestamp);
    event CountryRestricted(uint16 indexed country);
    event CountryUnrestricted(uint16 indexed country);
    event ClaimRegistryUpdated(address indexed oldRegistry, address indexed newRegistry);
    event RequiredClaimTopicAdded(uint256 indexed claimTopic);
    event RequiredClaimTopicRemoved(uint256 indexed claimTopic);

    /**
     * @dev Initialize the contract
     * @param admin The address to be granted DEFAULT_ADMIN_ROLE
     * @param _claimRegistry Address of the ClaimRegistry contract
     */
    function initialize(address admin, address _claimRegistry) public initializer {
        require(admin != address(0), "IdentityRegistry: admin cannot be zero address");
        require(_claimRegistry != address(0), "IdentityRegistry: claim registry cannot be zero address");
        
        __AccessControl_init();
        __ReentrancyGuard_init();
        
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(AGENT_ROLE, admin);
        
        claimRegistry = ClaimRegistry(_claimRegistry);
        
        // Set default required claim topics (KYC and Qualification)
        _requiredClaimTopics.push(1); // KYC_CLAIM
        _requiredClaimTopics.push(4); // QUALIFICATION_CLAIM
        _isRequiredClaimTopic[1] = true;
        _isRequiredClaimTopic[4] = true;
    }

    // ============================================================================
    // IDENTITY MANAGEMENT FUNCTIONS
    // ============================================================================

    /**
     * @dev Register a new identity
     * @param userAddress The address to register
     * @param country ISO-3166 country code
     */
    function registerIdentity(
        address userAddress, 
        uint16 country
    ) external onlyRole(AGENT_ROLE) {
        require(userAddress != address(0), "IdentityRegistry: cannot register zero address");
        require(!_identities[userAddress].isVerified, "IdentityRegistry: identity already registered");
        require(!_restrictedCountries[country], "IdentityRegistry: country is restricted");

        _identities[userAddress] = Identity({
            isVerified: true,
            isWhitelisted: false,
            isKycApproved: false,
            isFrozen: false,
            country: country,
            registeredAt: block.timestamp,
            updatedAt: block.timestamp
        });

        _verifiedAddresses.push(userAddress);
        _verifiedAddressIndex[userAddress] = _verifiedAddresses.length - 1;
        _countryInvestorCount[country]++;

        emit IdentityRegistered(userAddress, country, block.timestamp);
    }

    /**
     * @dev Update an existing identity
     * @param userAddress The address to update
     * @param country New ISO-3166 country code
     */
    function updateIdentity(
        address userAddress, 
        uint16 country
    ) external onlyRole(AGENT_ROLE) {
        require(_identities[userAddress].isVerified, "IdentityRegistry: identity not registered");
        require(!_restrictedCountries[country], "IdentityRegistry: country is restricted");

        uint16 oldCountry = _identities[userAddress].country;
        if (oldCountry != country) {
            _countryInvestorCount[oldCountry]--;
            _countryInvestorCount[country]++;
        }

        _identities[userAddress].country = country;
        _identities[userAddress].updatedAt = block.timestamp;

        emit IdentityUpdated(userAddress, country, block.timestamp);
    }

    /**
     * @dev Remove an identity
     * @param userAddress The address to remove
     */
    function removeIdentity(address userAddress) external onlyRole(AGENT_ROLE) {
        require(_identities[userAddress].isVerified, "IdentityRegistry: identity not registered");

        uint16 country = _identities[userAddress].country;
        _countryInvestorCount[country]--;

        // Remove from verified addresses array
        uint256 index = _verifiedAddressIndex[userAddress];
        uint256 lastIndex = _verifiedAddresses.length - 1;
        
        if (index != lastIndex) {
            address lastAddress = _verifiedAddresses[lastIndex];
            _verifiedAddresses[index] = lastAddress;
            _verifiedAddressIndex[lastAddress] = index;
        }
        
        _verifiedAddresses.pop();
        delete _verifiedAddressIndex[userAddress];
        delete _identities[userAddress];

        emit IdentityRemoved(userAddress, block.timestamp);
    }

    // ============================================================================
    // VERIFICATION AND STATUS FUNCTIONS
    // ============================================================================

    /**
     * @dev Check if an address is verified (has registered identity)
     * @param userAddress The address to check
     * @return bool True if verified
     */
    function isVerified(address userAddress) external view returns (bool) {
        return _identities[userAddress].isVerified;
    }

    /**
     * @dev Get investor country
     * @param userAddress The address to check
     * @return uint16 ISO-3166 country code
     */
    function investorCountry(address userAddress) external view returns (uint16) {
        require(_identities[userAddress].isVerified, "IdentityRegistry: identity not registered");
        return _identities[userAddress].country;
    }

    /**
     * @dev Get complete identity information
     * @param userAddress The address to check
     * @return Identity struct with all information
     */
    function getIdentity(address userAddress) external view returns (Identity memory) {
        return _identities[userAddress];
    }

    /**
     * @dev Check if address has valid claims for verification
     * @param userAddress The address to check
     * @return bool True if has all required valid claims
     */
    function hasValidClaims(address userAddress) public view returns (bool) {
        if (_requiredClaimTopics.length == 0) return true;
        
        for (uint256 i = 0; i < _requiredClaimTopics.length; i++) {
            uint256 claimType = _requiredClaimTopics[i];
            if (!claimRegistry.hasValidClaim(userAddress, claimType)) {
                return false;
            }
        }
        return true;
    }

    // ============================================================================
    // WHITELIST FUNCTIONS (IIdentityRegistry compatibility)
    // ============================================================================

    /**
     * @dev Check if an address is whitelisted
     * @param account The address to check
     * @return bool True if whitelisted
     */
    function isWhitelisted(address account) external view override returns (bool) {
        return _identities[account].isWhitelisted;
    }

    /**
     * @dev Add an address to the whitelist
     * @param account The address to add
     */
    function addToWhitelist(address account) external override onlyRole(AGENT_ROLE) {
        require(_identities[account].isVerified, "IdentityRegistry: identity not registered");
        require(!_identities[account].isWhitelisted, "IdentityRegistry: already whitelisted");
        require(!_identities[account].isFrozen, "IdentityRegistry: address is frozen");

        _identities[account].isWhitelisted = true;
        _identities[account].updatedAt = block.timestamp;

        emit AddedToWhitelist(account);
    }

    /**
     * @dev Remove an address from the whitelist
     * @param account The address to remove
     */
    function removeFromWhitelist(address account) external override onlyRole(AGENT_ROLE) {
        require(_identities[account].isWhitelisted, "IdentityRegistry: not whitelisted");

        _identities[account].isWhitelisted = false;
        _identities[account].updatedAt = block.timestamp;

        emit RemovedFromWhitelist(account);
    }

    // ============================================================================
    // KYC FUNCTIONS (IKYCRegistry compatibility)
    // ============================================================================

    /**
     * @dev Check if an address is KYC approved
     * @param account The address to check
     * @return bool True if KYC approved
     */
    function isKycApproved(address account) external view override returns (bool) {
        return _identities[account].isKycApproved;
    }

    /**
     * @dev Approve KYC for an address
     * @param account The address to approve
     */
    function approveKyc(address account) external override onlyRole(AGENT_ROLE) {
        require(_identities[account].isVerified, "IdentityRegistry: identity not registered");
        require(!_identities[account].isKycApproved, "IdentityRegistry: already KYC approved");

        _identities[account].isKycApproved = true;
        _identities[account].updatedAt = block.timestamp;

        emit KycApproved(account);
    }

    /**
     * @dev Revoke KYC approval for an address
     * @param account The address to revoke
     */
    function revokeKyc(address account) external override onlyRole(AGENT_ROLE) {
        require(_identities[account].isKycApproved, "IdentityRegistry: not KYC approved");

        _identities[account].isKycApproved = false;
        _identities[account].updatedAt = block.timestamp;

        emit KycRevoked(account);
    }

    // ============================================================================
    // FREEZING FUNCTIONS
    // ============================================================================

    /**
     * @dev Check if an address is frozen
     * @param account The address to check
     * @return bool True if frozen
     */
    function isFrozen(address account) external view override returns (bool) {
        return _identities[account].isFrozen;
    }

    /**
     * @dev Freeze an address
     * @param account The address to freeze
     */
    function freezeAddress(address account) external override onlyRole(AGENT_ROLE) {
        require(_identities[account].isVerified, "IdentityRegistry: identity not registered");
        require(!_identities[account].isFrozen, "IdentityRegistry: already frozen");

        _identities[account].isFrozen = true;
        _identities[account].updatedAt = block.timestamp;

        emit AddressFrozen(account);
    }

    /**
     * @dev Unfreeze an address
     * @param account The address to unfreeze
     */
    function unfreezeAddress(address account) external override onlyRole(AGENT_ROLE) {
        require(_identities[account].isFrozen, "IdentityRegistry: not frozen");

        _identities[account].isFrozen = false;
        _identities[account].updatedAt = block.timestamp;

        emit AddressUnfrozen(account);
    }

    // ============================================================================
    // BATCH OPERATIONS
    // ============================================================================

    /**
     * @dev Batch add addresses to whitelist
     * @param accounts The addresses to add
     */
    function batchAddToWhitelist(address[] calldata accounts) external override onlyRole(AGENT_ROLE) nonReentrant {
        uint256 length = accounts.length;
        require(length > 0, "IdentityRegistry: empty addresses array");

        for (uint256 i = 0; i < length; i++) {
            address account = accounts[i];
            if (_identities[account].isVerified &&
                !_identities[account].isWhitelisted &&
                !_identities[account].isFrozen) {

                _identities[account].isWhitelisted = true;
                _identities[account].updatedAt = block.timestamp;
                emit AddedToWhitelist(account);
            }
        }
    }

    /**
     * @dev Batch remove addresses from whitelist
     * @param accounts The addresses to remove
     */
    function batchRemoveFromWhitelist(address[] calldata accounts) external override onlyRole(AGENT_ROLE) nonReentrant {
        uint256 length = accounts.length;
        require(length > 0, "IdentityRegistry: empty addresses array");

        for (uint256 i = 0; i < length; i++) {
            address account = accounts[i];
            if (_identities[account].isWhitelisted) {
                _identities[account].isWhitelisted = false;
                _identities[account].updatedAt = block.timestamp;
                emit RemovedFromWhitelist(account);
            }
        }
    }

    /**
     * @dev Batch approve KYC for addresses
     * @param accounts The addresses to approve
     */
    function batchApproveKyc(address[] calldata accounts) external override onlyRole(AGENT_ROLE) nonReentrant {
        uint256 length = accounts.length;
        require(length > 0, "IdentityRegistry: empty addresses array");

        for (uint256 i = 0; i < length; i++) {
            address account = accounts[i];
            if (_identities[account].isVerified && !_identities[account].isKycApproved) {
                _identities[account].isKycApproved = true;
                _identities[account].updatedAt = block.timestamp;
                emit KycApproved(account);
            }
        }
    }

    /**
     * @dev Batch revoke KYC for addresses
     * @param accounts The addresses to revoke
     */
    function batchRevokeKyc(address[] calldata accounts) external override onlyRole(AGENT_ROLE) nonReentrant {
        uint256 length = accounts.length;
        require(length > 0, "IdentityRegistry: empty addresses array");

        for (uint256 i = 0; i < length; i++) {
            address account = accounts[i];
            if (_identities[account].isKycApproved) {
                _identities[account].isKycApproved = false;
                _identities[account].updatedAt = block.timestamp;
                emit KycRevoked(account);
            }
        }
    }

    /**
     * @dev Batch freeze addresses
     * @param accounts The addresses to freeze
     */
    function batchFreezeAddresses(address[] calldata accounts) external override onlyRole(AGENT_ROLE) nonReentrant {
        uint256 length = accounts.length;
        require(length > 0, "IdentityRegistry: empty addresses array");

        for (uint256 i = 0; i < length; i++) {
            address account = accounts[i];
            if (_identities[account].isVerified && !_identities[account].isFrozen) {
                _identities[account].isFrozen = true;
                _identities[account].updatedAt = block.timestamp;
                emit AddressFrozen(account);
            }
        }
    }

    /**
     * @dev Batch unfreeze addresses
     * @param accounts The addresses to unfreeze
     */
    function batchUnfreezeAddresses(address[] calldata accounts) external override onlyRole(AGENT_ROLE) nonReentrant {
        uint256 length = accounts.length;
        require(length > 0, "IdentityRegistry: empty addresses array");

        for (uint256 i = 0; i < length; i++) {
            address account = accounts[i];
            if (_identities[account].isFrozen) {
                _identities[account].isFrozen = false;
                _identities[account].updatedAt = block.timestamp;
                emit AddressUnfrozen(account);
            }
        }
    }

    // ============================================================================
    // COUNTRY MANAGEMENT FUNCTIONS
    // ============================================================================

    /**
     * @dev Restrict a country from registering new identities
     * @param country ISO-3166 country code to restrict
     */
    function restrictCountry(uint16 country) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(!_restrictedCountries[country], "IdentityRegistry: country already restricted");

        _restrictedCountries[country] = true;
        emit CountryRestricted(country);
    }

    /**
     * @dev Unrestrict a country
     * @param country ISO-3166 country code to unrestrict
     */
    function unrestrictCountry(uint16 country) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(_restrictedCountries[country], "IdentityRegistry: country not restricted");

        _restrictedCountries[country] = false;
        emit CountryUnrestricted(country);
    }

    /**
     * @dev Check if a country is restricted
     * @param country ISO-3166 country code to check
     * @return bool True if restricted
     */
    function isCountryRestricted(uint16 country) external view returns (bool) {
        return _restrictedCountries[country];
    }

    /**
     * @dev Get investor count for a country
     * @param country ISO-3166 country code
     * @return uint256 Number of investors from that country
     */
    function getCountryInvestorCount(uint16 country) external view returns (uint256) {
        return _countryInvestorCount[country];
    }

    // ============================================================================
    // CLAIM REGISTRY MANAGEMENT
    // ============================================================================

    /**
     * @dev Update the claim registry address
     * @param newClaimRegistry New ClaimRegistry contract address
     */
    function updateClaimRegistry(address newClaimRegistry) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(newClaimRegistry != address(0), "IdentityRegistry: claim registry cannot be zero address");

        address oldRegistry = address(claimRegistry);
        claimRegistry = ClaimRegistry(newClaimRegistry);

        emit ClaimRegistryUpdated(oldRegistry, newClaimRegistry);
    }

    /**
     * @dev Add a required claim topic for verification
     * @param claimTopic The claim topic to require
     */
    function addRequiredClaimTopic(uint256 claimTopic) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(!_isRequiredClaimTopic[claimTopic], "IdentityRegistry: claim topic already required");

        _requiredClaimTopics.push(claimTopic);
        _isRequiredClaimTopic[claimTopic] = true;

        emit RequiredClaimTopicAdded(claimTopic);
    }

    /**
     * @dev Remove a required claim topic
     * @param claimTopic The claim topic to remove
     */
    function removeRequiredClaimTopic(uint256 claimTopic) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(_isRequiredClaimTopic[claimTopic], "IdentityRegistry: claim topic not required");

        // Find and remove from array
        for (uint256 i = 0; i < _requiredClaimTopics.length; i++) {
            if (_requiredClaimTopics[i] == claimTopic) {
                _requiredClaimTopics[i] = _requiredClaimTopics[_requiredClaimTopics.length - 1];
                _requiredClaimTopics.pop();
                break;
            }
        }

        _isRequiredClaimTopic[claimTopic] = false;

        emit RequiredClaimTopicRemoved(claimTopic);
    }

    /**
     * @dev Get all required claim topics
     * @return uint256[] Array of required claim topics
     */
    function getRequiredClaimTopics() external view returns (uint256[] memory) {
        return _requiredClaimTopics;
    }

    // ============================================================================
    // VIEW FUNCTIONS FOR ENUMERATION
    // ============================================================================

    /**
     * @dev Get total number of verified addresses
     * @return uint256 Total count
     */
    function getVerifiedAddressCount() external view returns (uint256) {
        return _verifiedAddresses.length;
    }

    /**
     * @dev Get verified address by index
     * @param index Index in the verified addresses array
     * @return address The verified address
     */
    function getVerifiedAddressByIndex(uint256 index) external view returns (address) {
        require(index < _verifiedAddresses.length, "IdentityRegistry: index out of bounds");
        return _verifiedAddresses[index];
    }

    /**
     * @dev Get a range of verified addresses
     * @param start Starting index
     * @param limit Maximum number of addresses to return
     * @return addresses Array of verified addresses
     */
    function getVerifiedAddresses(uint256 start, uint256 limit) external view returns (address[] memory addresses) {
        uint256 total = _verifiedAddresses.length;
        if (start >= total) {
            return new address[](0);
        }

        uint256 end = start + limit;
        if (end > total) {
            end = total;
        }

        addresses = new address[](end - start);
        for (uint256 i = start; i < end; i++) {
            addresses[i - start] = _verifiedAddresses[i];
        }
    }

    // ============================================================================
    // COMPLIANCE INTEGRATION
    // ============================================================================

    /**
     * @dev Check if transfer is allowed between addresses (for compliance contract)
     * @param from Sender address
     * @param to Recipient address
     * @return bool True if transfer is allowed
     */
    function canTransfer(address from, address to) external view returns (bool) {
        // Both addresses must be verified and whitelisted
        if (!_identities[from].isVerified || !_identities[to].isVerified) {
            return false;
        }

        if (!_identities[from].isWhitelisted || !_identities[to].isWhitelisted) {
            return false;
        }

        // Neither address should be frozen
        if (_identities[from].isFrozen || _identities[to].isFrozen) {
            return false;
        }

        // Both should have valid claims
        if (!hasValidClaims(from) || !hasValidClaims(to)) {
            return false;
        }

        // Countries should not be restricted
        if (_restrictedCountries[_identities[from].country] ||
            _restrictedCountries[_identities[to].country]) {
            return false;
        }

        return true;
    }
}
