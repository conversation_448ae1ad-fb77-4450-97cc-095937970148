import { useState } from 'react';
import { ethers } from 'ethers';
import { getNetworkConfig } from '../../../config';
import SecurityTokenFactoryABI from '../../../contracts/SecurityTokenFactory.json';
import SecurityTokenABI from '../../../contracts/SecurityToken.json';
import { DeployedToken, DeploymentStep, TokenFormData } from '../types';
import { useERC3643Integration } from './useERC3643Integration';

/**
 * Custom hook for token deployment logic
 *
 * Encapsulates all the token deployment functionality including state management,
 * transaction handling, and error handling
 */
export function useTokenDeployment(
  network: string,
  factoryAddress: string,
  hasDeployerRole: boolean,
  kycSupported: boolean
) {
  // State management
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [deployedToken, setDeployedToken] = useState<DeployedToken | null>(null);
  const [transactionHash, setTransactionHash] = useState<string | null>(null);
  const [deploymentStep, setDeploymentStep] = useState<DeploymentStep>('idle');

  // ERC-3643 integration
  const { setupERC3643Compliance, isERC3643Available } = useERC3643Integration();

  /**
   * Save token data to database
   */
  const saveTokenToDatabase = async (
    deployedToken: DeployedToken,
    formData: TokenFormData,
    transactionHash: string,
    blockNumber: string,
    network: string
  ) => {
    // Fetch totalSupply from the blockchain
    let totalSupply = '0';
    try {
      // Create a new provider instance for blockchain calls
      const provider = new ethers.BrowserProvider(window.ethereum);
      const token = new ethers.Contract(
        deployedToken.address,
        SecurityTokenABI.abi,
        provider
      );
      const totalSupplyRaw = await token.totalSupply();
      totalSupply = deployedToken.decimals === 0
        ? totalSupplyRaw.toString()
        : ethers.formatUnits(totalSupplyRaw, deployedToken.decimals);
    } catch (error) {
      console.warn('Could not fetch totalSupply from blockchain, using default 0:', error);
    }

    const tokenData = {
      address: deployedToken.address,
      transactionHash: transactionHash,
      blockNumber: blockNumber,
      network: network,
      name: deployedToken.name,
      symbol: deployedToken.symbol,
      decimals: deployedToken.decimals,
      maxSupply: deployedToken.maxSupply,
      totalSupply: totalSupply, // Include totalSupply from blockchain
      tokenType: formData.tokenType,
      tokenPrice: deployedToken.tokenPrice,
      currency: deployedToken.currency,
      bonusTiers: deployedToken.bonusTiers,
      tokenImageUrl: deployedToken.tokenImageUrl,
      whitelistAddress: deployedToken.whitelistAddress,
      adminAddress: deployedToken.admin,
      hasKYC: deployedToken.hasKYC,
      isActive: true,
      deployedBy: deployedToken.admin, // Use admin address as deployer
      deploymentNotes: `${formData.tokenType} token deployed via admin panel`
    };

    const response = await fetch('/api/tokens', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(tokenData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Database save failed: ${errorData.error || 'Unknown error'}`);
    }

    return await response.json();
  };

  /**
   * Deploy a new token with the provided form data
   */
  const deployToken = async (formData: TokenFormData) => {
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);
    setDeployedToken(null);
    setTransactionHash(null);
    setDeploymentStep('preparing');

    try {
      // Validate form data
      validateFormData(formData);

      // Get network configuration
      const networkConfig = getNetworkConfig(network);

      if (!factoryAddress) {
        throw new Error(`No factory address configured for network: ${network}`);
      }

      if (!window.ethereum) {
        throw new Error('Please install MetaMask to use this feature!');
      }

      setDeploymentStep('connecting');

      // Get provider and signer
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();

      // Verify network connection
      await verifyNetworkConnection(provider, network);

      // Connect to the factory contract
      const factory = new ethers.Contract(
        factoryAddress,
        SecurityTokenFactoryABI.abi,
        signer
      );

      console.log("Connected to factory at:", factoryAddress);

      // Verify deployer role
      await verifyDeployerRole(factory, signer, hasDeployerRole);

      // Verify KYC support
      await verifyKYCSupport(factory, formData.enableKYC);

      // Check if token symbol already exists
      await checkTokenSymbolAvailability(factory, formData.symbol);

      // Convert maxSupply to the appropriate unit based on decimals
      const maxSupplyWei = formData.decimals === 0
        ? BigInt(formData.maxSupply)
        : ethers.parseUnits(formData.maxSupply, formData.decimals);

      setDeploymentStep('deploying');
      console.log("Deploying token with params:", {
        name: formData.name,
        symbol: formData.symbol,
        decimals: formData.decimals,
        maxSupply: formData.maxSupply,
        admin: formData.ownerAddress,
        tokenPrice: formData.tokenPrice,
        bonusTiers: formData.bonusTiers,
        enableKYC: formData.enableKYC
      });

      // Create the transaction
      const tx = await createDeployTransaction(
        factory,
        formData,
        maxSupplyWei,
        network,
        kycSupported
      );

      setTransactionHash(tx.hash);
      console.log("Transaction hash:", tx.hash);
      setDeploymentStep('confirming');

      // Wait for the transaction to be mined
      const receipt = await tx.wait();
      console.log("Transaction mined in block:", receipt.blockNumber);

      setDeploymentStep('fetching');

      // Get the token address
      const tokenAddress = await factory.getTokenAddressBySymbol(formData.symbol);

      if (tokenAddress && tokenAddress !== ethers.ZeroAddress) {
        // Create token deployment result
        const deploymentResult = await getDeploymentResult(
          tokenAddress,
          provider,
          formData
        );

        setDeployedToken(deploymentResult);

        // Save token to database
        try {
          await saveTokenToDatabase(deploymentResult, formData, tx.hash, receipt.blockNumber.toString(), network);
          console.log("Token successfully saved to database");
        } catch (dbError) {
          console.warn("Failed to save token to database:", dbError);
          // Don't fail the deployment if database save fails
        }

        // Setup ERC-3643 compliance if available
        if (isERC3643Available()) {
          setDeploymentStep('setting_up_compliance');
          console.log("🏛️ Setting up ERC-3643 compliance...");

          try {
            const complianceResult = await setupERC3643Compliance(
              tokenAddress,
              formData.ownerAddress,
              signer,
              {
                name: formData.name,
                symbol: formData.symbol,
                tokenType: formData.tokenType,
                country: formData.issuerCountry || 'US',
                selectedClaims: formData.selectedClaims
              }
            );

            if (complianceResult.errors.length > 0) {
              console.warn("⚠️ Some ERC-3643 setup steps failed:", complianceResult.errors);
              // Don't fail deployment, just warn
            } else {
              console.log("✅ ERC-3643 compliance setup completed successfully");
            }
          } catch (complianceError) {
            console.warn("⚠️ ERC-3643 compliance setup failed:", complianceError);
            // Don't fail deployment, just warn
          }
        } else {
          console.log("ℹ️ ERC-3643 contracts not available, skipping compliance setup");
        }

        setDeploymentStep('completed');
        setSuccess(`Token "${formData.name}" (${formData.symbol}) successfully deployed!`);
      } else {
        throw new Error("Token deployment failed: Could not retrieve token address");
      }
    } catch (err: any) {
      handleDeploymentError(err, network);
    } finally {
      setIsSubmitting(false);
    }
  };

  /**
   * Validate form data before deployment
   */
  const validateFormData = (formData: TokenFormData) => {
    if (!formData.name || !formData.symbol || !formData.maxSupply || !formData.ownerAddress) {
      throw new Error('Please fill in all required fields');
    }

    if (!ethers.isAddress(formData.ownerAddress)) {
      throw new Error('Invalid owner address');
    }

    if (formData.decimals < 0 || formData.decimals > 18) {
      throw new Error('Decimals must be between 0 and 18');
    }
  };

  /**
   * Verify that the wallet is connected to the correct network
   */
  const verifyNetworkConnection = async (provider: ethers.Provider, network: string) => {
    const chainId = (await provider.getNetwork()).chainId;
    if (network === 'amoy' && chainId.toString() !== '80002') {
      throw new Error('Please connect your wallet to the Amoy network (Chain ID: 80002)');
    } else if (network === 'polygon' && chainId.toString() !== '137') {
      throw new Error('Please connect your wallet to the Polygon network (Chain ID: 137)');
    }
  };

  /**
   * Verify that the connected wallet has DEPLOYER_ROLE
   */
  const verifyDeployerRole = async (
    factory: ethers.Contract,
    signer: ethers.Signer,
    hasRole: boolean
  ) => {
    if (!hasRole) {
      const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();
      const hasDeployerRole = await factory.hasRole(DEPLOYER_ROLE, await signer.getAddress());

      if (!hasDeployerRole) {
        throw new Error("Your wallet does not have the DEPLOYER_ROLE required to create tokens");
      }
    }
  };

  /**
   * Check if token symbol is available
   */
  const checkTokenSymbolAvailability = async (factory: ethers.Contract, symbol: string) => {
    try {
      const existingTokenAddress = await factory.getTokenAddressBySymbol(symbol);
      if (existingTokenAddress !== ethers.ZeroAddress) {
        throw new Error(`Token with symbol "${symbol}" already exists at address ${existingTokenAddress}. Please choose a different symbol.`);
      }
    } catch (err: any) {
      if (err.message.includes("already exists")) {
        throw err; // Re-throw our custom error
      }
      // If it's a different error, log it but don't fail the deployment
      console.warn("Could not check token symbol availability:", err.message);
    }
  };

  /**
   * Verify KYC support in the factory contract
   */
  const verifyKYCSupport = async (factory: ethers.Contract, enableKYC: boolean) => {
    try {
      // Check if whitelistWithKYCImplementation exists
      const kycImplementation = await factory.whitelistWithKYCImplementation();

      // Check function exists by examining the ABI
      const hasKYCFunction = factory.interface.fragments.some(
        fragment => fragment.type === "function" &&
                  'name' in fragment &&
                  fragment.name === "deploySecurityTokenWithOptions"
      );

      if (!hasKYCFunction) {
        throw new Error("The deployed factory contract doesn't support the KYC functionality. Please deploy an updated factory contract.");
      }

      if (kycImplementation === ethers.ZeroAddress) {
        throw new Error("KYC implementation address is not set in the factory contract.");
      }
    } catch (err: any) {
      if (err.message.includes("whitelistWithKYCImplementation")) {
        throw new Error("The deployed factory contract doesn't support the KYC functionality. Please deploy an updated factory contract.");
      }
      throw err;
    }
  };

  /**
   * Create the deployment transaction with the appropriate gas settings
   */
  const createDeployTransaction = async (
    factory: ethers.Contract,
    formData: TokenFormData,
    maxSupplyWei: bigint,
    network: string,
    supportsKYC: boolean
  ) => {
    // Determine if KYC is supported
    let canUseKYC = supportsKYC;
    try {
      await factory.whitelistWithKYCImplementation();

      const hasKYCFunction = factory.interface.fragments.some(
        fragment => fragment.type === "function" &&
                   'name' in fragment &&
                   fragment.name === "deploySecurityTokenWithOptions"
      );

      canUseKYC = hasKYCFunction;
    } catch (err) {
      console.log("KYC functionality not supported in this factory contract, using standard deployment");
      canUseKYC = false;

      // If KYC was enabled but not supported, warn the user
      if (formData.enableKYC) {
        console.warn("KYC requested but not supported by the contract. Proceeding with standard token deployment.");
      }
    }

    // Optimized gas settings for Amoy testnet
    if (network === 'amoy') {
      // Optimized gas settings for Amoy testnet - 5M gas limit, 50 gwei gas price
      const gasLimit = BigInt(5000000);
      const gasPrice = ethers.parseUnits("50", "gwei");

      console.log("Using optimized gas settings for Amoy testnet:");
      console.log("Gas limit:", gasLimit.toString());
      console.log("Gas price:", ethers.formatUnits(gasPrice, "gwei"), "gwei");

      // Call the appropriate function based on KYC support
      if (canUseKYC) {
        console.log("Calling deploySecurityTokenWithOptions with KYC:", formData.enableKYC);
        return await factory.deploySecurityTokenWithOptions(
          formData.name,
          formData.symbol,
          formData.decimals,
          maxSupplyWei,
          formData.ownerAddress,
          `${formData.tokenPrice} ${formData.currency}`,
          formData.bonusTiers,
          `${formData.tokenType} token deployed via admin panel`,
          formData.tokenImageUrl || "",
          formData.enableKYC,
          { gasLimit, gasPrice }
        );
      } else {
        console.log("Calling deploySecurityToken (no KYC support)");
        return await factory.deploySecurityToken(
          formData.name,
          formData.symbol,
          formData.decimals,
          maxSupplyWei,
          formData.ownerAddress,
          `${formData.tokenPrice} ${formData.currency}`,
          formData.bonusTiers,
          `${formData.tokenType} token deployed via admin panel`,
          formData.tokenImageUrl || "",
          { gasLimit, gasPrice }
        );
      }
    } else {
      // For other networks, try to estimate gas
      let gasLimit;
      try {
        // Estimate gas based on which function we can call
        let gasEstimate;
        if (canUseKYC) {
          gasEstimate = await factory.deploySecurityTokenWithOptions.estimateGas(
            formData.name,
            formData.symbol,
            formData.decimals,
            maxSupplyWei,
            formData.ownerAddress,
            `${formData.tokenPrice} ${formData.currency}`,
            formData.bonusTiers,
            `${formData.tokenType} token deployed via admin panel`,
            formData.tokenImageUrl || "",
            formData.enableKYC
          );
        } else {
          gasEstimate = await factory.deploySecurityToken.estimateGas(
            formData.name,
            formData.symbol,
            formData.decimals,
            maxSupplyWei,
            formData.ownerAddress,
            `${formData.tokenPrice} ${formData.currency}`,
            formData.bonusTiers,
            `${formData.tokenType} token deployed via admin panel`,
            formData.tokenImageUrl || ""
          );
        }

        console.log("Gas estimate:", gasEstimate.toString());

        // Add 50% to the gas estimate to be safer
        gasLimit = (gasEstimate * BigInt(150)) / BigInt(100);
        console.log("Using calculated gas limit:", gasLimit.toString());
      } catch (estimateErr) {
        console.error("Gas estimation failed, using fixed limit:", estimateErr);
        // Fallback to fixed gas limit if estimation fails
        gasLimit = BigInt(2000000); // Use 2M for non-Amoy networks
        console.log("Using fallback gas limit:", gasLimit.toString());
      }

      // Call without specific gas price for networks that calculate it properly
      if (canUseKYC) {
        return await factory.deploySecurityTokenWithOptions(
          formData.name,
          formData.symbol,
          formData.decimals,
          maxSupplyWei,
          formData.ownerAddress,
          `${formData.tokenPrice} ${formData.currency}`,
          formData.bonusTiers,
          `${formData.tokenType} token deployed via admin panel`,
          formData.tokenImageUrl || "",
          formData.enableKYC,
          { gasLimit }
        );
      } else {
        return await factory.deploySecurityToken(
          formData.name,
          formData.symbol,
          formData.decimals,
          maxSupplyWei,
          formData.ownerAddress,
          `${formData.tokenPrice} ${formData.currency}`,
          formData.bonusTiers,
          `${formData.tokenType} token deployed via admin panel`,
          formData.tokenImageUrl || "",
          { gasLimit }
        );
      }
    }
  };

  /**
   * Get deployment result after successful transaction
   */
  const getDeploymentResult = async (
    tokenAddress: string,
    provider: ethers.Provider,
    formData: TokenFormData
  ): Promise<DeployedToken> => {
    console.log("Token successfully deployed at:", tokenAddress);

    // Connect to the token contract
    const token = new ethers.Contract(
      tokenAddress,
      SecurityTokenABI.abi,
      provider
    );

    // Get identity registry address (whitelist contract)
    const whitelistAddress = await token.identityRegistry();

    // Get token decimals from the contract
    const tokenDecimals = await token.decimals();
    const decimalsNumber = Number(tokenDecimals);

    // Format maxSupply based on decimals
    const maxSupplyRaw = await token.maxSupply();
    const maxSupplyFormatted = decimalsNumber === 0
      ? maxSupplyRaw.toString()
      : ethers.formatUnits(maxSupplyRaw, tokenDecimals);

    // Try to get the image URL from the contract if supported
    let tokenImageUrl = formData.tokenImageUrl;
    try {
      // Check if the token contract supports tokenImageUrl function
      if (token.interface.fragments.some(fragment =>
        fragment.type === "function" && 'name' in fragment && fragment.name === "tokenImageUrl")) {
        tokenImageUrl = await token.tokenImageUrl();
      }
    } catch (error) {
      console.log("Token contract doesn't support image URL, using form data");
    }



    // Create deployed token object
    return {
      address: tokenAddress,
      name: await token.name(),
      symbol: await token.symbol(),
      decimals: decimalsNumber,
      maxSupply: maxSupplyFormatted,
      whitelistAddress: whitelistAddress,
      admin: formData.ownerAddress,
      tokenPrice: `${formData.tokenPrice} ${formData.currency}`,
      currency: formData.currency,
      bonusTiers: formData.bonusTiers,
      hasKYC: formData.enableKYC,
      tokenImageUrl: tokenImageUrl
    };
  };

  /**
   * Handle deployment errors with detailed messages
   */
  const handleDeploymentError = (err: any, network: string) => {
    console.error('Error creating token:', err);

    // Extract more details from the error for debugging
    const errorDetails = typeof err === 'object' ?
      JSON.stringify({
        code: err.code,
        message: err.message,
        data: err.data,
        info: err.info
      }, null, 2) :
      String(err);

    // Special handling for specific contract errors
    if (err.message.includes("transaction execution reverted")) {
      // This is likely a contract validation error
      setError(`Transaction failed: The contract rejected the transaction. This could be due to:

• Token symbol already exists - try a different symbol
• Invalid parameters (empty name/symbol, zero max supply, etc.)
• Access control issues

Please check your inputs and try again with a unique token symbol.

Technical details: ${err.message}`);
    } else if (err.message.includes("gas required exceeds allowance") ||
        err.message.includes("intrinsic gas too low") ||
        err.message.includes("Internal JSON-RPC error")) {

      // For Amoy testnet specifically, provide CLI alternative
      if (network === 'amoy') {
        // Create a CLI command template - actual values will be filled in by the UI
        const cliCommand = `# For Windows PowerShell:
cd Token
$env:NETWORK="amoy"
$env:TOKEN_NAME="YourTokenName"
$env:TOKEN_SYMBOL="YTS"
$env:TOKEN_DECIMALS="0"
$env:MAX_SUPPLY="1000000"
$env:ADMIN_ADDRESS="0xYourAddress"
$env:TOKEN_PRICE="10 USD"
$env:BONUS_TIERS="Tier 1: 5%, Tier 2: 10%"
npx hardhat run scripts/02-deploy-token.js --network amoy`;

        setError(`Gas estimation failed on Amoy testnet. This is a common issue with this network.

You can try using this command line script instead:

${cliCommand}`);
      } else {
        setError(`Transaction failed due to gas calculation issues: ${err.message}\n\nDetails: ${errorDetails}`);
      }
    } else if (err.message.includes("Internal JSON-RPC error") || err.message.includes("could not coalesce error")) {
      setError("Transaction failed. This is likely due to gas calculation issues on the Amoy testnet. Try using the command line script instead.");
    } else {
      setError(`Transaction failed: ${err.message}\n\nDetails: ${errorDetails}`);
    }

    setDeploymentStep('failed');
  };

  return {
    isSubmitting,
    error,
    success,
    deployedToken,
    transactionHash,
    deploymentStep,
    deployToken
  };
}