"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-token/page",{

/***/ "(app-pages-browser)/./src/app/create-token/hooks/useERC3643Integration.ts":
/*!*************************************************************!*\
  !*** ./src/app/create-token/hooks/useERC3643Integration.ts ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useERC3643Integration: () => (/* binding */ useERC3643Integration)\n/* harmony export */ });\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/abi/abi-coder.js\");\n\n// Contract ABIs for ERC-3643 integration\nconst IdentityRegistryABI = [\n    \"function registerIdentity(address investor, uint16 country) external\",\n    \"function addToWhitelist(address investor) external\",\n    \"function approveKyc(address investor) external\",\n    \"function isVerified(address investor) external view returns (bool)\",\n    \"function isWhitelisted(address investor) external view returns (bool)\",\n    \"function isKycApproved(address investor) external view returns (bool)\"\n];\nconst ClaimRegistryABI = [\n    \"function issueClaim(address subject, uint256 topic, bytes calldata signature, bytes calldata data, string calldata uri, uint256 validUntil) external\",\n    \"function hasValidClaim(address subject, uint256 topic) external view returns (bool)\"\n];\nconst ComplianceABI = [\n    \"function created(address to, uint256 value) external\",\n    \"function canTransfer(address from, address to, uint256 value) external view returns (bool)\"\n];\n// Tokeny Claim Topics (following Tokeny standard)\nconst CLAIM_TOPICS = {\n    KYC: 1,\n    AML: 2,\n    IDENTITY: 3,\n    QUALIFICATION: 4,\n    ACCREDITATION: 5,\n    RESIDENCE: 6,\n    TOKEN_ISSUER: 7 // Custom claim for token issuers\n};\n// Tokeny-style claim data format\n// Format: YYYYMMDDHHMMSS (timestamp) + country code + additional data\n// Example: 10101010000648 = timestamp + country + verification level\nfunction generateTokenyClaim(country, claimType) {\n    const now = new Date();\n    const timestamp = now.getFullYear().toString().slice(-2) + // YY\n    (now.getMonth() + 1).toString().padStart(2, '0') + // MM\n    now.getDate().toString().padStart(2, '0') + // DD\n    now.getHours().toString().padStart(2, '0') + // HH\n    now.getMinutes().toString().padStart(2, '0') + // MM\n    now.getSeconds().toString().padStart(2, '0'); // SS\n    const countryCode = getCountryCode(country).toString().padStart(3, '0');\n    // Additional data based on claim type\n    let additionalData = '';\n    switch(claimType){\n        case 'KYC':\n            additionalData = '001'; // KYC level 1\n            break;\n        case 'QUALIFICATION':\n            additionalData = '002'; // Qualified investor\n            break;\n        case 'TOKEN_ISSUER':\n            additionalData = '003'; // Token issuer\n            break;\n        default:\n            additionalData = '000';\n    }\n    return timestamp + countryCode + additionalData;\n}\n// Country code mapping (ISO-3166 numeric)\nconst COUNTRY_CODES = {\n    'US': 840,\n    'USA': 840,\n    'United States': 840,\n    'CA': 124,\n    'Canada': 124,\n    'GB': 826,\n    'UK': 826,\n    'United Kingdom': 826,\n    'DE': 276,\n    'Germany': 276,\n    'FR': 250,\n    'France': 250,\n    'IT': 380,\n    'Italy': 380,\n    'ES': 724,\n    'Spain': 724,\n    'NL': 528,\n    'Netherlands': 528,\n    'CH': 756,\n    'Switzerland': 756,\n    'AU': 36,\n    'Australia': 36,\n    'JP': 392,\n    'Japan': 392,\n    'SG': 702,\n    'Singapore': 702\n};\nfunction getCountryCode(country) {\n    return COUNTRY_CODES[country] || COUNTRY_CODES[country.toUpperCase()] || 840; // Default to USA\n}\n// Global cache to prevent duplicate operations\nconst operationCache = new Map();\n/**\n * Hook for ERC-3643 integration during token deployment\n */ function useERC3643Integration() {\n    /**\n   * Setup ERC-3643 compliance for a newly deployed token\n   */ const setupERC3643Compliance = async (tokenAddress, ownerAddress, signer, tokenData)=>{\n        // Create a unique operation key to prevent duplicates\n        const operationKey = \"\".concat(tokenAddress, \"-\").concat(ownerAddress, \"-\").concat(JSON.stringify(tokenData.selectedClaims));\n        // Check if this operation is already in progress\n        if (operationCache.has(operationKey)) {\n            console.log(\"🔄 ERC-3643 compliance setup already in progress, returning cached promise\");\n            return operationCache.get(operationKey);\n        }\n        console.log(\"🏛️ Setting up ERC-3643 compliance for token:\", tokenAddress);\n        const results = {\n            identityRegistered: false,\n            whitelisted: false,\n            kycApproved: false,\n            claimsIssued: [],\n            complianceNotified: false,\n            errors: []\n        };\n        // Create the operation promise\n        const operationPromise = (async ()=>{\n            try {\n                // Get contract addresses from environment\n                const identityRegistryAddress = \"0x129E04323E4c9bFBD097489473d8523E4015Bfc3\";\n                const claimRegistryAddress = \"******************************************\";\n                const complianceAddress = \"******************************************\";\n                if (!identityRegistryAddress || !claimRegistryAddress || !complianceAddress) {\n                    console.warn(\"⚠️ ERC-3643 contract addresses not configured, skipping compliance setup\");\n                    return results;\n                }\n                // Connect to contracts\n                const identityRegistry = new ethers__WEBPACK_IMPORTED_MODULE_0__.Contract(identityRegistryAddress, IdentityRegistryABI, signer);\n                const claimRegistry = new ethers__WEBPACK_IMPORTED_MODULE_0__.Contract(claimRegistryAddress, ClaimRegistryABI, signer);\n                const compliance = new ethers__WEBPACK_IMPORTED_MODULE_0__.Contract(complianceAddress, ComplianceABI, signer);\n                // Pre-check: Get current status to avoid unnecessary transactions\n                console.log(\"🔍 Checking current compliance status...\");\n                const [isVerified, isWhitelisted, isKycApproved] = await Promise.all([\n                    identityRegistry.isVerified(ownerAddress).catch(()=>false),\n                    identityRegistry.isWhitelisted(ownerAddress).catch(()=>false),\n                    identityRegistry.isKycApproved(ownerAddress).catch(()=>false)\n                ]);\n                console.log(\"📊 Current status:\", {\n                    isVerified,\n                    isWhitelisted,\n                    isKycApproved\n                });\n                // Step 1: Register identity if not already registered\n                try {\n                    if (!isVerified) {\n                        console.log(\"📝 Registering identity for token owner...\");\n                        const countryCode = getCountryCode(tokenData.country || 'US');\n                        const tx1 = await identityRegistry.registerIdentity(ownerAddress, countryCode);\n                        await tx1.wait();\n                        results.identityRegistered = true;\n                        console.log(\"✅ Identity registered successfully\");\n                    } else {\n                        console.log(\"✅ Identity already registered\");\n                        results.identityRegistered = true;\n                    }\n                } catch (error) {\n                    console.error(\"❌ Failed to register identity:\", error);\n                    results.errors.push(\"Identity registration failed: \".concat(error.message));\n                }\n                // Step 2: Add to whitelist if not already whitelisted\n                try {\n                    if (!isWhitelisted) {\n                        console.log(\"📋 Adding to whitelist...\");\n                        const tx2 = await identityRegistry.addToWhitelist(ownerAddress);\n                        await tx2.wait();\n                        results.whitelisted = true;\n                        console.log(\"✅ Added to whitelist successfully\");\n                    } else {\n                        console.log(\"✅ Already whitelisted\");\n                        results.whitelisted = true;\n                    }\n                } catch (error) {\n                    console.error(\"❌ Failed to add to whitelist:\", error);\n                    results.errors.push(\"Whitelist addition failed: \".concat(error.message));\n                }\n                // Step 3: Approve KYC if not already approved\n                try {\n                    if (!isKycApproved) {\n                        console.log(\"🔍 Approving KYC...\");\n                        const tx3 = await identityRegistry.approveKyc(ownerAddress);\n                        await tx3.wait();\n                        results.kycApproved = true;\n                        console.log(\"✅ KYC approved successfully\");\n                    } else {\n                        console.log(\"✅ KYC already approved\");\n                        results.kycApproved = true;\n                    }\n                } catch (error) {\n                    console.error(\"❌ Failed to approve KYC:\", error);\n                    results.errors.push(\"KYC approval failed: \".concat(error.message));\n                }\n                // Step 4: Issue selected Tokeny-style claims for token issuer\n                try {\n                    const selectedClaims = tokenData.selectedClaims || [\n                        'KYC',\n                        'QUALIFICATION',\n                        'TOKEN_ISSUER'\n                    ];\n                    console.log(\"📜 Issuing selected Tokeny-style claims:\", selectedClaims);\n                    // Pre-check existing claims to avoid duplicates\n                    const existingClaims = new Map();\n                    for (const claimType of selectedClaims){\n                        const claimTopic = CLAIM_TOPICS[claimType];\n                        if (claimTopic) {\n                            try {\n                                const hasExistingClaim = await claimRegistry.hasValidClaim(ownerAddress, claimTopic);\n                                existingClaims.set(claimType, hasExistingClaim);\n                            } catch (error) {\n                                existingClaims.set(claimType, false);\n                            }\n                        }\n                    }\n                    console.log(\"📊 Existing claims status:\", Object.fromEntries(existingClaims));\n                    // Issue claims based on user selection\n                    for (const claimType of selectedClaims){\n                        try {\n                            const claimValue = generateTokenyClaim(tokenData.country || 'US', claimType);\n                            const claimTopic = CLAIM_TOPICS[claimType];\n                            if (!claimTopic) {\n                                console.warn(\"⚠️ Unknown claim type: \".concat(claimType));\n                                continue;\n                            }\n                            console.log(\"\\uD83D\\uDD22 Generated \".concat(claimType, \" claim: \").concat(claimValue));\n                            // Check if claim already exists\n                            const hasExistingClaim = await claimRegistry.hasValidClaim(ownerAddress, claimTopic);\n                            if (hasExistingClaim) {\n                                console.log(\"✅ \".concat(claimType, \" claim already exists\"));\n                                continue;\n                            }\n                            // Prepare claim data based on type\n                            let claimData;\n                            let claimUri;\n                            switch(claimType){\n                                case 'KYC':\n                                    claimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                                        \"string\",\n                                        \"string\",\n                                        \"uint256\"\n                                    ], [\n                                        claimValue,\n                                        \"KYC_APPROVED\",\n                                        Math.floor(Date.now() / 1000)\n                                    ]);\n                                    claimUri = \"KYC:\".concat(claimValue);\n                                    break;\n                                case 'AML':\n                                    claimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                                        \"string\",\n                                        \"string\",\n                                        \"uint256\"\n                                    ], [\n                                        claimValue,\n                                        \"AML_VERIFIED\",\n                                        Math.floor(Date.now() / 1000)\n                                    ]);\n                                    claimUri = \"AML:\".concat(claimValue);\n                                    break;\n                                case 'IDENTITY':\n                                    claimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                                        \"string\",\n                                        \"string\",\n                                        \"uint256\"\n                                    ], [\n                                        claimValue,\n                                        \"IDENTITY_VERIFIED\",\n                                        Math.floor(Date.now() / 1000)\n                                    ]);\n                                    claimUri = \"IDENTITY:\".concat(claimValue);\n                                    break;\n                                case 'QUALIFICATION':\n                                    claimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                                        \"string\",\n                                        \"string\",\n                                        \"uint256\"\n                                    ], [\n                                        claimValue,\n                                        \"QUALIFIED_INVESTOR\",\n                                        Math.floor(Date.now() / 1000)\n                                    ]);\n                                    claimUri = \"QUALIFICATION:\".concat(claimValue);\n                                    break;\n                                case 'ACCREDITATION':\n                                    claimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                                        \"string\",\n                                        \"string\",\n                                        \"uint256\"\n                                    ], [\n                                        claimValue,\n                                        \"ACCREDITED_INVESTOR\",\n                                        Math.floor(Date.now() / 1000)\n                                    ]);\n                                    claimUri = \"ACCREDITATION:\".concat(claimValue);\n                                    break;\n                                case 'RESIDENCE':\n                                    claimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                                        \"string\",\n                                        \"string\",\n                                        \"uint256\"\n                                    ], [\n                                        claimValue,\n                                        \"RESIDENCE_VERIFIED\",\n                                        Math.floor(Date.now() / 1000)\n                                    ]);\n                                    claimUri = \"RESIDENCE:\".concat(claimValue);\n                                    break;\n                                case 'TOKEN_ISSUER':\n                                    claimData = ethers__WEBPACK_IMPORTED_MODULE_1__.AbiCoder.defaultAbiCoder().encode([\n                                        \"string\",\n                                        \"string\",\n                                        \"string\",\n                                        \"string\",\n                                        \"uint256\"\n                                    ], [\n                                        claimValue,\n                                        tokenData.name,\n                                        tokenData.symbol,\n                                        tokenData.tokenType,\n                                        Math.floor(Date.now() / 1000)\n                                    ]);\n                                    claimUri = \"TOKEN_ISSUER:\".concat(claimValue, \":\").concat(tokenData.symbol);\n                                    break;\n                                default:\n                                    console.warn(\"⚠️ Unsupported claim type: \".concat(claimType));\n                                    continue;\n                            }\n                            // Issue the claim\n                            const tx = await claimRegistry.issueClaim(ownerAddress, claimTopic, \"0x\", claimData, claimUri, 0 // never expires\n                            );\n                            await tx.wait();\n                            results.claimsIssued.push(\"\".concat(claimType, \":\").concat(claimValue));\n                            console.log(\"✅ \".concat(claimType, \" claim issued: \").concat(claimValue));\n                        } catch (claimError) {\n                            console.error(\"❌ Failed to issue \".concat(claimType, \" claim:\"), claimError);\n                            results.errors.push(\"\".concat(claimType, \" claim issuance failed: \").concat(claimError.message));\n                        }\n                    }\n                    console.log(\"\\uD83C\\uDF89 Claims issuance completed! Issued \".concat(results.claimsIssued.length, \" claims\"));\n                } catch (error) {\n                    console.error(\"❌ Failed to issue claims:\", error);\n                    results.errors.push(\"Claims issuance failed: \".concat(error.message));\n                }\n                // Step 5: Notify compliance contract (if needed)\n                try {\n                    console.log(\"⚖️ Notifying compliance contract...\");\n                    // This would typically be called when tokens are minted, but we can prepare it\n                    const canTransfer = await compliance.canTransfer(ownerAddress, ownerAddress, 1);\n                    console.log(\"✅ Compliance check passed:\", canTransfer);\n                    results.complianceNotified = true;\n                } catch (error) {\n                    console.error(\"❌ Compliance notification failed:\", error);\n                    results.errors.push(\"Compliance notification failed: \".concat(error.message));\n                }\n                console.log(\"🎉 ERC-3643 compliance setup completed!\");\n                console.log(\"Results:\", {\n                    identityRegistered: results.identityRegistered,\n                    whitelisted: results.whitelisted,\n                    kycApproved: results.kycApproved,\n                    claimsIssued: results.claimsIssued,\n                    complianceNotified: results.complianceNotified,\n                    errorCount: results.errors.length\n                });\n            } catch (error) {\n                console.error(\"❌ ERC-3643 setup failed:\", error);\n                results.errors.push(\"General setup failed: \".concat(error.message));\n            } finally{\n                // Clean up cache entry\n                operationCache.delete(operationKey);\n            }\n            return results;\n        })();\n        // Cache the operation promise\n        operationCache.set(operationKey, operationPromise);\n        return operationPromise;\n    };\n    /**\n   * Check if ERC-3643 contracts are available\n   */ const isERC3643Available = ()=>{\n        return !!( true && \"******************************************\");\n    };\n    /**\n   * Get ERC-3643 contract addresses\n   */ const getERC3643Addresses = ()=>{\n        return {\n            identityRegistry: \"0x129E04323E4c9bFBD097489473d8523E4015Bfc3\",\n            claimRegistry: \"******************************************\",\n            compliance: \"******************************************\"\n        };\n    };\n    return {\n        setupERC3643Compliance,\n        isERC3643Available,\n        getERC3643Addresses\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/create-token/hooks/useERC3643Integration.ts\n"));

/***/ })

});